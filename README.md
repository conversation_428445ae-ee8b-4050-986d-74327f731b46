# bilibili bvc python common module - pyutils

## 项目说明

BVC Python 项目通用工具集合

包括 Databus、异步任务框架、服务发现 等常用工具

## 项目使用

可通过 git submodule 形式引入到自己项目中，主要代码在 src 目录下
```bash
git <NAME_EMAIL>:bvc-pycommon/pyutils.git
```

所需依赖包在 `requirements.txt` 中，部分依赖必须用基架 pypi 才能安装
``` bash 
# 进入 pyutils 目录下，执行命令安装依赖
pip3 install -r requirements.txt -i https://pypi.bilibili.co/repository/pypi-public/simple/
```

执行测试
``` bash 
make test
```

执行验证（针对 API）
``` bash 
make verify
```
