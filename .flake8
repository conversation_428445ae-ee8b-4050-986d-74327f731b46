[flake8]
max-line-length = 120
max-complexity = 12
format = pylint
show_source = True
statistics = True
count = True
ignore =
    # E266 too many leading '#' for block comment
    E266,
    # imported but unused
    F401
    # no newline at end of file
    W292,
    # local variable 'mre' is assigned to but never used
    F841
    # line break before binary operator
    W503
    # line break after binary operator
    W504
exclude =
    *migrations*
# python related
    *.pyc
    .tox
    .git
    __pycache__
    settings
    venv
    env
# test related
    tests
    verifys
# grpc related
    *_pb2.py
    *_pb2.pyi
    *_pb2_grpc.py
