FROM hub.bilibili.co/maxinjun/bilivqa_v2:v20241225

ENV pypi_host pypi.douban.com/simple
ENV pypi_source "https://${pypi_host}"
ENV TZ=Asia/Shanghai

ARG app_name="archive-qoe"
ADD ./assets /data/app/${app_name}/assets
ADD ./source /data/app/${app_name}/source
ADD ./conf /data/app/${app_name}/conf
ADD ./README.md /data/app/${app_name}/README.md
ADD ./scripts /data/app/${app_name}/scripts
ADD ./model /data/app/${app_name}/model

WORKDIR /data/app/${app_name}

RUN mkdir /data/app/${app_name}/model/images
RUN mkdir /data/app/${app_name}/model/images/test

WORKDIR /data/app/${app_name}/source

RUN curl -sf "http://upos-sz-staticcos.bilivideo.com/bvcstaticboss/upclone/upclone-install.sh" | bash -s v2.0.20
RUN pip3 install -r /data/app/${app_name}/scripts/internals/requirements.txt
RUN pip3 install -r /data/app/${app_name}/source/utils/pyutils/requirements.txt -i https://pypi.bilibili.co/repository/pypi-public/simple/
RUN pip3 install pynvml

ENV PATH=/usr/lib/linux-tools/4.15.0-213-generic:${PATH}

RUN chown root:root -R /data/app/${app_name}/assets/keys/* && \
	chmod 400 -R /data/app/${app_name}/assets/keys/* && \
	mkdir -p /data/bili_vxcode_workspace && \
	chmod 777 -R /data/bili_vxcode_workspace && \
	python3.7 -m compileall -b . && \
    find . -name '__pycache__' -type d -delete



ENV PYTHONPATH=/data/app/${app_name}/source
ENV NVIDIA_DRIVER_CAPABILITIES=all
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/workspace/TensorRT-8.2.5.1_cu114/lib
ENV APP_NAME=${app_name}
