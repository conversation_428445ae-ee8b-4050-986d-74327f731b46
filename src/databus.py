"""
ref: https://info.bilibili.co/display/documentation/databus+python+SDK
require: redis
usage:

# consumer
consumer = Consumer(...)
for messages in consumer.subscribe():
    # messages: [message1, message2, ...]
    handle_messages(messages)

# producer
producer = Producer(...)
producer.publish({'key1': 'value1', 'key2': 'value2'})
"""
import collections
import itertools
import json
import logging
import time
import uuid
from typing import List

import redis

from .common import chunked
from .common import register_term_signal
from .retry import retry


class _Client:
    def __init__(
            self, host, port, key, secret, topic, group, role, *, color=None, **kwargs,
    ):
        auth = f'{key}:{secret}@{group}/topic={topic}&role={role}'
        if color is not None:
            auth = auth + f'&color={color}'
        self._auth = auth
        self._group = group

        socket_timeout = kwargs.get('socket_timeout')
        max_connections = kwargs.get('max_connections')

        self._connection = redis.Redis(
            host=host,
            port=port,
            socket_connect_timeout=1,
            socket_timeout=socket_timeout,
            max_connections=max_connections,
        )
        cd = 10
        self._cool_down_default = cd
        self._cool_down = cd

        self.reconnect()

    def reconnect(self):
        group = self._group
        try:
            self._connection.execute_command('auth', self._auth)
            logging.info(f'[pyutils] databus client reconnect success: group<{group}>')
        except Exception as e:
            logging.exception(e)
            logging.info(f'[pyutils] databus client reconnect fail: group<{group}> error<{e}>')

    def _check_heartbeat(self, items):
        now = int(time.time())
        for item in items:
            p, ts = item['partition'], item['timestamp']
            latency = now - ts
            item['latency'] = latency

        if len(items) == 0:
            self._cool_down -= 1
        else:
            self._cool_down = self._cool_down_default

        if self._cool_down < 0:
            cd = self._cool_down_default
            self._cool_down = cd
            group = self._group
            raise RuntimeError(f'databus client pull empty messages too many times: group<{group}> cd<{cd}>')

    def mget(self):
        try:
            ms = self._connection.mget('json')
            items = [json.loads(i) for i in ms]
            self._check_heartbeat(items)
            return items
        except Exception as e:
            logging.exception(e)
            self.reconnect()
            return []

    @retry(exceptions=(Exception,), delay=0.1, tries=5)
    def set(self, key: str, value):
        try:
            self._connection.set(key, value)
        except Exception as e:
            logging.exception(e)
            self.reconnect()
            raise


class Consumer:
    def __init__(self, host: str, port: int, key: str, secret: str, topic: str, group: str, *, color=None):
        self.group = group
        self.client = _Client(
            host=host, port=port, key=key, secret=secret,
            topic=topic, group=group,
            color=color,
            role='sub',
            socket_timeout=60,
            max_connections=1,
        )
        self.running = False
        register_term_signal(self.exit_signal_handler)

    def subscribe(self, chunk_size=None):
        group = self.group
        logging.info(f'[pyutils] databus consumer subscribe start: group<{group}>')

        if chunk_size is None:
            chunk_size = 1

        client = self.client
        self.running = True
        # for graceful close
        while self.running:
            messages = client.mget()
            if len(messages) == 0:
                sleep_time = 5
                logging.info(
                    f'[pyutils] databus consumer receive empty messages, so sleeping in {sleep_time}s: group<{group}>'
                )
                # sleep for a while when no message
                time.sleep(sleep_time)
                continue
            else:
                logging.info(f'[pyutils] databus consumer receive: group<{group}> messages<{messages}>')

                offsets = collections.defaultdict(lambda: 0)
                for msgs in chunked(messages, chunk_size):
                    values = []
                    for msg in msgs:
                        p, o = msg['partition'], msg['offset']
                        if o > offsets[p]:
                            offsets[p] = o

                        v = msg['value']
                        if v is not None:
                            values.append(v)
                        else:
                            logging.warning(
                                f'[pyutils] databus consumer receive none value: group<{group}> message<{msg}>'
                            )

                        latency = msg['latency']
                        logging.info(
                            f'[pyutils] databus consumer latency: group<{group}> partition<{p}> latency<{latency}>',
                            extra=dict(databus_latency=latency),
                        )

                    yield values

                for p, o in offsets.items():
                    client.set(p, o)
                    logging.info(f'[pyutils] databus consumer offset: group<{group}> partition<{p}> offset<{o}>')

                logging.info(f'[pyutils] databus consumer commit: group<{group}> messages<{messages}>')

        logging.info(f'[pyutils] databus consumer subscribe finish: group<{group}>')

    def exit_signal_handler(self, sig, frame):
        """ graceful close """
        group = self.group

        logging.warning(f'[pyutils] databus consumer caught signal: group<{group}> signal<{sig}> frame<{frame}>')
        self.running = False
        logging.info(f'[pyutils] databus consumer stop: group<{group}>')


class Producer:
    def __init__(self, host: str, port: int, key: str, secret: str, topic: str, group: str, *, color=None):
        self.group = group
        self.client = _Client(
            host=host, port=port, key=key, secret=secret,
            topic=topic, group=group,
            color=color,
            role='pub',
            socket_timeout=3,
            max_connections=30,
        )

    def publish(self, value: dict, key: str = None):
        group = self.group
        if key is None:
            key = uuid.uuid4().hex
        assert isinstance(value, dict)
        self.client.set(key, json.dumps(value))
        logging.info(f'[pyutils] databus producer publish: group<{group}> key<{key}> value<{value}>')

    def batch_publish(self, values: List[dict], keys: List[str] = None):
        if len(values) == 0:
            return False

        keys = keys or []
        assert len(values) >= len(keys)
        for key, value in itertools.zip_longest(keys, values):
            self.publish(value=value, key=key)
        return True
