"""
默认从环境变量读取服务发现配置
如果环境配置缺失导致发现失败，或者需要手动指定环境
可预先手动调用 discovery_init 方法进行初始化

实际使用时，优先考虑 discovery_fetch 方法
部分参数说明：
    scheme
        - http
        - grpc
    cluster
        可用于未开启「分区注册」的应用集群筛选子应用
        此时，discovery_id 填应用集群；cluster 填子应用名
"""
import dataclasses
import itertools
import logging
import random
from typing import Any
from typing import Callable
from typing import List
from typing import Optional

from discovery import config_from_env
from discovery.client import Client as DiscoveryClient

__all__ = [
    'Instance',

    'discovery_init',
    'discovery_fetch',
    'discovery_watch',
]


_dis_cli: Optional[DiscoveryClient] = None
_default_zone: Optional[str] = None


@dataclasses.dataclass
class Instance:
    region: str
    zone: str
    env: str
    appid: str
    hostname: str
    metadata: dict
    # 例如: ['grpc://*************:6077', 'http://*************:80']
    addrs: List[str]

    @property
    def grpc_host(self):
        for addr in self.addrs:
            if addr.startswith('grpc://'):
                return addr

    @property
    def http_host(self):
        for addr in self.addrs:
            if addr.startswith('http://'):
                return addr


def _random_pick(data, *, scheme: str = None, cluster: str = None, zone: str = None):
    zone = zone or _default_zone

    # filter
    filtered_data = {}
    for key, values in data.items():
        instances = []
        for value in values:
            ins = Instance(
                region=value['region'],
                zone=value['zone'],
                env=value['env'],
                appid=value['appid'],
                hostname=value['hostname'],
                metadata=value.get('metadata') or {},
                addrs=value['addrs'],
            )

            if scheme == 'grpc' and (not ins.grpc_host):
                continue
            if scheme == 'http' and (not ins.http_host):
                continue
            if cluster and (ins.metadata.get('cluster') != cluster):
                continue

            instances.append(ins)

        if instances:
            filtered_data[key] = instances

    # random
    # 优先选取指定 zone 的实例，找不到时从其他可用 zone 随机选取
    if filtered_data.get(zone):
        instances = filtered_data[zone]
    else:
        instances = list(itertools.chain(*filtered_data.values()))

    if instances:
        ins: Instance = random.choice(instances)
        return ins
    return None


def discovery_init(**kwargs):
    global _dis_cli, _default_zone
    if _dis_cli is None:
        config = config_from_env(**kwargs)
        _dis_cli = DiscoveryClient(config)
        _default_zone = config.zone
        return True
    else:
        logging.warning(
            f'[pyutils] discovery client already initialized, not take effect: kwargs<{kwargs}>'
        )
        return False


def _ensure_discovery_client():
    if _dis_cli is None:
        discovery_init()


def discovery_fetch(discovery_id: str, *, scheme: str = None, cluster: str = None, zone: str = None):
    _ensure_discovery_client()
    data = _dis_cli.fetch(discovery_id)
    ins = _random_pick(data, scheme=scheme, cluster=cluster, zone=zone)
    if not ins:
        logging.warning(
            f'[pyutils] discovery fetch pick no instance: '
            f'discovery_id<{discovery_id}> scheme<{scheme}> cluster<{cluster}> zone<{zone}> '
            f'data<{data}> '
        )
    return ins


def discovery_watch(
        discovery_id: str, callback: Callable[[Instance], Any], *,
        scheme: str = None, cluster: str = None, zone: str = None,
):
    _ensure_discovery_client()

    def callback_wrapper(data):
        ins = _random_pick(data, scheme=scheme, cluster=cluster, zone=zone)
        if not ins:
            logging.warning(
                f'[pyutils] discovery watch pick no instance: '
                f'discovery_id<{discovery_id}> scheme<{scheme}> cluster<{cluster}> zone<{zone}> '
                f'data<{data}> '
            )
        callback(ins)
    _dis_cli.watch(discovery_id, callback_wrapper)
