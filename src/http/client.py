import copy
import dataclasses
import json
import logging
import os
import shlex
import time
from typing import <PERSON>ple
from typing import Union

import requests

from ..common import NoneFilterDict
from ..discovery import discovery_fetch
from ..errors import HttpDiscoveryError
from ..errors import HttpRequestError


def http_path_cleaned(path: str):
    p = path.lstrip('/')
    return f'/{p}'


def request_to_curl(request, compressed=False, verify=True):
    """
    Returns string with curl command by provided request object

    Parameters
    ----------
    compressed : bool
        If `True` then `--compressed` argument will be added to result
    """
    parts = [
        ('curl', None),
        ('-X', request.method),
    ]

    for k, v in sorted(request.headers.items()):
        parts += [('-H', '{0}: {1}'.format(k, v))]

    if request.body:
        body = request.body
        if isinstance(body, bytes):
            body = body.decode('utf-8')
        parts += [('-d', body)]

    if compressed:
        parts += [('--compressed', None)]

    if not verify:
        parts += [('--insecure', None)]

    parts += [(None, request.url)]

    flat_parts = []
    for k, v in parts:
        if k:
            flat_parts.append(shlex.quote(k))
        if v:
            flat_parts.append(shlex.quote(v))

    return ' '.join(flat_parts)


def log_as_curl(method, url, **kwargs):
    req = requests.Request(method=method, url=url, **kwargs).prepare()
    cmd = request_to_curl(request=req)
    logging.info(f'[pyutils] http client request to curl: <{cmd}>')


def try_json_decode(text: str):
    try:
        r = json.loads(text)
    except json.JSONDecodeError:
        return text
    else:
        return r


@dataclasses.dataclass
class RequestParam:
    headers: dict = None
    auth: Tuple[str, str] = None
    query_params: dict = None
    json_params: dict = None
    form_params: dict = None

    def filter_none(self):
        for k, v in self.__dict__.items():
            if isinstance(v, dict):
                self.__dict__[k] = NoneFilterDict(v)

    def dict(self):
        d = {}
        for k, v in self.__dict__.items():
            if v is not None:
                d[k] = v
        return d

    def dict_for_request(self):
        return dict(
            headers=self.headers,
            auth=self.auth,
            params=self.query_params,
            json=self.json_params,
            data=self.form_params,
        )


@dataclasses.dataclass(frozen=True)
class HttpConfig:
    timeout: float = 0.5
    # codes not raised
    allowed_status_codes: Tuple[int, ...] = ()
    # retry config
    retry_times: int = 3
    retry_interval: float = 0.1
    # discovery config
    discovery_cluster: str = None
    discovery_zone: str = None


class HttpClient:
    def __init__(self, host_or_discovery_id: str, *, config: HttpConfig = None):
        """
        host_or_discovery_id
            - 若以 http:// 或 https:// 开头，则当作 host 进行请求
            - 否则当作 discovery_id，通过服务发现，获取实际 host 后再请求
        """
        self._host_or_discovery_id = host_or_discovery_id
        self._config = config or HttpConfig()

        caller = os.getenv('APP_ID', 'unknown')
        zone = os.getenv('ZONE', 'unknown')
        self._meta_headers = {
            'x-bili-metadata-caller': caller,
            'x-bili-metadata-zone': zone,
            # old rule
            'x1-bilispy-user': caller,
            'x1-bilispy-zone': zone,
        }

    @property
    def host(self):
        s = self._host_or_discovery_id
        if s.startswith('http://') or s.startswith('https://'):
            return s.rstrip('/')
        else:
            discovery_id = self._host_or_discovery_id
            cluster = self._config.discovery_cluster
            zone = self._config.discovery_zone
            ins = discovery_fetch(discovery_id, scheme='http', cluster=cluster, zone=zone)
            if not ins:
                raise HttpDiscoveryError(discovery_id=discovery_id, cluster=cluster)
            return ins.http_host

    def with_config(self, **kwargs):
        other = copy.copy(self)
        other._config = dataclasses.replace(self._config, **kwargs)
        return other

    def request(
            self, method: str, path: str, *,
            headers: dict = None, auth: Tuple[str, str] = None,
            query_params: dict = None, json_params: Union[dict, list] = None, form_params: dict = None,
    ):
        method = method.upper()

        hs = {}
        hs.update(self._meta_headers)
        hs.update(headers or {})

        param = RequestParam(
            headers=hs, auth=auth,
            query_params=query_params, json_params=json_params, form_params=form_params,
        )
        param.filter_none()

        host = self.host
        path = http_path_cleaned(path)
        url = f'{host}{path}'
        log_as_curl(method=method, url=url, **param.dict_for_request())

        c = self._config
        timeout = c.timeout
        retry_times, retry_interval = c.retry_times, c.retry_interval
        allowed_status_codes = c.allowed_status_codes

        while True:
            status_code = -1
            try:
                resp = requests.request(method=method, url=url, **param.dict_for_request(), timeout=timeout)
                status_code = resp.status_code
                if (not resp.ok) and (status_code not in allowed_status_codes):
                    resp.raise_for_status()

                if method == 'HEAD':
                    rs = dict(resp.headers)
                    rs['status_code'] = status_code
                    return rs

                if resp.headers['Content-Type'] == 'application/json':
                    return resp.json()
                return try_json_decode(resp.text)

            except requests.exceptions.RequestException as e:
                if retry_times <= 0:
                    raise HttpRequestError(
                        host=host, method=method, path=path, message=str(e), status_code=status_code, **param.dict(),
                    )
                logging.warning(
                    '[pyutils] http client retrying in <%s> seconds: \nhost<%s> method<%s> path<%s> \nerror<%s>',
                    retry_interval, host, method, path, e,
                )
                retry_times -= 1
                time.sleep(retry_interval)
