import asyncio
import logging
from typing import Tuple

import aiohttp
import aiohttp.client_exceptions
from aiohttp import ClientTimeout

from .client import HttpClient
from .client import RequestParam
from .client import http_path_cleaned
from .client import log_as_curl
from .client import try_json_decode
from ..errors import HttpRequestError


class HttpAioClient(HttpClient):
    async def request(
            self, method: str, path: str, *,
            headers: dict = None, auth: Tuple[str, str] = None,
            query_params: dict = None, json_params: dict = None, form_params: dict = None,
    ):
        method = method.upper()

        hs = {}
        hs.update(self._meta_headers)
        hs.update(headers or {})

        param = RequestParam(
            headers=hs, auth=auth,
            query_params=query_params, json_params=json_params, form_params=form_params,
        )
        param.filter_none()

        host = self.host
        path = http_path_cleaned(path)
        url = f'{host}{path}'
        log_as_curl(method=method, url=url, **param.dict_for_request())

        c = self._config
        timeout = ClientTimeout(total=c.timeout)
        retry_times, retry_interval = c.retry_times, c.retry_interval
        allowed_status_codes = c.allowed_status_codes

        while True:
            status_code = -1
            try:
                async with aiohttp.request(method=method, url=url, **param.dict_for_request(), timeout=timeout) as resp:
                    status_code = resp.status
                    if (not resp.ok) and (status_code not in allowed_status_codes):
                        resp.raise_for_status()

                    if method == 'HEAD':
                        rs = dict(resp.headers)
                        rs['status_code'] = status_code
                        return rs

                    encoding = 'utf-8'
                    if resp.headers['Content-Type'] == 'application/json':
                        return await resp.json(encoding=encoding)

                    text = await resp.text(encoding=encoding)
                    return try_json_decode(text)

            except (aiohttp.client_exceptions.ClientError, asyncio.TimeoutError) as e:
                if retry_times <= 0:
                    raise HttpRequestError(
                        host=host, method=method, path=path, message=str(e), status_code=status_code, **param.dict(),
                    )
                logging.warning(
                    '[pyutils] http aio client retrying in <%s> seconds: \nhost<%s> method<%s> path<%s> \nerror<%s>',
                    retry_interval, host, method, path, e,
                )
                retry_times -= 1
                await asyncio.sleep(retry_interval)
