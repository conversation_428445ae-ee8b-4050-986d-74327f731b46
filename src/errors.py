class PyUtilError(Exception):
    code = 1001

    def __init__(self, *, message: str = '', **kwargs):
        super().__init__()
        self.name = self.__class__.__name__
        self.message = message
        self._kwargs = kwargs

    @property
    def extra_info(self):
        ignore_fields = ['code', 'name', 'message']
        d = {}
        for k, v in self.__dict__.items():
            if (not k.startswith('_')) and (k not in ignore_fields):
                d[k] = v
        d.update(self._kwargs)
        return d

    def __str__(self):
        name, code, message, extra_info = self.name, self.code, self.message, self.extra_info
        s = f'<{name}>: code<{code}> message<{message}> extra_info<{extra_info}>'
        return s


# 2001 <= code <= 2999
class PyUtilHttpError(PyUtilError):
    code = 2001


class HttpDiscoveryError(PyUtilHttpError):
    code = 2002


class HttpRequestError(PyUtilHttpError):
    code = 2003


# 3001 <= code <= 3999
class PyUtilApiError(PyUtilError):
    code = 3001


# 3101 <= code <= 3199
class RawVideoError(PyUtilApiError):
    code = 3101


class RawVideoWaitTimeoutError(PyUtilApiError):
    code = 3102


# 3201 <= code <= 3299
class BvcflowError(PyUtilApiError):
    code = 3201


class BvcflowFopError(BvcflowError):
    code = 3211


class BvcflowFopWaitTimeoutError(BvcflowFopError):
    code = 3212


# 3301 <= code <= 3399
class BiliMainError(PyUtilApiError):
    code = 3301


# 3401 <= code <= 3499
class DataSourceError(PyUtilApiError):
    code = 3401


# 3501 <= code <= 3599
class TriggerError(PyUtilApiError):
    code = 3501


# 3601 <= code <= 3699
class WeWorkBotError(PyUtilApiError):
    code = 3601


# 4001 <= code <= 4999
class PyUtilGrpcError(PyUtilError):
    code = 4001


class GrpcDiscoveryError(PyUtilGrpcError):
    code = 4002


class GrpcRequestError(PyUtilGrpcError):
    code = 4003


# 4101 <= code <= 4199
class TaishanError(PyUtilGrpcError):
    code = 4101
