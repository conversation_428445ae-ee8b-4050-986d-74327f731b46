import asyncio
import logging

import grpc as _grpc
import grpc.aio as _grpc_aio
from google.protobuf.message import Message

from .client import GrpcClient
from ..errors import GrpcRequestError


class GrpcAioClient(GrpcClient):
    async def request(self, stub_class: type, method: str, request: Message):
        host = self.host
        metadata = self._metadata

        c = self._config
        timeout = c.timeout
        retry_times, retry_interval = c.retry_times, c.retry_interval

        async with _grpc_aio.insecure_channel(host) as channel:
            stub = stub_class(channel)
            f = getattr(stub, method)
            while True:
                try:
                    return await f(request, timeout=timeout, metadata=metadata)
                except _grpc.RpcError as e:
                    status_code = e.code()
                    if retry_times <= 0:
                        raise GrpcRequestError(
                            host=host, method=method, request=str(request), message=str(e), status_code=status_code,
                        )
                    logging.warning(
                        '[pyutils] grpc aio client retrying in <%s> seconds: \n'
                        'host<%s> method<%s> request<%s> \nerror<%s>',
                        retry_interval, host, method, request, e,
                    )
                    retry_times -= 1
                    await asyncio.sleep(retry_interval)
