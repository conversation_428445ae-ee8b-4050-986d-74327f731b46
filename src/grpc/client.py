import copy
import dataclasses
import logging
import os
import time

import grpc as _grpc
from google.protobuf.message import Message

from ..discovery import discovery_fetch
from ..errors import GrpcDiscoveryError
from ..errors import GrpcRequestError


@dataclasses.dataclass(frozen=True)
class GrpcConfig:
    timeout: float = 0.5
    # retry config
    retry_times: int = 3
    retry_interval: float = 0.1
    # discovery config
    discovery_cluster: str = None
    discovery_zone: str = None


class GrpcClient:
    def __init__(self, host_or_discovery_id: str, *, config: GrpcConfig = None):
        """
        host_or_discovery_id
            - 若以 grpc:// 开头，则当作 host 直接请求
            - 否则当作 discovery_id，通过服务发现，获取实际 host 后再请求
        """
        self._host_or_discovery_id = host_or_discovery_id
        self._config = config or GrpcConfig()
        self._metadata = (
            ('caller', os.getenv('APP_ID', 'unknown')),
            ('zone', os.getenv('ZONE', 'unknown')),
        )

    @property
    def host(self):
        s = self._host_or_discovery_id
        if s.startswith('grpc://'):
            host = s
        else:
            discovery_id = self._host_or_discovery_id
            cluster = self._config.discovery_cluster
            zone = self._config.discovery_zone
            ins = discovery_fetch(discovery_id, scheme='grpc', cluster=cluster)
            if not ins:
                raise GrpcDiscoveryError(discovery_id=discovery_id, cluster=cluster)
            host = ins.grpc_host
        return host.replace('grpc://', '')

    def with_config(self, **kwargs):
        other = copy.copy(self)
        other._config = dataclasses.replace(self._config, **kwargs)
        return other

    def request(self, stub_class: type, method: str, request: Message):
        host = self.host
        metadata = self._metadata

        c = self._config
        timeout = c.timeout
        retry_times, retry_interval = c.retry_times, c.retry_interval

        with _grpc.insecure_channel(host) as channel:
            stub = stub_class(channel)
            f = getattr(stub, method)
            while True:
                try:
                    return f(request, timeout=timeout, metadata=metadata)
                except _grpc.RpcError as e:
                    status_code = e.code()
                    if retry_times <= 0:
                        raise GrpcRequestError(
                            host=host, method=method, request=str(request), message=str(e), status_code=status_code,
                        )
                    logging.warning(
                        '[pyutils] grpc client retrying in <%s> seconds: \n'
                        'host<%s> method<%s> request<%s> \nerror<%s>',
                        retry_interval, host, method, request, e,
                    )
                    retry_times -= 1
                    time.sleep(retry_interval)
