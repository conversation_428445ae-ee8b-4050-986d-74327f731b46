import asyncio
import functools
import logging
import time
from typing import Callable
from typing import TypeVar

from .common import _TypeHint

RT = TypeVar('RT')


def _retry_async(func: Callable[..., RT], exceptions, tries, delay, max_delay, backoff) -> Callable[..., RT]:
    @functools.wraps(func)
    async def wrapper(*args, **kw) -> RT:
        _tries, _delay = tries, delay
        while True:
            try:
                return await func(*args, **kw)
            except exceptions as e:
                logging.warning(
                    f'[pyutils] retry {func.__name__} retrying in %s seconds...\nerror(%s)\nargs(%s)\nkwargs(%s)',
                    _delay, repr(e), args, kw,
                )

                _tries -= 1
                if _tries < 0:
                    raise

                await asyncio.sleep(_delay)
                _delay *= backoff

                if max_delay is not None:
                    _delay = min(_delay, max_delay)
    return wrapper


def _retry_sync(func: Callable[..., RT], exceptions, tries, delay, max_delay, backoff) -> Callable[..., RT]:
    @functools.wraps(func)
    def wrapper(*args, **kw) -> RT:
        _tries, _delay = tries, delay
        while True:
            try:
                return func(*args, **kw)
            except exceptions as e:
                logging.warning(
                    f'[pyutils] retry {func.__name__} retrying in %s seconds...\nerror(%s)\nargs(%s)\nkwargs(%s)',
                    _delay, repr(e), args, kw,
                )

                _tries -= 1
                if _tries < 0:
                    raise

                time.sleep(_delay)
                _delay *= backoff

                if max_delay is not None:
                    _delay = min(_delay, max_delay)
    return wrapper


def retry(
        exceptions: _TypeHint.ExceptionClassOrTuple, *,
        tries: int = 3, delay: float = 0.1, max_delay: float = 10.0, backoff: float = 2.0,
) -> Callable[[Callable[..., RT]], Callable[..., RT]]:
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            return _retry_async(func, exceptions, tries=tries, delay=delay, max_delay=max_delay, backoff=backoff)
        else:
            return _retry_sync(func, exceptions, tries=tries, delay=delay, max_delay=max_delay, backoff=backoff)
    return decorator
