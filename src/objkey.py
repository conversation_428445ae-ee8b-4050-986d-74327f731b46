import os

from . import vod_format as vf


#
# main station resource
#

def objkey(cid: int, vp: int, format: int, *, ver: str = None):
    cid_filled = str(cid).rjust(4, '0')
    vfmt = vf.VodFormat.find_one(id=int(format))
    ext = vfmt.container
    if ver:
        ver = f'_{ver}'
    else:
        ver = ''
    return f'/{cid_filled[-2:]}/{cid_filled[-4:-2]}/{cid}/{cid}{ver}-{vp}-{format}.{ext}'


def uri_prefix(bucket: str, cid: int, *, ver: str = None):
    cid_filled = str(cid).rjust(4, '0')
    if ver:
        ver = f'_{ver}'
    else:
        ver = ''
    return f'upos://{bucket}/{cid_filled[-2:]}/{cid_filled[-4:-2]}/{cid}/{cid}{ver}'


def uri(bucket: str, cid: int, vp: int, format: int, *, ver: str = None):
    prefix = uri_prefix(bucket, cid, ver=ver)
    vfmt = vf.VodFormat.find_one(id=int(format))
    ext = vfmt.container
    return f'{prefix}-{vp}-{format}.{ext}'


def uri_parse(uri: str):
    """
    upos://upgcxcode/45/23/12345/12345_da3-1-16.mp4
    /45/23/12345/12345_da3-1-16.mp4
    """
    prefix, vp, fmt_ext = uri.split('-')
    fmt, ext = fmt_ext.split('.')

    name = os.path.basename(uri)
    cid_ver, *_ = name.split('-', 1)
    if '_' in cid_ver:
        cid, ver = cid_ver.split('_')
    else:
        cid, ver = cid_ver, ''

    return dict(
        prefix=prefix,
        cid=int(cid),
        ver=ver,
        vp=int(vp),
        format=int(fmt),
        ext=ext,
    )


#
# common business resource
# for example: iup 、manga...
#

def common_objkey(filename: str, vp: int, flag: str, *, ver: str = None):
    flag_data = common_flag_parse(flag)
    ext = flag_data['container']
    if ver:
        ver = f'_{ver}'
    else:
        ver = ''
    return f'/{filename[-2:]}/{filename[-4:-2]}/{filename}{ver}-{vp}-{flag}.{ext}'


def common_uri_prefix(bucket: str, filename: str, *, ver: str = None):
    if ver:
        ver = f'_{ver}'
    else:
        ver = ''
    return f'upos://{bucket}/{filename[-2:]}/{filename[-4:-2]}/{filename}{ver}'


def common_uri(bucket: str, filename: str, vp: int, flag: str, *, ver: str = None):
    prefix = common_uri_prefix(bucket, filename, ver=ver)
    flag_data = common_flag_parse(flag)
    ext = flag_data['container']
    return f'{prefix}-{vp}-{flag}.{ext}'


def common_uri_parse(uri: str):
    prefix, vp, flag_ext = uri.split('-')
    flag, ext = flag_ext.split('.')
    filename_ver = prefix.rsplit('/', 1)[1]

    if '_' in filename_ver:
        filename, ver = filename_ver.split('_')
    else:
        filename = filename_ver
        ver = ''

    return dict(
        prefix=prefix,
        filename=filename,
        vp=int(vp),
        ver=ver,
        flag=flag,
        ext=ext,
    )


# common objkey flag


class _CommonFlagModel:
    _business_map = (
        ('0', None),
        ('1', 'ugc'),
        ('2', 'pgc'),
        ('3', 'ott'),
        ('4', 'preview'),
        ('5', 'proj'),
    )

    _qn_map = (
        ('0', None),
        ('1', '16'),
        ('2', '29'),
        ('3', '32'),
        ('4', '64'),
        ('5', '80'),
        ('6', '112'),
        ('7', '116'),
        ('8', '120'),
        ('9', '125'),
        ('a', '30216'),
        ('b', '30229'),
        ('c', '30232'),
        ('d', '30280'),
        ('e', '6'),
        ('f', '5'),
    )

    _container_map = (
        ('0', None),
        ('1', 'm4s'),
        ('2', 'mp4'),
        ('3', 'flv'),
        ('4', 'm4a'),
    )

    _media_type_map = (
        ('0', None),
        ('1', 'video_audio'),
        ('2', 'video'),
        ('3', 'audio'),
    )

    _video_codec_map = (
        ('0', None),
        ('1', 'avc'),
        ('2', 'hevc'),
        ('3', 'av1'),
    )

    _audio_codec_map = (
        ('0', None),
        ('1', 'aac'),
        ('2', 'flac'),
    )

    _frame_rate_map = (
        ('0', None),
        ('1', '0_35', lambda x: 0 < float(x) <= 35),
        ('2', '35_60', lambda x: 35 < float(x) <= 60),
        ('3', '60+', lambda x: float(x) > 60),
    )

    _bit_depth_map = (
        ('0', None),
        ('1', '8'),
        ('2', '10'),
        ('3', '12'),
        ('4', '14'),
        ('5', '16'),
    )

    _video_characteristic_map = (
        ('0', None),
        ('1', 'spherical'),
    )

    _audio_characteristic_map = (
        ('0', None),
        ('1', 'dolby'),
    )

    _audio_channels_map = (
        ('0', None),
        ('1', '1'),
        ('2', '2'),
        ('5', '5.1'),
    )

    _audio_sample_rate_map = (
        ('0', None),
        ('1', '0_22050', lambda x: 0 < float(x) <= 22050),
        ('2', '22050_44100', lambda x: 22050 < float(x) <= 44100),
        ('3', '44100_48000', lambda x: 44100 < float(x) <= 48000),
    )

    @classmethod
    def all(cls):
        fs = (
            ('business', cls._business_map),
            ('qn', cls._qn_map),
            ('container', cls._container_map),
            ('media_type', cls._media_type_map),
            ('video_codec', cls._video_codec_map),
            ('audio_codec', cls._audio_codec_map),
            ('frame_rate', cls._frame_rate_map),
            ('bit_depth', cls._bit_depth_map),
            ('video_characteristic', cls._video_characteristic_map),
            ('audio_characteristic', cls._audio_characteristic_map),
            ('audio_channels', cls._audio_channels_map),
            ('audio_sample_rate', cls._audio_sample_rate_map),
        )
        return fs

    @classmethod
    def _map(cls, flag_key: str):
        key = str(flag_key).lower()

        fmap_list = cls.all()
        for fm in fmap_list:
            if fm[0] == key:
                return fm[1]

        raise ValueError(f'flag error: map failed, flag_key({key}) not found')

    @classmethod
    def get(cls, flag_key: str, value_of_data):
        """ value: info => flag """
        value = str(value_of_data).lower() if value_of_data is not None else value_of_data
        fmap = cls._map(flag_key)

        for fv, iv, *extra in fmap:
            if len(extra) > 0:
                matcher = extra[0]
            else:
                matcher = lambda x: False  # noqa: E731

            if (iv == value) or matcher(value):
                return fv

        raise ValueError(
            f'flag error: get_inv failed, value({value}) invalid for flag_key({flag_key}) with flag_map{fmap}'
        )

    @classmethod
    def get_inverted(cls, flag_key: str, value_of_flag: str):
        """ value: flag => info """
        value = str(value_of_flag).lower()
        fmap = cls._map(flag_key)

        for fv, iv, *_ in fmap:
            if fv == value:
                return iv

        raise ValueError(
            f'flag error: get failed, value({value}) invalid for flag_key({flag_key}) with flag_map({fmap})'
        )


def common_flag(data: dict):
    fmap_list = _CommonFlagModel.all()
    result = []
    for fmap in fmap_list:
        k = fmap[0]
        if k in data:
            iv = data[k]
            fv = _CommonFlagModel.get(k, iv)
            result.append(fv)
        else:
            result.append('0')
    return ''.join(result)


def common_flag_parse(flag: str):
    fmap_list = _CommonFlagModel.all()
    result = {}
    for idx, fmap in enumerate(fmap_list):
        if idx < len(flag):
            fv = flag[idx]
        else:
            fv = '0'
        k = fmap[0]
        result[k] = _CommonFlagModel.get_inverted(k, fv)
    return result


def common_flag_update(flag: str, data_for_update: dict):
    data = common_flag_parse(flag)
    data.update(data_for_update)
    return common_flag(data)
