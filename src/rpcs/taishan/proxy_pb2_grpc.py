# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import proxy_pb2 as proxy__pb2


class TaishanProxyStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.put = channel.unary_unary(
                '/taishan.api.TaishanProxy/put',
                request_serializer=proxy__pb2.PutReq.SerializeToString,
                response_deserializer=proxy__pb2.PutResp.FromString,
                )
        self.expire = channel.unary_unary(
                '/taishan.api.TaishanProxy/expire',
                request_serializer=proxy__pb2.ExpireReq.SerializeToString,
                response_deserializer=proxy__pb2.ExpireResp.FromString,
                )
        self.get = channel.unary_unary(
                '/taishan.api.TaishanProxy/get',
                request_serializer=proxy__pb2.GetReq.SerializeToString,
                response_deserializer=proxy__pb2.GetResp.FromString,
                )
        self.count = channel.unary_unary(
                '/taishan.api.TaishanProxy/count',
                request_serializer=proxy__pb2.CountReq.SerializeToString,
                response_deserializer=proxy__pb2.CountResp.FromString,
                )
        self.scan = channel.unary_unary(
                '/taishan.api.TaishanProxy/scan',
                request_serializer=proxy__pb2.ScanReq.SerializeToString,
                response_deserializer=proxy__pb2.ScanResp.FromString,
                )
        self.cas = channel.unary_unary(
                '/taishan.api.TaishanProxy/cas',
                request_serializer=proxy__pb2.CasReq.SerializeToString,
                response_deserializer=proxy__pb2.CasResp.FromString,
                )
        self.incr = channel.unary_unary(
                '/taishan.api.TaishanProxy/incr',
                request_serializer=proxy__pb2.IncrReq.SerializeToString,
                response_deserializer=proxy__pb2.IncrResp.FromString,
                )
        self.pop = channel.unary_unary(
                '/taishan.api.TaishanProxy/pop',
                request_serializer=proxy__pb2.PopReq.SerializeToString,
                response_deserializer=proxy__pb2.PopResp.FromString,
                )
        self.push = channel.unary_unary(
                '/taishan.api.TaishanProxy/push',
                request_serializer=proxy__pb2.PushReq.SerializeToString,
                response_deserializer=proxy__pb2.PushResp.FromString,
                )
        self.batch_get = channel.unary_unary(
                '/taishan.api.TaishanProxy/batch_get',
                request_serializer=proxy__pb2.BatchGetReq.SerializeToString,
                response_deserializer=proxy__pb2.BatchGetResp.FromString,
                )
        self.batch_put = channel.unary_unary(
                '/taishan.api.TaishanProxy/batch_put',
                request_serializer=proxy__pb2.BatchPutReq.SerializeToString,
                response_deserializer=proxy__pb2.BatchPutResp.FromString,
                )
        self.batch_del = channel.unary_unary(
                '/taishan.api.TaishanProxy/batch_del',
                request_serializer=proxy__pb2.BatchDelReq.SerializeToString,
                response_deserializer=proxy__pb2.BatchDelResp.FromString,
                )


class TaishanProxyServicer(object):
    """Missing associated documentation comment in .proto file."""

    def put(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def expire(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def get(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def count(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def scan(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def cas(self, request, context):
        """del 是 python 关键字，无法使用
        rpc del(DelReq) returns (DelResp);
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def incr(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def pop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def push(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def batch_get(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def batch_put(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def batch_del(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TaishanProxyServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'put': grpc.unary_unary_rpc_method_handler(
                    servicer.put,
                    request_deserializer=proxy__pb2.PutReq.FromString,
                    response_serializer=proxy__pb2.PutResp.SerializeToString,
            ),
            'expire': grpc.unary_unary_rpc_method_handler(
                    servicer.expire,
                    request_deserializer=proxy__pb2.ExpireReq.FromString,
                    response_serializer=proxy__pb2.ExpireResp.SerializeToString,
            ),
            'get': grpc.unary_unary_rpc_method_handler(
                    servicer.get,
                    request_deserializer=proxy__pb2.GetReq.FromString,
                    response_serializer=proxy__pb2.GetResp.SerializeToString,
            ),
            'count': grpc.unary_unary_rpc_method_handler(
                    servicer.count,
                    request_deserializer=proxy__pb2.CountReq.FromString,
                    response_serializer=proxy__pb2.CountResp.SerializeToString,
            ),
            'scan': grpc.unary_unary_rpc_method_handler(
                    servicer.scan,
                    request_deserializer=proxy__pb2.ScanReq.FromString,
                    response_serializer=proxy__pb2.ScanResp.SerializeToString,
            ),
            'cas': grpc.unary_unary_rpc_method_handler(
                    servicer.cas,
                    request_deserializer=proxy__pb2.CasReq.FromString,
                    response_serializer=proxy__pb2.CasResp.SerializeToString,
            ),
            'incr': grpc.unary_unary_rpc_method_handler(
                    servicer.incr,
                    request_deserializer=proxy__pb2.IncrReq.FromString,
                    response_serializer=proxy__pb2.IncrResp.SerializeToString,
            ),
            'pop': grpc.unary_unary_rpc_method_handler(
                    servicer.pop,
                    request_deserializer=proxy__pb2.PopReq.FromString,
                    response_serializer=proxy__pb2.PopResp.SerializeToString,
            ),
            'push': grpc.unary_unary_rpc_method_handler(
                    servicer.push,
                    request_deserializer=proxy__pb2.PushReq.FromString,
                    response_serializer=proxy__pb2.PushResp.SerializeToString,
            ),
            'batch_get': grpc.unary_unary_rpc_method_handler(
                    servicer.batch_get,
                    request_deserializer=proxy__pb2.BatchGetReq.FromString,
                    response_serializer=proxy__pb2.BatchGetResp.SerializeToString,
            ),
            'batch_put': grpc.unary_unary_rpc_method_handler(
                    servicer.batch_put,
                    request_deserializer=proxy__pb2.BatchPutReq.FromString,
                    response_serializer=proxy__pb2.BatchPutResp.SerializeToString,
            ),
            'batch_del': grpc.unary_unary_rpc_method_handler(
                    servicer.batch_del,
                    request_deserializer=proxy__pb2.BatchDelReq.FromString,
                    response_serializer=proxy__pb2.BatchDelResp.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'taishan.api.TaishanProxy', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TaishanProxy(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def put(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/put',
            proxy__pb2.PutReq.SerializeToString,
            proxy__pb2.PutResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def expire(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/expire',
            proxy__pb2.ExpireReq.SerializeToString,
            proxy__pb2.ExpireResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def get(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/get',
            proxy__pb2.GetReq.SerializeToString,
            proxy__pb2.GetResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def count(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/count',
            proxy__pb2.CountReq.SerializeToString,
            proxy__pb2.CountResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def scan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/scan',
            proxy__pb2.ScanReq.SerializeToString,
            proxy__pb2.ScanResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def cas(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/cas',
            proxy__pb2.CasReq.SerializeToString,
            proxy__pb2.CasResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def incr(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/incr',
            proxy__pb2.IncrReq.SerializeToString,
            proxy__pb2.IncrResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def pop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/pop',
            proxy__pb2.PopReq.SerializeToString,
            proxy__pb2.PopResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def push(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/push',
            proxy__pb2.PushReq.SerializeToString,
            proxy__pb2.PushResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def batch_get(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/batch_get',
            proxy__pb2.BatchGetReq.SerializeToString,
            proxy__pb2.BatchGetResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def batch_put(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/batch_put',
            proxy__pb2.BatchPutReq.SerializeToString,
            proxy__pb2.BatchPutResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def batch_del(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/taishan.api.TaishanProxy/batch_del',
            proxy__pb2.BatchDelReq.SerializeToString,
            proxy__pb2.BatchDelResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
