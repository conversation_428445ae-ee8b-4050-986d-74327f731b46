import logging

from . import proxy_pb2
from . import proxy_pb2_grpc
from .. import BaseRpc
from ...errors import TaishanError
from ...grpc import GrpcClient


class TaishanRpc(BaseRpc):
    __stub_class__ = proxy_pb2_grpc.TaishanProxyStub

    def __init__(self, client: GrpcClient, table: str, token: str):
        super().__init__(client)
        self._table = table
        self._token = token

    def _common_params(self):
        return dict(
            table=self._table,
            auth=proxy_pb2.Auth(token=self._token),
        )

    def set(self, key: str, value: str, *, ttl: int = None):
        # 0 表示永不过期
        ttl = ttl or 0
        resp: proxy_pb2.PutResp = self._request(
            method='put',
            request=proxy_pb2.PutReq(
                **self._common_params(),
                record=proxy_pb2.Record(
                    key=key.encode(),
                    columns=[proxy_pb2.Column(
                        value=value.encode(),
                    )],
                    ttl=ttl,
                ),
            ),
        )
        if resp.status.err_no != 0:
            raise TaishanError(code=resp.status.err_no, message=resp.status.msg)

    def get(self, key: str):
        # taishan 会保证至少返回一个 column
        resp: proxy_pb2.GetResp = self._request(
            method='get',
            request=proxy_pb2.GetReq(
                **self._common_params(),
                record=proxy_pb2.Record(
                    key=key.encode(),
                ),
            ),
        )
        return resp.record.columns[0].value.decode()

    def expire(self, key: str, ttl: int):
        # 0 表示永不过期
        ttl = ttl or 0
        resp: proxy_pb2.ExpireResp = self._request(
            method='expire',
            request=proxy_pb2.ExpireReq(
                **self._common_params(),
                key=key.encode(),
                ttl=ttl,
            ),
        )
        if resp.status.err_no == 404:
            logging.warning(f'[pyutils] taishan expire key not found: key<{key}>')
        elif resp.status.err_no != 0:
            raise TaishanError(code=resp.status.err_no, message=resp.status.msg)
