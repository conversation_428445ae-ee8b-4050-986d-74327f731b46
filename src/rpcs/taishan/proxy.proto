syntax = "proto3";
package taishan.api;

option go_package = "taishan";

option cc_generic_services = true;

message Status {
  int32 err_no = 1;
  string msg = 2;
}

message Column {
  bytes name = 1;
  bytes value = 2;
}

message Record {
  bytes key = 1;
  repeated Column columns = 2;
  uint32 ttl = 3;
  Status status = 4;
}

message Auth { string token = 1; }

message PutReq {
  string table = 1;
  Record record = 2;
  Auth auth = 3;
}

message PutResp { Status status = 1; }

message GetReq {
  string table = 1;
  Record record = 2;
  Auth auth = 3;
}

message GetResp { Record record = 1; }

message ScanReq {
  string table = 1;
  Record start_rec = 2;
  Record end_rec = 3;
  uint32 limit = 4;
  Auth auth = 5;
}

message ScanResp {
  Status status = 1;
  repeated Record records = 2;
  bytes next_key = 3;
  bool has_next = 4;
}

message DelReq {
  string table = 1;
  Record record = 2;
  Auth auth = 3;
}

message DelResp { Status status = 1; }
message BatchGetReq {
  string table = 1;
  repeated Record records = 2;
  Auth auth = 3;
}

message CountReq {
  string table = 1;
  bytes key = 2;
  Auth auth = 3;
}

message CountResp {
  Status status = 1;
  uint64 count = 2;
}

message BatchGetResp {
  bool all_succeed = 1;
  bool all_failed = 2;
  repeated Record records = 3;
}

message IncrReq {
  string table = 1;
  bytes key = 2;
  int64 increment = 3;
  Auth auth = 4;
}

message IncrResp {
  Status status = 1;
  Record record = 3;
}

message CasCond {
  enum Method { EQUALS = 0; }
  Method method = 1;
  repeated Record records = 2;
}

message CasReq {
  string table = 1;
  CasCond cond = 2;
  repeated Record records = 3;
  Auth auth = 4;
}

message CasResp {
  Status status = 1;
  repeated Record records = 2;
}

message PushReq {
  string table = 1;
  bytes key = 2;
  repeated bytes values = 3;
  Auth auth = 4;
}

message PushResp { Status status = 1; }

message PopReq {
  string table = 1;
  bytes key = 2;
  int64 index = 3;
  Auth auth = 4;
}

message PopResp {
  Status status = 1;
  bytes value = 2;
  int64 index = 3;
}

message BatchPutReq {
  string table = 1;
  repeated Record records = 2;
  Auth auth = 3;
}

message BatchPutResp {
  bool all_succeed = 1;
  bool all_failed = 2;
  repeated Record records = 3;
}

message BatchDelReq {
  string table = 1;
  repeated Record records = 2;
  Auth auth = 3;
}

message BatchDelResp {
  bool all_succeed = 1;
  bool all_failed = 2;
  repeated Record records = 3;
}

message ExpireReq {
  string table = 1;
  bytes key = 2;
  int32 ttl = 3; // ttl必须不小于0，小于0为非法参数。0表示永不过期，
  Auth auth = 4;
}

message ExpireResp { Status status = 1; }

service TaishanProxy {
  rpc put(PutReq) returns (PutResp);
  rpc expire(ExpireReq) returns (ExpireResp);
  rpc get(GetReq) returns (GetResp);
  rpc count(CountReq) returns (CountResp);
  rpc scan(ScanReq) returns (ScanResp);
  // del 是 python 关键字，无法使用
  // rpc del(DelReq) returns (DelResp);
  rpc cas(CasReq) returns (CasResp);
  rpc incr(IncrReq) returns (IncrResp);
  rpc pop(PopReq) returns (PopResp);
  rpc push(PushReq) returns (PushResp);
  rpc batch_get(BatchGetReq) returns (BatchGetResp);
  rpc batch_put(BatchPutReq) returns (BatchPutResp);
  rpc batch_del(BatchDelReq) returns (BatchDelResp);
}