import logging

from google.protobuf.message import Message

from . import proxy_pb2
from .rpc import TaishanRpc
from ...errors import TaishanError
from ...grpc.aio import GrpcAioClient


class TaishanAioRpc(TaishanRpc):
    def __init__(self, client: GrpcAioClient, table: str, token: str):
        super().__init__(client, table, token)

    async def _request(self, method: str, request: Message):
        stub_class = self.__stub_class__
        return await self._client.request(stub_class, method, request)

    async def set(self, key: str, value: str, *, ttl: int = None):
        # 0 表示永不过期
        ttl = ttl or 0
        resp: proxy_pb2.PutResp = await self._request(
            method='put',
            request=proxy_pb2.PutReq(
                **self._common_params(),
                record=proxy_pb2.Record(
                    key=key.encode(),
                    columns=[proxy_pb2.Column(
                        value=value.encode(),
                    )],
                    ttl=ttl,
                ),
            ),
        )
        if resp.status.err_no != 0:
            raise TaishanError(code=resp.status.err_no, message=resp.status.msg)

    async def get(self, key: str):
        # taishan 会保证至少返回一个 column
        resp: proxy_pb2.GetResp = await self._request(
            method='get',
            request=proxy_pb2.GetReq(
                **self._common_params(),
                record=proxy_pb2.Record(
                    key=key.encode(),
                ),
            ),
        )
        return resp.record.columns[0].value.decode()

    async def expire(self, key: str, ttl: int):
        # 0 表示永不过期
        ttl = ttl or 0
        resp: proxy_pb2.ExpireResp = await self._request(
            method='expire',
            request=proxy_pb2.ExpireReq(
                **self._common_params(),
                key=key.encode(),
                ttl=ttl,
            ),
        )
        if resp.status.err_no == 404:
            logging.warning(f'[pyutils] taishan expire key not found: key<{key}>')
        elif resp.status.err_no != 0:
            raise TaishanError(code=resp.status.err_no, message=resp.status.msg)
