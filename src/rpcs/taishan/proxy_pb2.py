# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proxy.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bproxy.proto\x12\x0btaishan.api\"%\n\x06Status\x12\x0e\n\x06\x65rr_no\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\"%\n\x06\x43olumn\x12\x0c\n\x04name\x18\x01 \x01(\x0c\x12\r\n\x05value\x18\x02 \x01(\x0c\"m\n\x06Record\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12$\n\x07\x63olumns\x18\x02 \x03(\x0b\x32\x13.taishan.api.Column\x12\x0b\n\x03ttl\x18\x03 \x01(\r\x12#\n\x06status\x18\x04 \x01(\x0b\x32\x13.taishan.api.Status\"\x15\n\x04\x41uth\x12\r\n\x05token\x18\x01 \x01(\t\"]\n\x06PutReq\x12\r\n\x05table\x18\x01 \x01(\t\x12#\n\x06record\x18\x02 \x01(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\".\n\x07PutResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\"]\n\x06GetReq\x12\r\n\x05table\x18\x01 \x01(\t\x12#\n\x06record\x18\x02 \x01(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\".\n\x07GetResp\x12#\n\x06record\x18\x01 \x01(\x0b\x32\x13.taishan.api.Record\"\x96\x01\n\x07ScanReq\x12\r\n\x05table\x18\x01 \x01(\t\x12&\n\tstart_rec\x18\x02 \x01(\x0b\x32\x13.taishan.api.Record\x12$\n\x07\x65nd_rec\x18\x03 \x01(\x0b\x32\x13.taishan.api.Record\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x1f\n\x04\x61uth\x18\x05 \x01(\x0b\x32\x11.taishan.api.Auth\"y\n\x08ScanResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\x12$\n\x07records\x18\x02 \x03(\x0b\x32\x13.taishan.api.Record\x12\x10\n\x08next_key\x18\x03 \x01(\x0c\x12\x10\n\x08has_next\x18\x04 \x01(\x08\"]\n\x06\x44\x65lReq\x12\r\n\x05table\x18\x01 \x01(\t\x12#\n\x06record\x18\x02 \x01(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\".\n\x07\x44\x65lResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\"c\n\x0b\x42\x61tchGetReq\x12\r\n\x05table\x18\x01 \x01(\t\x12$\n\x07records\x18\x02 \x03(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\"G\n\x08\x43ountReq\x12\r\n\x05table\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\"?\n\tCountResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\"]\n\x0c\x42\x61tchGetResp\x12\x13\n\x0b\x61ll_succeed\x18\x01 \x01(\x08\x12\x12\n\nall_failed\x18\x02 \x01(\x08\x12$\n\x07records\x18\x03 \x03(\x0b\x32\x13.taishan.api.Record\"Y\n\x07IncrReq\x12\r\n\x05table\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\x12\x11\n\tincrement\x18\x03 \x01(\x03\x12\x1f\n\x04\x61uth\x18\x04 \x01(\x0b\x32\x11.taishan.api.Auth\"T\n\x08IncrResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\x12#\n\x06record\x18\x03 \x01(\x0b\x32\x13.taishan.api.Record\"r\n\x07\x43\x61sCond\x12+\n\x06method\x18\x01 \x01(\x0e\x32\x1b.taishan.api.CasCond.Method\x12$\n\x07records\x18\x02 \x03(\x0b\x32\x13.taishan.api.Record\"\x14\n\x06Method\x12\n\n\x06\x45QUALS\x10\x00\"\x82\x01\n\x06\x43\x61sReq\x12\r\n\x05table\x18\x01 \x01(\t\x12\"\n\x04\x63ond\x18\x02 \x01(\x0b\x32\x14.taishan.api.CasCond\x12$\n\x07records\x18\x03 \x03(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x04 \x01(\x0b\x32\x11.taishan.api.Auth\"T\n\x07\x43\x61sResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\x12$\n\x07records\x18\x02 \x03(\x0b\x32\x13.taishan.api.Record\"V\n\x07PushReq\x12\r\n\x05table\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\x12\x0e\n\x06values\x18\x03 \x03(\x0c\x12\x1f\n\x04\x61uth\x18\x04 \x01(\x0b\x32\x11.taishan.api.Auth\"/\n\x08PushResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\"T\n\x06PopReq\x12\r\n\x05table\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\x12\r\n\x05index\x18\x03 \x01(\x03\x12\x1f\n\x04\x61uth\x18\x04 \x01(\x0b\x32\x11.taishan.api.Auth\"L\n\x07PopResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status\x12\r\n\x05value\x18\x02 \x01(\x0c\x12\r\n\x05index\x18\x03 \x01(\x03\"c\n\x0b\x42\x61tchPutReq\x12\r\n\x05table\x18\x01 \x01(\t\x12$\n\x07records\x18\x02 \x03(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\"]\n\x0c\x42\x61tchPutResp\x12\x13\n\x0b\x61ll_succeed\x18\x01 \x01(\x08\x12\x12\n\nall_failed\x18\x02 \x01(\x08\x12$\n\x07records\x18\x03 \x03(\x0b\x32\x13.taishan.api.Record\"c\n\x0b\x42\x61tchDelReq\x12\r\n\x05table\x18\x01 \x01(\t\x12$\n\x07records\x18\x02 \x03(\x0b\x32\x13.taishan.api.Record\x12\x1f\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x11.taishan.api.Auth\"]\n\x0c\x42\x61tchDelResp\x12\x13\n\x0b\x61ll_succeed\x18\x01 \x01(\x08\x12\x12\n\nall_failed\x18\x02 \x01(\x08\x12$\n\x07records\x18\x03 \x03(\x0b\x32\x13.taishan.api.Record\"U\n\tExpireReq\x12\r\n\x05table\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\x12\x0b\n\x03ttl\x18\x03 \x01(\x05\x12\x1f\n\x04\x61uth\x18\x04 \x01(\x0b\x32\x11.taishan.api.Auth\"1\n\nExpireResp\x12#\n\x06status\x18\x01 \x01(\x0b\x32\x13.taishan.api.Status2\xae\x05\n\x0cTaishanProxy\x12\x30\n\x03put\x12\x13.taishan.api.PutReq\x1a\x14.taishan.api.PutResp\x12\x39\n\x06\x65xpire\x12\x16.taishan.api.ExpireReq\x1a\x17.taishan.api.ExpireResp\x12\x30\n\x03get\x12\x13.taishan.api.GetReq\x1a\x14.taishan.api.GetResp\x12\x36\n\x05\x63ount\x12\x15.taishan.api.CountReq\x1a\x16.taishan.api.CountResp\x12\x33\n\x04scan\x12\x14.taishan.api.ScanReq\x1a\x15.taishan.api.ScanResp\x12\x30\n\x03\x63\x61s\x12\x13.taishan.api.CasReq\x1a\x14.taishan.api.CasResp\x12\x33\n\x04incr\x12\x14.taishan.api.IncrReq\x1a\x15.taishan.api.IncrResp\x12\x30\n\x03pop\x12\x13.taishan.api.PopReq\x1a\x14.taishan.api.PopResp\x12\x33\n\x04push\x12\x14.taishan.api.PushReq\x1a\x15.taishan.api.PushResp\x12@\n\tbatch_get\x12\x18.taishan.api.BatchGetReq\x1a\x19.taishan.api.BatchGetResp\x12@\n\tbatch_put\x12\x18.taishan.api.BatchPutReq\x1a\x19.taishan.api.BatchPutResp\x12@\n\tbatch_del\x12\x18.taishan.api.BatchDelReq\x1a\x19.taishan.api.BatchDelRespB\x0cZ\x07taishan\x80\x01\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proxy_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\007taishan\200\001\001'
  _STATUS._serialized_start=28
  _STATUS._serialized_end=65
  _COLUMN._serialized_start=67
  _COLUMN._serialized_end=104
  _RECORD._serialized_start=106
  _RECORD._serialized_end=215
  _AUTH._serialized_start=217
  _AUTH._serialized_end=238
  _PUTREQ._serialized_start=240
  _PUTREQ._serialized_end=333
  _PUTRESP._serialized_start=335
  _PUTRESP._serialized_end=381
  _GETREQ._serialized_start=383
  _GETREQ._serialized_end=476
  _GETRESP._serialized_start=478
  _GETRESP._serialized_end=524
  _SCANREQ._serialized_start=527
  _SCANREQ._serialized_end=677
  _SCANRESP._serialized_start=679
  _SCANRESP._serialized_end=800
  _DELREQ._serialized_start=802
  _DELREQ._serialized_end=895
  _DELRESP._serialized_start=897
  _DELRESP._serialized_end=943
  _BATCHGETREQ._serialized_start=945
  _BATCHGETREQ._serialized_end=1044
  _COUNTREQ._serialized_start=1046
  _COUNTREQ._serialized_end=1117
  _COUNTRESP._serialized_start=1119
  _COUNTRESP._serialized_end=1182
  _BATCHGETRESP._serialized_start=1184
  _BATCHGETRESP._serialized_end=1277
  _INCRREQ._serialized_start=1279
  _INCRREQ._serialized_end=1368
  _INCRRESP._serialized_start=1370
  _INCRRESP._serialized_end=1454
  _CASCOND._serialized_start=1456
  _CASCOND._serialized_end=1570
  _CASCOND_METHOD._serialized_start=1550
  _CASCOND_METHOD._serialized_end=1570
  _CASREQ._serialized_start=1573
  _CASREQ._serialized_end=1703
  _CASRESP._serialized_start=1705
  _CASRESP._serialized_end=1789
  _PUSHREQ._serialized_start=1791
  _PUSHREQ._serialized_end=1877
  _PUSHRESP._serialized_start=1879
  _PUSHRESP._serialized_end=1926
  _POPREQ._serialized_start=1928
  _POPREQ._serialized_end=2012
  _POPRESP._serialized_start=2014
  _POPRESP._serialized_end=2090
  _BATCHPUTREQ._serialized_start=2092
  _BATCHPUTREQ._serialized_end=2191
  _BATCHPUTRESP._serialized_start=2193
  _BATCHPUTRESP._serialized_end=2286
  _BATCHDELREQ._serialized_start=2288
  _BATCHDELREQ._serialized_end=2387
  _BATCHDELRESP._serialized_start=2389
  _BATCHDELRESP._serialized_end=2482
  _EXPIREREQ._serialized_start=2484
  _EXPIREREQ._serialized_end=2569
  _EXPIRERESP._serialized_start=2571
  _EXPIRERESP._serialized_end=2620
  _TAISHANPROXY._serialized_start=2623
  _TAISHANPROXY._serialized_end=3309
# @@protoc_insertion_point(module_scope)
