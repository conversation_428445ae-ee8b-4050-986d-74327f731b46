from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Auth(_message.Message):
    __slots__ = ["token"]
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    token: str
    def __init__(self, token: _Optional[str] = ...) -> None: ...

class BatchDelReq(_message.Message):
    __slots__ = ["auth", "records", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    records: _containers.RepeatedCompositeFieldContainer[Record]
    table: str
    def __init__(self, table: _Optional[str] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class BatchDelResp(_message.Message):
    __slots__ = ["all_failed", "all_succeed", "records"]
    ALL_FAILED_FIELD_NUMBER: _ClassVar[int]
    ALL_SUCCEED_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    all_failed: bool
    all_succeed: bool
    records: _containers.RepeatedCompositeFieldContainer[Record]
    def __init__(self, all_succeed: bool = ..., all_failed: bool = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ...) -> None: ...

class BatchGetReq(_message.Message):
    __slots__ = ["auth", "records", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    records: _containers.RepeatedCompositeFieldContainer[Record]
    table: str
    def __init__(self, table: _Optional[str] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class BatchGetResp(_message.Message):
    __slots__ = ["all_failed", "all_succeed", "records"]
    ALL_FAILED_FIELD_NUMBER: _ClassVar[int]
    ALL_SUCCEED_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    all_failed: bool
    all_succeed: bool
    records: _containers.RepeatedCompositeFieldContainer[Record]
    def __init__(self, all_succeed: bool = ..., all_failed: bool = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ...) -> None: ...

class BatchPutReq(_message.Message):
    __slots__ = ["auth", "records", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    records: _containers.RepeatedCompositeFieldContainer[Record]
    table: str
    def __init__(self, table: _Optional[str] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class BatchPutResp(_message.Message):
    __slots__ = ["all_failed", "all_succeed", "records"]
    ALL_FAILED_FIELD_NUMBER: _ClassVar[int]
    ALL_SUCCEED_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    all_failed: bool
    all_succeed: bool
    records: _containers.RepeatedCompositeFieldContainer[Record]
    def __init__(self, all_succeed: bool = ..., all_failed: bool = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ...) -> None: ...

class CasCond(_message.Message):
    __slots__ = ["method", "records"]
    class Method(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = []
    EQUALS: CasCond.Method
    METHOD_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    method: CasCond.Method
    records: _containers.RepeatedCompositeFieldContainer[Record]
    def __init__(self, method: _Optional[_Union[CasCond.Method, str]] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ...) -> None: ...

class CasReq(_message.Message):
    __slots__ = ["auth", "cond", "records", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    COND_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    cond: CasCond
    records: _containers.RepeatedCompositeFieldContainer[Record]
    table: str
    def __init__(self, table: _Optional[str] = ..., cond: _Optional[_Union[CasCond, _Mapping]] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class CasResp(_message.Message):
    __slots__ = ["records", "status"]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    records: _containers.RepeatedCompositeFieldContainer[Record]
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ...) -> None: ...

class Column(_message.Message):
    __slots__ = ["name", "value"]
    NAME_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    name: bytes
    value: bytes
    def __init__(self, name: _Optional[bytes] = ..., value: _Optional[bytes] = ...) -> None: ...

class CountReq(_message.Message):
    __slots__ = ["auth", "key", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    key: bytes
    table: str
    def __init__(self, table: _Optional[str] = ..., key: _Optional[bytes] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class CountResp(_message.Message):
    __slots__ = ["count", "status"]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    count: int
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ..., count: _Optional[int] = ...) -> None: ...

class DelReq(_message.Message):
    __slots__ = ["auth", "record", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    RECORD_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    record: Record
    table: str
    def __init__(self, table: _Optional[str] = ..., record: _Optional[_Union[Record, _Mapping]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class DelResp(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ...) -> None: ...

class ExpireReq(_message.Message):
    __slots__ = ["auth", "key", "table", "ttl"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    TTL_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    key: bytes
    table: str
    ttl: int
    def __init__(self, table: _Optional[str] = ..., key: _Optional[bytes] = ..., ttl: _Optional[int] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class ExpireResp(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ...) -> None: ...

class GetReq(_message.Message):
    __slots__ = ["auth", "record", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    RECORD_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    record: Record
    table: str
    def __init__(self, table: _Optional[str] = ..., record: _Optional[_Union[Record, _Mapping]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class GetResp(_message.Message):
    __slots__ = ["record"]
    RECORD_FIELD_NUMBER: _ClassVar[int]
    record: Record
    def __init__(self, record: _Optional[_Union[Record, _Mapping]] = ...) -> None: ...

class IncrReq(_message.Message):
    __slots__ = ["auth", "increment", "key", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    INCREMENT_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    increment: int
    key: bytes
    table: str
    def __init__(self, table: _Optional[str] = ..., key: _Optional[bytes] = ..., increment: _Optional[int] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class IncrResp(_message.Message):
    __slots__ = ["record", "status"]
    RECORD_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    record: Record
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ..., record: _Optional[_Union[Record, _Mapping]] = ...) -> None: ...

class PopReq(_message.Message):
    __slots__ = ["auth", "index", "key", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    index: int
    key: bytes
    table: str
    def __init__(self, table: _Optional[str] = ..., key: _Optional[bytes] = ..., index: _Optional[int] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class PopResp(_message.Message):
    __slots__ = ["index", "status", "value"]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    index: int
    status: Status
    value: bytes
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ..., value: _Optional[bytes] = ..., index: _Optional[int] = ...) -> None: ...

class PushReq(_message.Message):
    __slots__ = ["auth", "key", "table", "values"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    VALUES_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    key: bytes
    table: str
    values: _containers.RepeatedScalarFieldContainer[bytes]
    def __init__(self, table: _Optional[str] = ..., key: _Optional[bytes] = ..., values: _Optional[_Iterable[bytes]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class PushResp(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ...) -> None: ...

class PutReq(_message.Message):
    __slots__ = ["auth", "record", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    RECORD_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    record: Record
    table: str
    def __init__(self, table: _Optional[str] = ..., record: _Optional[_Union[Record, _Mapping]] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class PutResp(_message.Message):
    __slots__ = ["status"]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ...) -> None: ...

class Record(_message.Message):
    __slots__ = ["columns", "key", "status", "ttl"]
    COLUMNS_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    TTL_FIELD_NUMBER: _ClassVar[int]
    columns: _containers.RepeatedCompositeFieldContainer[Column]
    key: bytes
    status: Status
    ttl: int
    def __init__(self, key: _Optional[bytes] = ..., columns: _Optional[_Iterable[_Union[Column, _Mapping]]] = ..., ttl: _Optional[int] = ..., status: _Optional[_Union[Status, _Mapping]] = ...) -> None: ...

class ScanReq(_message.Message):
    __slots__ = ["auth", "end_rec", "limit", "start_rec", "table"]
    AUTH_FIELD_NUMBER: _ClassVar[int]
    END_REC_FIELD_NUMBER: _ClassVar[int]
    LIMIT_FIELD_NUMBER: _ClassVar[int]
    START_REC_FIELD_NUMBER: _ClassVar[int]
    TABLE_FIELD_NUMBER: _ClassVar[int]
    auth: Auth
    end_rec: Record
    limit: int
    start_rec: Record
    table: str
    def __init__(self, table: _Optional[str] = ..., start_rec: _Optional[_Union[Record, _Mapping]] = ..., end_rec: _Optional[_Union[Record, _Mapping]] = ..., limit: _Optional[int] = ..., auth: _Optional[_Union[Auth, _Mapping]] = ...) -> None: ...

class ScanResp(_message.Message):
    __slots__ = ["has_next", "next_key", "records", "status"]
    HAS_NEXT_FIELD_NUMBER: _ClassVar[int]
    NEXT_KEY_FIELD_NUMBER: _ClassVar[int]
    RECORDS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    has_next: bool
    next_key: bytes
    records: _containers.RepeatedCompositeFieldContainer[Record]
    status: Status
    def __init__(self, status: _Optional[_Union[Status, _Mapping]] = ..., records: _Optional[_Iterable[_Union[Record, _Mapping]]] = ..., next_key: _Optional[bytes] = ..., has_next: bool = ...) -> None: ...

class Status(_message.Message):
    __slots__ = ["err_no", "msg"]
    ERR_NO_FIELD_NUMBER: _ClassVar[int]
    MSG_FIELD_NUMBER: _ClassVar[int]
    err_no: int
    msg: str
    def __init__(self, err_no: _Optional[int] = ..., msg: _Optional[str] = ...) -> None: ...
