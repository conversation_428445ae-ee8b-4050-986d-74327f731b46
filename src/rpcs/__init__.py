import copy

from google.protobuf.message import Message

from ..grpc import GrpcClient


class BaseRpc:
    __stub_class__: type = None

    def __init__(self, client: GrpcClient):
        self._client = client

    def _request(self, method: str, request: Message):
        stub_class = self.__stub_class__
        return self._client.request(stub_class, method, request)

    def with_config(self, **kwargs):
        other = copy.copy(self)
        other._client = self._client.with_config(**kwargs)
        return other
