# -*- coding: utf-8 -*-
"""
Here is a simple how we can use logger

.. testcode::
    logger = BillionsLogger(BILLIONS_SOCK_FILE, BILLIONS_TASK_ID, BILLIONS_APP_ID).get_logger()


"""

import logging
from .lancerhandler import LancerStream
from .jsonlogger import JsonFormatter


class BillionsLogger(object):
    fmt = '[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s'

    def __init__(self, sock_file_path, task_id, app_id, fmt=None, name=None, level='INFO'):
        self._name = name
        self._sock_file = sock_file_path
        self._task_id = task_id
        self._app_id = app_id
        self._fmt = fmt if fmt else self.fmt
        self._level = level.upper()

    def get_logger(self):
        logger = logging.getLogger(name=self._name)
        opslogHandler = logging.StreamHandler(stream=LancerStream(self._task_id, self._sock_file))
        opslogformatter = JsonFormatter(fmt=self._fmt,
                                        additional_fields={
                                            'app_id': self._app_id,
                                        })
        opslogHandler.setFormatter(opslogformatter)
        logger.addHandler(opslogHandler)
        logger.setLevel(self._level)
        return logger
