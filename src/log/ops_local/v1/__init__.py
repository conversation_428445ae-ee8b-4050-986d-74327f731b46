# -*- coding: utf-8 -*-
"""
Here is a simple how we can use logger

.. testcode::
    logger = BillionsLogger(BILLIONS_SOCK_FILE, BILLIONS_TASK_ID, BILLIONS_APP_ID).get_logger()


"""
import logging
import socket
import time
import json
import os
from logging.handlers import DEFAULT_TCP_LOGGING_PORT, SocketHandler
from .logstash_formatter import LogstashFormatterBase


class BillionsLogFormatter(LogstashFormatterBase):
    version = 1

    def __init__(self, app_id, message_type='logstash', tags=None, fqdn=False):
        super(BillionsLogFormatter, self).__init__(message_type, tags, fqdn)
        self.app_id = app_id

    def format(self, record):
        # Create message dict
        message = {
            'time': self.format_timestamp(record.created),
            'log': record.msg,
            'app_id': self.app_id,
            'path': record.pathname,
            'lineno': record.lineno,
            'tags': self.tags,
            'instance_id': self.host,
            'pid': os.getpid(),
            'level': record.levelname
        }

        if isinstance(record.msg, str):
            message['log'] = dict(msg=record.msg)
        elif not isinstance(record.msg, dict):
            # todo to avoid es parsing error, all msgs have to be dict wrapped
            return ''

        # Add extra fields
        message.update(self.get_extra_fields(record))

        # If exception, add debug info
        if record.exc_info:
            message.update(self.get_debug_fields(record))
        return json.dumps(message)


class BillionsLogHandler(SocketHandler):
    def __init__(self, sock_path, task_id, message_type='logstash', tags=None, fqdn=False):
        # since makeSocket is overloaded, host, port here will never be used
        super(BillionsLogHandler, self).__init__('127.0.0.1', DEFAULT_TCP_LOGGING_PORT)
        self.sock_path = sock_path
        self.task_id = task_id
        self.closeOnError = 1

    def makeSocket(self, timeout=0.05):
        s = socket.socket(socket.AF_UNIX, socket.SOCK_DGRAM)
        if hasattr(s, 'settimeout'):
            s.settimeout(timeout)
        s.connect(self.sock_path)
        return s

    def makeRecord(self, record):
        record = self.format(record)
        if record:
            return self.task_id + str(int(time.time() * 1000)) + record

    def emit(self, record):
        """
        Emit a record.

        Pickles the record and writes it to the socket in binary format.
        If there is an error with the socket, silently drop the packet.
        If there was a problem with the socket, re-establishes the
        socket.
        """
        try:
            s = self.makeRecord(record)
            if s:
                self.send(s)
        except (KeyboardInterrupt, SystemExit):
            raise
        except Exception:
            self.handleError(record)


class BillionsLogger(object):
    def __init__(self, sock_file_path, task_id, app_id, name=None, level='INFO'):
        self._name = name
        self._sock_file = sock_file_path
        self._task_id = task_id
        self._app_id = app_id
        self._level = level.upper()

    def get_logger(self):
        logger = logging.getLogger(name=self._name)
        if len(logger.handlers) == 0 or not any([isinstance(i, BillionsLogHandler) for i in logger.handlers]):
            handler = BillionsLogHandler(self._sock_file, self._task_id)
            handler.setFormatter(BillionsLogFormatter(self._app_id))
            logger.addHandler(handler)
        # logger.handlers = filter(lambda hdl: not isinstance(hdl, logging.StreamHandler), logger.handlers)
        logger.setLevel(self._level)
        return logger
