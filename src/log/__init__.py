import logging
import os

from logging_sdk import BillionsLoggerV2
from logging_sdk import Extra<PERSON>ieldFormatter

from .ops_local.v2 import BillionsLogger

__all__ = [
    'BillionsLogger',
    'BillionsLoggerV2',

    'ExtraFieldFormatter',

    'init_std_log',
    'init_ops_log',
    'init_ck_log',
]


def init_std_log(*, level: str = 'INFO'):
    format = '[%(levelname)1.1s %(asctime)s %(module)-10.10s:%(lineno)5d] %(message)s'
    date_foramt = '%y%m%d %H:%M:%S'

    logging.basicConfig(**{
        'format': format,
        'datefmt': date_foramt,
        'level': level
    })


def init_ops_log(*, level: str = 'INFO', app_id: str = None, task_id: str = None, sock_file: str = None):
    app_id = app_id or os.getenv('APP_ID', 'unknown')
    task_id = task_id or '000000'
    sock_file = sock_file or '/var/run/lancer/collector.sock'
    BillionsLogger(
        app_id=app_id,
        task_id=task_id,
        sock_file_path=sock_file,
        level=level,
    ).get_logger()


def init_ck_log(*, level: str = 'INFO', channel: str = None, app_id: str = None):
    channel = channel or 'unix:///var/run/lancer/collector_otel.sock'
    BillionsLoggerV2(channel=channel, level=level, service_name=app_id).get_logger()
