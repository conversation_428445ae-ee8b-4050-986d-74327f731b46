import hashlib
import inspect
import logging
import os
import signal
import socket
from functools import wraps
from typing import <PERSON><PERSON>
from typing import Type
from typing import Union

import binascii


class _TypeHint:
    ExceptionClass = Type[Exception]
    ExceptionClassOrTuple = Union[ExceptionClass, Tuple[ExceptionClass, ...]]


class NoneFilterDict(dict):
    """ filter key if value is None """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        d = self.copy()
        for key, value in d.items():
            if value is None:
                self.pop(key)


def chunked(data, chunk_size: int):
    i = 0
    n = chunk_size
    while i < len(data):
        yield data[i:(i + n)]
        i += n


def get_host_name():
    return socket.gethostname()


def get_app_name():
    return os.getenv('CONF_APPID', os.getenv('APP_NAME')) or 'unknown'


def crc32hash(item):
    return binascii.crc32(item.encode()) & 0xffffffff


def hash_md5(msg, raw=False):
    md5 = hashlib.md5()
    md5.update(msg.encode())
    if raw:
        return md5.digest()
    else:
        return md5.hexdigest()


def join_list(sequence, sep=','):
    return sep.join([str(i) for i in sequence])


def get_file_size(path: str):
    return os.path.getsize(path)


def get_file_md5(path: str):
    md5 = hashlib.md5()
    with open(path, 'rb') as f:
        # Read and update hash in chunks of 4K
        for chunk in iter(lambda: f.read(4096), b''):
            md5.update(chunk)
        return md5.hexdigest()


def register_term_signal(signal_handler):
    for signal_num in [signal.SIGTERM, signal.SIGINT, signal.SIGQUIT]:
        signal.signal(signal_num, signal_handler)


def deny_errors(func):
    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        try:
            ret = func(self, *args, **kwargs)
            if inspect.iscoroutine(ret):
                ret = await ret
        except Exception as ex:
            # assertion errors and other unexpected exceptions
            logging.error('[pyutils] internal error: {}'.format(ex), exc_info=True)
            return self.write_failure(msg='internal error:{}'.format(ex))
        return ret

    return wrapper
