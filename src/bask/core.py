import json
import logging
from importlib import import_module
from json import JSONDecodeError
from typing import List

from .constant import TaskBodyKey
from .redis_queue import RedisQueue
from ..common import register_term_signal


class _Task:
    def __init__(self, function, function_name, priority, callback):
        self.function = function
        self.function_name = function_name
        self.priority = priority
        self.task_callback = callback

    def delay(self, **kwargs):
        self.task_callback(self.function_name, self.priority, **kwargs)

    def __call__(self, **kwargs):
        return self.function(**kwargs)

    def __repr__(self):
        return f'FUNCTION:{self.function_name}\tPRIORITY:{self.priority}'


class BaskEvent:
    runBefore = 'run_before'
    runAfter = 'run_after'
    runSuccess = 'run_success'
    runFailure = 'run_failure'

    @classmethod
    def all(cls):
        return [cls.runBefore, cls.runAfter, cls.runSuccess, cls.runFailure]


class Bask:
    # must be class attribute
    functionMap = dict()
    eventMap = {i: (lambda **kwargs: None)for i in BaskEvent.all()}

    def __init__(self, broker):
        self.queue = RedisQueue(broker)
        self.running = False
        self.task_modules = []
        register_term_signal(self._exit_signal_handler)

    def _send_message(self, body):
        self.queue.push(body=body)

    def _send_execute_command(self, function_name, priority, **kwargs):
        logging.info(f"[pyutils] bask send task: function<{function_name}> kwargs<{kwargs}>")
        body = {
            TaskBodyKey.taskName: function_name,
            TaskBodyKey.taskParams: kwargs,
            TaskBodyKey.priority: priority,
        }
        serialized = json.dumps(body)
        self._send_message(serialized)

    def start_consumer(self, *, prefetch_count: int = 1):
        modules = self.task_modules
        for module in modules:
            import_module(module)

        self.running = True
        while self.running:
            task_info_list = self.queue.consume(prefetch_count=prefetch_count)
            for task in task_info_list:
                try:
                    deserialized = json.loads(task)
                except JSONDecodeError:
                    logging.info(f'[pyutils] bask task not valid json: <{task}>')
                    continue
                self.eventMap[BaskEvent.runBefore](task=deserialized)

                function_name = deserialized[TaskBodyKey.taskName]
                kwargs = deserialized[TaskBodyKey.taskParams]

                function = self.functionMap[function_name]
                try:
                    function(**kwargs)
                    self.eventMap[BaskEvent.runSuccess](task=deserialized)
                except Exception as e:
                    self.eventMap[BaskEvent.runFailure](task=deserialized, exception=e, traceback=e.__traceback__)

                self.eventMap[BaskEvent.runAfter](task=deserialized)

    def task(self, *, priority: int = 5):
        def decorate(function):
            function_name = function.__name__
            f = self.functionMap.get(function_name)
            if f and f.__module__ != function.__module__:
                raise ValueError(
                    f'[pyutils] bask function<{function_name}> already registered: '
                    f'<{f.__module__}> <{function.__module__}>'
                )
            self.functionMap[function_name] = function
            logging.info(f'[pyutils] bask register task: function<{function_name}> priority<{priority}>')
            return _Task(
                function=function, function_name=function_name,
                priority=priority, callback=self._send_execute_command,
            )
        return decorate

    def register_event_handler(self, event, handler):
        self.eventMap[event] = handler

    def _exit_signal_handler(self, sig, frame):
        """ graceful close """
        logging.info(f'[pyutils] bask consumer caught signal: signal<{sig}> frame<{frame}>')
        self.running = False
        logging.info('[pyutils] bask consumer stop')

    def register_task_modules(self, modules: List[str]):
        self.task_modules = modules
