import json
import logging
import uuid

import redis

from .constant import TaskBody<PERSON>ey
from .constant import queue_key
from .constant import task_key
from ..retry import retry


class RedisQueue:
    def __init__(self, url):
        self.connection = redis.StrictRedis.from_url(url=url, decode_responses=True)
        # default priority is 0 - 9
        self.queue_list = [queue_key(i) for i in range(0, 10)]

    def push(self, body):
        c = self.connection
        body = json.loads(body)

        task_id = uuid.uuid4().hex
        priority = body.get('priority')
        queue_name = queue_key(priority)

        # 应该先保存 task 详情，再保存 task_id
        # 避免 consume 获取 task 详情失败
        # save task info
        task_info = {
            TaskBodyKey.taskId: task_id,
            TaskBodyKey.queueName: queue_name,
            **body,
        }
        task_info = json.dumps(task_info)
        t = task_key(task_id)
        c.set(t, task_info, ex=3600 * 24)
        # push taskId into queue
        c.lpush(queue_name, task_id)

    @retry(exceptions=(Exception,), delay=0.1, tries=5)
    def consume(self, prefetch_count: int = 1):
        assert prefetch_count == 1, '[pyutils] bask consume not support prefetch_count > 1'

        queue_list = self.queue_list
        c = self.connection

        r = c.brpop(queue_list)
        if not r:
            return []
        _, task_id = r

        k = task_key(task_id)
        task = c.get(k)
        if not task:
            logging.warning(f'[pyutils] bask consume task with empty body: task_id<{task_id}> task<{task}>')
            return []
        c.delete(k)
        return [task]
