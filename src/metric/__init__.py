import threading

import prometheus_client as pc
import prometheus_client.multiprocess as pc_multiprocess
import prometheus_client.registry as pc_registry

from .model import Counter
from .model import Gauge
from .model import Histogram
from .pusher import Pusher

__all__ = [
    'Counter',
    'Gauge',
    'Histogram',

    'generate',
    'start_exporter',
    'start_pusher',

    'gunicorn_handle_child_exit',
]


# 多进程模式（gunicorn）
# 参考 https://github.com/prometheus/client_python#multiprocess-mode-eg-gunicorn
# - 设置环境变量 PROMETHEUS_MULTIPROC_DIR，比如 /tmp/prometheus/{app_name}
#  确保启动时该文件夹存在且可写，推荐每次启动前清理该文件夹
# - 使用 generate(multiprocess=True) 获取 metrics
# - 使用 gunicorn_handle_child_exit 配置 child_exit 事件处理


def generate(*, multiprocess: bool = False):
    """ metrics generate """
    if multiprocess:
        registry = pc_registry.CollectorRegistry()
        pc_multiprocess.MultiProcessCollector(registry)
        content = pc.generate_latest(registry).decode()
    else:
        content = pc.generate_latest().decode()
    ct = pc.CONTENT_TYPE_LATEST
    return dict(content=content, content_type=ct)


# http exporter on daemon thread
def start_exporter(port: int):
    return pc.start_http_server(port)


# gateway pusher on daemon thread
# https://info.bilibili.co/pages/viewpage.action?pageId=174310139
def start_pusher(gateway: str, job: str, token: str, grouping_key: dict = None, interval: int = None):
    p = Pusher(gateway, job, token, grouping_key=grouping_key)
    t = threading.Thread(target=p.run, args=(interval,))
    t.daemon = True
    t.start()


def gunicorn_handle_child_exit(server, worker):
    pc_multiprocess.mark_process_dead(worker.pid)
