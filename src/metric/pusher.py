import logging
import os
import time

import prometheus_client as pc

from ..common import register_term_signal
from ..failsafe import failsafe


class Pusher:
    def __init__(self, gateway: str, job: str, token: str, grouping_key: dict = None, interval: int = None):
        grouping_key = grouping_key or {}

        if not grouping_key.get('app'):
            grouping_key['app'] = os.getenv('APP_ID', job)

        if not grouping_key.get('instance'):
            grouping_key['instance'] = os.getenv('POD_NAME', job)

        if not grouping_key.get('env'):
            grouping_key['env'] = os.getenv('DEPLOY_ENV', 'dev')

        def handler(url, method, timeout, headers, data):
            headers.append(['X-Authorization-Pushgateway', token])
            return pc.exposition.default_handler(url, method, timeout, headers, data)

        self.gateway = gateway
        self.job = job
        self.grouping_key = grouping_key
        self.handler = handler
        self.interval = interval

        self.running = False
        register_term_signal(self.exit_signal_handler)

    @failsafe(exceptions=Exception)
    def add_safe(self):
        gateway = self.gateway
        job = self.job
        gk = self.grouping_key
        h = self.handler
        return pc.pushadd_to_gateway(gateway, job, pc.REGISTRY, grouping_key=gk, handler=h)

    def run(self, interval: int = None):
        interval = interval or 5

        self.running = True
        while self.running:
            self.add_safe()
            time.sleep(interval)

    def exit_signal_handler(self, sig, frame):
        """ graceful close """

        logging.warning(f'[pyutils] metric pusher caught signal: signal<{sig}> frame<{frame}>')
        self.running = False
        logging.info('[pyutils] metric pusher stop')
