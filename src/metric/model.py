from typing import <PERSON><PERSON>

from prometheus_client import Counter as PromCounter
from prometheus_client import Gauge as PromGauge
from prometheus_client import Histogram as PromHistogram

from ..common import NoneFilterDict


def _ensure_inf(buckets):
    if buckets is None:
        return buckets

    inf = float('inf')
    if inf not in buckets:
        buckets = buckets + (inf,)
    if -inf not in buckets:
        buckets = (-inf,) + buckets
    return buckets


class _Metric:
    __prom_class__ = None
    __default_buckets__ = None

    def __init__(
            self,
            namespace: str,
            subsystem: str,
            name: str,
            label_names: Tuple[str, ...] = (),
            unit: str = '',
            buckets: Tuple[float, ...] = None,
    ):
        buckets = _ensure_inf(buckets or self.__default_buckets__)
        params = NoneFilterDict(
            namespace=namespace,
            subsystem=subsystem,
            name=name,
            labelnames=label_names,
            unit=unit,
            buckets=buckets,
            documentation='',
        )
        self._metric = self.__prom_class__(**params)

    def clear(self):
        m = self._metric
        return m.clear()


class Counter(_Metric):
    __prom_class__ = PromCounter

    def increase(self, amount: float = 1.0, **labels):
        m = self._metric
        return m.labels(**labels).inc(amount)


class Gauge(_Metric):
    __prom_class__ = PromGauge

    def increase(self, amount: float, **labels):
        m = self._metric
        return m.labels(**labels).inc(amount)

    def decrease(self, amount: float, **labels):
        m = self._metric
        return m.labels(**labels).dec(amount)

    def set(self, amount: float, **labels):
        m = self._metric
        return m.labels(**labels).set(amount)


class Histogram(_Metric):
    __prom_class__ = PromHistogram
    __default_buckets__ = PromHistogram.DEFAULT_BUCKETS

    def observe(self, amount: float, **labels):
        m = self._metric
        return m.labels(**labels).observe(amount)
