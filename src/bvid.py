"""
av bv exchange module
author: z<PERSON><PERSON><PERSON>@bilibili.com

tapd: https://www.tapd.bilibili.co/61759993/prong/stories/view/1161759993001166469?
url_cache_key=6b31ca6cd0e5cd08600bcd9d64ff000d&action_entry_type=story_tree_list
"""

##################################################
# constants
##################################################
# decimal bvid value range: [maskcode + 1, bvid_upper_limit); avid <= maskcode
# decimal avid value range: avid <= maskcode

ALPHABET = 'FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf'
XORCODE = 23442827791579
MASKCODE = 2251799813685247
PREFIX = 'BV1'
UPPER_LIMIT = 4503599627370496
##################################################


def exchange(lbv: list, p1: int, p2: int) -> str:
    """
    exchange two chars in a list of chars, and return it as a string
    """
    s1 = lbv[p1]
    s2 = lbv[p2]
    lbv[p1] = s2
    lbv[p2] = s1
    return ''.join(lbv)


def decimal_convert(num: int, a: str) -> str:
    """
    convert a number to a string according to given alphabet a
    """
    radix = len(a)
    tnum = ''
    while True:
        q = num // radix
        r = num % radix
        tnum = a[r] + tnum
        if q == 0:
            break
        num = q
    return tnum


def convert_to_decimal(num: str, a: str) -> int:
    """
    convert a str to a decimal number according to given alphabet a
    """
    radix = len(a)
    num = list(num)
    num.reverse()
    dnum = 0
    for i in range(len(num)):
        dnum += int(a.index(num[i]) * pow(radix, i))
    return dnum


def parse_avid(avid: int):
    """
    avid check function
    """
    if not isinstance(avid, int) or avid <= 0:
        raise Exception('avid lte zero')
    if avid > MASKCODE:
        raise Exception('avid is too big')


def parse_bvid(bvid):
    """
    bvid check function
    """
    if not isinstance(bvid, str):
        raise Exception('bvid is not string')
    if bvid == '' or all(s == ' ' for s in bvid):
        raise Exception('bvid is empty')
    if bvid.startswith(PREFIX) or bvid.startswith(PREFIX.lower()):
        bvid = bvid[3:]
    if len(bvid) < 9:
        raise Exception('bvid is too small')
    return bvid


def AvToBv(avid):
    """
    convert an avid to a bvid
    """
    parse_avid(avid)
    avid = avid | (MASKCODE + 1)
    avid = avid ^ XORCODE
    bvid = decimal_convert(avid, ALPHABET)
    bvid = exchange(list(bvid), 0, 6)
    bvid = exchange(list(bvid), 1, 4)
    return PREFIX + bvid


def BvToAv(bvid):
    """
    convert a bvid to an avid
    """
    bvid = parse_bvid(bvid)
    for c in bvid:
        if c not in ALPHABET:
            raise Exception('bvid {} is illegal, invalid char: {}'.format(bvid, c))
    bvid = exchange(list(bvid), 0, 6)
    bvid = exchange(list(bvid), 1, 4)
    avid = convert_to_decimal(bvid, ALPHABET)
    if avid >= UPPER_LIMIT:
        raise Exception('bvid is too big')
    if avid < MASKCODE + 1:
        raise Exception('bvid is too small')
    avid = avid & MASKCODE
    avid = avid ^ XORCODE
    if not avid:
        raise Exception('bvid is too small')
    return avid
