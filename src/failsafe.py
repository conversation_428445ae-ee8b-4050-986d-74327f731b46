
"""
usage: a error handler integrating ability to fuse

feature:
- capture assigned exceptions and fallback
- flexible user-defined fallback
- auto mask requests when failure ratio too high
- compatible with both sync function and async function


example:

# capture assigned exceptions
@failsafe(exceptions=(ValueError,), fallback=0)
def func_may_raise_value_error(v)
    raise ValueError
    return v

assert func_may_raise_value_error(1) == 0


# user-defined fallback
@failsafe(exceptions=(ValueError,), fallback=(lambda x: [0]*x))
def func_return_array(v)
    raise ValueError
    return list(range(v))

assert func_return_array(3) == [0, 0, 0]


# for async function
@failsafe(exceptions=(ValueError,), fallback=0)
async def func(v)
    raise ValueError
    return v
"""


import logging
import time
from collections import deque
from functools import update_wrapper
from functools import wraps
from inspect import iscoroutinefunction
from typing import Callable
from typing import Union

from .common import _TypeHint


def _call(func, *args, **kwargs):
    if callable(func):
        return func(*args, **kwargs)
    else:
        return func


def _wrapper_async(self, func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # for log
        detail = dict(func=func, args=args, kwargs=kwargs)

        nonlocal self
        exceptions, fallback = self._exceptions, self._fallback
        masked, masked_at, masked_time = self._masked, self._masked_at, self._masked_time
        now = int(time.time())

        if masked and (now - masked_at) < masked_time:
            logging.info(f'[pyutils] failsafe masked with detail({detail})')
            return _call(fallback, *args, **kwargs)
        try:
            r = await func(*args, **kwargs)
            self._mark_success()
            return r
        except exceptions as e1:
            logging.info(f'[pyutils] failsafe fallback due to exception({e1}) with detail({detail})')
            r = _call(fallback, *args, **kwargs)
            self._mark_failure()
            return r
        except Exception as e2:
            logging.error(f'[pyutils] failsafe failed due to exception({e2}) not in expected exceptions({exceptions}) '
                          f'with detail({detail})', exc_info=True)
            raise

    return wrapper


def _wrapper_sync(self, func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        # for log
        detail = dict(func=func, args=args, kwargs=kwargs)

        nonlocal self
        exceptions, fallback = self._exceptions, self._fallback
        masked, masked_at, masked_time = self._masked, self._masked_at, self._masked_time
        now = int(time.time())

        if masked and (now - masked_at) < masked_time:
            logging.info(f'[pyutils] failsafe masked with detail({detail})')
            return _call(fallback, *args, **kwargs)
        try:
            r = func(*args, **kwargs)
            self._mark_success()
            return r
        except exceptions as e1:
            logging.info(f'[pyutils] failsafe fallback due to exception({e1}) with detail({detail})')
            r = _call(fallback, *args, **kwargs)
            self._mark_failure()
            return r
        except Exception as e2:
            logging.error(f'[pyutils] failsafe failed due to exception({e2}) not in expected exceptions({exceptions}) '
                          f'with detail({detail})', exc_info=True)
            raise

    return wrapper


# support for .direct
class _WrapperWithDirect:
    def __init__(self, func_wrapped, func_origin):
        self._func_wrapped = func_wrapped
        self._func_origin = func_origin

        self.__func__ = func_wrapped
        self.direct = func_origin
        update_wrapper(self, func_wrapped)

    @classmethod
    def _bind(cls, func, target):
        if iscoroutinefunction(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await func(target, *args, **kwargs)
        else:
            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(target, *args, **kwargs)

        return wrapper

    def __get__(self, instance, owner=None):
        func_wrapped = self._func_wrapped
        func_origin = self._func_origin

        wrapper = self._bind(func_wrapped, instance)
        direct = self._bind(func_origin, instance)

        wrapper.direct = direct
        return wrapper

    def __call__(self, *args, **kwargs):
        return self.__func__(*args, **kwargs)


class _FailsafeDecorator:
    def __init__(self, exceptions, fallback):
        self._exceptions = exceptions
        self._fallback = fallback

        self._masked = False
        self._masked_at = 0
        self._masked_time = 20
        self._records = deque(maxlen=100)
        self._threshold = 0.5

    @property
    def _ratio(self):
        records = self._records
        count = len(records)
        if count > 0:
            bad_count = records.count(False)
            return bad_count / count
        else:
            return 0

    def _mark_success(self):
        self._records.append(True)
        if self._masked:
            logging.warning('[pyutils] failsafe recover from masked...')
            self._records.clear()
            self._masked = False

    def _mark_failure(self):
        self._records.append(False)
        if (not self._masked) and (self._ratio > self._threshold):
            logging.warning('[pyutils] failsafe failure ratio too high, masking...')
            self._masked = True
            self._masked_at = int(time.time())

    def __call__(self, func):
        if iscoroutinefunction(func):
            wrapper = _wrapper_async(self, func)
        else:
            wrapper = _wrapper_sync(self, func)

        # func.direct(*args, **kwargs) for direct call
        return _WrapperWithDirect(wrapper, func)


def failsafe(exceptions: _TypeHint.ExceptionClassOrTuple, fallback: Union[object, Callable] = None):
    return _FailsafeDecorator(exceptions=exceptions, fallback=fallback)
