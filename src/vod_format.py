import inspect
import json
import os.path
from dataclasses import dataclass
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar

from .cached_property import cached_property

_T = TypeVar('_T')


def _unique(vfmts: List['VodFormat']):
    s = set()
    r = []
    for vfmt in vfmts:
        id = vfmt.id
        if id not in s:
            s.add(id)
            r.append(vfmt)
    return r


class _BaseModel:
    __table__ = None
    __all_data__ = None

    def __init__(self, *args, **kwargs):
        pass

    @classmethod
    def from_dict(cls, data):
        return cls(**{k: v for k, v in data.items() if k in inspect.signature(cls).parameters})

    @classmethod
    def _matched(cls, item, **kwargs):
        for k, v in kwargs.items():
            if (v is not None) and (v != getattr(item, k, None)):
                return False
        return True

    @classmethod
    def _all(cls: Type[_T]) -> List[_T]:
        if cls.__all_data__ is None:
            d = os.path.dirname(__file__)
            p = os.path.join(d, 'vod_format.json')
            with open(p) as f:
                data = json.loads(f.read())
            cls.__all_data__ = [cls.from_dict(i) for i in data[cls.__table__]]
        return cls.__all_data__

    @classmethod
    def find(cls: Type[_T], **kwargs) -> List[_T]:
        result = []
        for item in cls._all():
            if cls._matched(item, **kwargs):
                result.append(item)
        return result

    @classmethod
    def find_one(cls: Type[_T], **kwargs) -> Optional[_T]:
        for item in cls._all():
            if cls._matched(item, **kwargs):
                return item
        return None


class _BaseEnum:
    unset = ''

    @classmethod
    def all(cls):
        # print(cls.__dict__)
        return [v for k, v in cls.__dict__.items() if not k.startswith('_')] + [cls.unset]


class Container(_BaseEnum):
    flv = 'flv'
    mp4 = 'mp4'
    m4s = 'm4s'
    ts = 'ts'
    bbts = 'bbts'


class VideoCodec(_BaseEnum):
    avc = 'avc'
    hevc = 'hevc'
    av1 = 'av1'


class AudioCodec(_BaseEnum):
    aac = 'aac'
    eac3 = 'eac3'
    flac = 'flac'


class MediaType(_BaseEnum):
    video = 'video'
    audio = 'audio'
    video_audio = 'video_audio'


class XcodeMode(_BaseEnum):
    narrow_band = 'narrow_band'
    narrow_band_phone = 'narrow_band_phone'
    narrow_band_quick = 'narrow_band_quick'
    multi_track = 'multi_track'


class EnhanceMode(_BaseEnum):
    super_resolution = 'super_resolution'


class HdrType(_BaseEnum):
    hdr10 = 'hdr10'
    dolby_vision = 'dolby_vision'


class DolbyAudioType(_BaseEnum):
    dolby_atmos = 'dolby_atmos'
    dolby_digital_plus = 'dolby_digital_plus'


class MappingType(_BaseEnum):
    dash = 'dash'
    proj = 'proj'
    preview = 'preview'
    ott = 'ott'
    hevc = 'hevc'
    av1 = 'av1'
    ts = 'ts'
    encrypt = 'encrypt'
    narrow_band = 'narrow_band'
    narrow_band_phone = 'narrow_band_phone'
    narrow_band_quick = 'narrow_band_quick'


@dataclass(frozen=True)
class TransType:
    mapping_type: str
    reverse: bool = False

    @classmethod
    def dash(cls, *, reverse: bool = False):
        return cls(MappingType.dash, reverse=reverse)

    @classmethod
    def proj(cls, *, reverse: bool = False):
        return cls(MappingType.proj, reverse=reverse)

    @classmethod
    def preview(cls, *, reverse: bool = False):
        return cls(MappingType.preview, reverse=reverse)

    @classmethod
    def ott(cls, *, reverse: bool = False):
        return cls(MappingType.ott, reverse=reverse)

    @classmethod
    def hevc(cls, *, reverse: bool = False):
        return cls(MappingType.hevc, reverse=reverse)

    @classmethod
    def av1(cls, *, reverse: bool = False):
        return cls(MappingType.av1, reverse=reverse)

    @classmethod
    def ts(cls, *, reverse: bool = False):
        return cls(MappingType.ts, reverse=reverse)

    @classmethod
    def encrypt(cls, *, reverse: bool = False):
        return cls(MappingType.encrypt, reverse=reverse)

    @classmethod
    def narrow_band(cls, *, reverse: bool = False):
        return cls(MappingType.narrow_band, reverse=reverse)

    @classmethod
    def narrow_band_phone(cls, *, reverse: bool = False):
        return cls(MappingType.narrow_band_phone, reverse=reverse)

    @classmethod
    def narrow_band_quick(cls, *, reverse: bool = False):
        return cls(MappingType.narrow_band_quick, reverse=reverse)


@dataclass(frozen=True)
class Quality(_BaseModel):
    __table__ = 'quality'
    qn: int
    description: str
    resolution: str
    video_bit_rate: int
    audio_bit_rate: int
    is_vip: bool
    is_audio: bool


@dataclass(frozen=True)
class Mapping(_BaseModel):
    __table__ = 'mapping'
    type: str
    source_format_id: int
    target_format_id: int


@dataclass(frozen=True)
class VodFormat(_BaseModel):
    __table__ = 'format'
    id: int
    # key fields
    qn: int
    container: str
    video_codec: str
    audio_codec: str
    xcode_mode: str
    enhance_mode: str
    is_preview: bool
    is_ott: bool
    is_proj: bool
    is_encrypt: bool

    # detail fields
    media_type: str
    is_normal: bool
    is_open: bool
    hdr_type: str
    dolby_audio_type: str
    frame_rate: int

    @cached_property
    def quality(self):
        qn = self.qn
        return Quality.find_one(qn=qn)

    @cached_property
    def video_bit_rate(self):
        if self.media_type not in [MediaType.video_audio, MediaType.video]:
            return 0
        q = self.quality
        s = {
            VideoCodec.avc: 1,
            VideoCodec.hevc: 0.7,
            VideoCodec.av1: 0.7,
        }[self.video_codec]
        return q.video_bit_rate * s

    @cached_property
    def audio_bit_rate(self):
        if self.media_type not in [MediaType.video_audio, MediaType.audio]:
            return 0
        q = self.quality
        return q.audio_bit_rate

    @cached_property
    def sort_key(self):
        k_qn = self.qn
        k_preview = int(self.is_preview)
        k_ott = int(self.is_ott)
        k_proj = int(self.is_proj)
        k_is_encrypt = int(self.is_encrypt)
        k_container = {
            Container.flv: 1,
            Container.mp4: 2,
            Container.m4s: 3,
            Container.ts: 4,
            Container.bbts: 5,
        }.get(self.container, 0)
        k_video_codec = {
            VideoCodec.avc: 1,
            VideoCodec.hevc: 2,
            VideoCodec.av1: 3,
        }.get(self.video_codec, 0)
        k_audio_codec = {
            AudioCodec.aac: 1,
            AudioCodec.eac3: 2,
            AudioCodec.flac: 3,
        }.get(self.audio_codec, 0)
        k_xcode_mode = {
            XcodeMode.narrow_band: 1,
            XcodeMode.narrow_band_phone: 2,
        }.get(self.xcode_mode, 0)
        k_enhance_mode = {
            EnhanceMode.super_resolution: 1,
        }.get(self.enhance_mode, 0)
        return k_qn, k_enhance_mode, k_xcode_mode, k_proj, k_ott, k_preview, \
            k_video_codec, k_container, k_audio_codec, \
            k_is_encrypt

    @classmethod
    def _transform(cls, vod_format, trans_type: TransType):
        if vod_format is None:
            return []
        assert isinstance(vod_format, cls)

        mapping_type, reverse = trans_type.mapping_type, trans_type.reverse
        fid = vod_format.id
        if reverse:
            ms = Mapping.find(type=mapping_type, target_format_id=fid)
            return [cls.find_one(id=m.source_format_id) for m in ms]
        else:
            ms = Mapping.find(type=mapping_type, source_format_id=fid)
            return [cls.find_one(id=m.target_format_id) for m in ms]

    @classmethod
    def transform(cls, vod_format: Optional['VodFormat'], *trans_types: TransType) -> List['VodFormat']:
        """
        transform by mappings

        fs = VodFormat.transform(f, TransType.dash())
        fs = VodFormat.transform(f, TransType.hevc(), TransType.dash())
        fs = VodFormat.transform(f, TransType.dash(reverse=True))
        """
        if len(trans_types) == 0:
            return []

        tt = trans_types[0]
        fs = cls._transform(vod_format, tt)
        tts = trans_types[1:]
        if len(tts) == 0:
            return fs

        r = []
        for f in fs:
            r.extend(cls.transform(f, *tts))
        return _unique(r)
