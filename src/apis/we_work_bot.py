from . import BaseApi
from ..errors import WeWorkBotError
from ..http import HttpClient


class WeWorkBotApi(BaseApi):
    def __init__(self, client: HttpClient, key: str):
        super().__init__(client=client)
        self._key = key

    def send(self, type: str, content: str):
        """
        type
            - text
            - markdown
        """
        return self._request(
            method='post',
            path='/cgi-bin/webhook/send',
            json_params={
                'msgtype': type,
                type: {
                    'content': content,
                }
            },
        )

    def _request(self, method: str, path: str, *, query_params: dict = None, json_params: dict = None):
        qs = dict(
            key=self._key,
        )
        qs.update(query_params or {})
        resp = super()._request(method, path, query_params=qs, json_params=json_params)
        code = resp.get('errcode', -1)
        if code != 0:
            raise WeWorkBotError(
                method=method, path=path,
                query_params=qs, json_params=json_params,
                message=resp.get('errmsg'), response=resp,
            )
        return resp
