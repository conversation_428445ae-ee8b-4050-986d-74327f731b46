import hashlib
import time
from collections import OrderedDict
from typing import List
from urllib.parse import urlencode

from . import Base<PERSON><PERSON>
from ..common import join_list
from ..errors import BiliMainError
from ..http import HttpClient


class _BiliApi(BaseApi):
    def __init__(self, client: HttpClient, app_key: str, app_secret: str):
        super().__init__(client=client)
        self._app_key = app_key
        self._app_secret = app_secret

    # reference: https://info.bilibili.co/pages/viewpage.action?pageId=162705569
    def _signed_params(self, query_params: dict = None):
        qs = dict(query_params or {})
        qs.update(
            appkey=self._app_key,
            ts=int(time.time()),
        )
        digest = urlencode(OrderedDict(sorted([(k, v) for k, v in qs.items() if v is not None], key=lambda x: x[0])))
        sign = (hashlib.md5((digest + self._app_secret).encode()).hexdigest()).lower()
        qs['sign'] = sign
        return qs

    def _request(self, method: str, path: str, *, query_params: dict = None, json_params: dict = None):
        qs = self._signed_params(query_params)
        resp = super()._request(method, path, query_params=qs, json_params=json_params)
        code = resp.get('code', -1)
        if code != 0:
            raise BiliMainError(
                method=method, path=path,
                query_params=qs, json_params=json_params,
                message=resp.get('message'), response=resp,
            )
        return resp['data']


class ArchiveApi(_BiliApi):
    """ 稿件 C 端 archive-service """
    def get_archive(self, aid: int):
        return self._request(
            method='get',
            path='/x/internal/v2/archive',
            query_params=dict(aid=aid),
        )

    def get_archive_view(self, aid: int):
        """ 包含稿件多 P 信息，在 pages 字段中"""
        return self._request(
            method='get',
            path='/x/internal/v2/archive/view',
            query_params=dict(aid=aid),
        )

    def get_archive_cids(self, aid: int):
        data = self.get_archive_view(aid)
        return [int(row['cid']) for row in data.get('pages', [])]


class ArchiveVideoUpOpenApi(_BiliApi):
    """ 稿件 B 端 videoup-open-service """
    def batch_get_video(self, cids: List[int]):
        resp = self._request(
            method='get',
            path='/x/internal/videoup-open/video/info',
            query_params=dict(ids=join_list(cids)),
        )
        vs = resp.get('videos', {})
        return {int(k): v for k, v in vs.items()}

    def get_video(self, cid: int):
        return self.batch_get_video(cids=[cid]).get(cid) or {}

    def get_video_filename(self, cid: int):
        video = self.get_video(cid)
        return video.get('filename', '')

    def get_video_aid(self, cid: int):
        video = self.get_video(cid)
        return video.get('aid', 0)


class ArchiveVideoUpApi(_BiliApi):
    """
    稿件 B 端 videoup-service
    https://info.bilibili.co/pages/viewpage.action?pageId=3686646
    注意：此 API 只能用于「后台」展示等 QPS 极低的场景
    """
    def all_types(self):
        return self._request(
            method='get',
            path='/videoup/types',
        )

    def get_video(self, cid: int):
        return self._request(
            method='get',
            path='/videoup/cid',
            query_params=dict(cid=cid),
        )

    def get_video_aid(self, cid: int):
        r = self.get_video(cid)
        return r.get('aid', 0)

    def get_archive(self, aid: int):
        return self._request(
            method='get',
            path='/videoup/simplearchive',
            query_params=dict(
                aid=aid,
                mode=3,
            ),
        )


class OttApi(_BiliApi):
    def apply_audit_pgc(self, cid: int, apply_time: int):
        try:
            return self._request(
                method='post',
                path='/x/tv/audit/apply/pgc',
                query_params=dict(
                    cid=cid,
                    apply_time=apply_time,
                ),
            )
        except BiliMainError as e:
            if e.extra_info['response'].get('code', -1) != -404:
                raise e
            return True
