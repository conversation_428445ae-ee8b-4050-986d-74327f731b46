from . import BaseApi
from ..errors import TriggerError


class TriggerApi(BaseApi):
    """
    https://info.bilibili.co/pages/viewpage.action?pageId=111060255
    """
    def _request(self, method, path, *, query_params=None, json_params=None):
        resp = super()._request(method, path, query_params=query_params, json_params=json_params)
        code = resp.get('code', -1)
        if code != 0:
            raise TriggerError(
                method=method, path=path,
                query_params=query_params, json_params=json_params,
                message=resp.get('message'), response=resp,
            )
        return resp

    def push_task(self, task_type: str, cid: int, *, task_params: dict = None):
        return self._request(
            method='post',
            path=f'/api/v1/task/push/{task_type}',
            json_params=dict(
                cid=cid,
                **task_params,
            ),
        )
