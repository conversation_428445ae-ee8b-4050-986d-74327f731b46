import hashlib
import time
import uuid
from collections import OrderedDict
from urllib.parse import urlencode
from urllib.parse import urlparse

from . import BaseApi
from ..http import HttpClient


class UposApi(BaseApi):
    def __init__(self, client: HttpClient, auth_key: str, upsig_secret: str):
        super().__init__(client=client)

        self._auth_key = auth_key
        self._upsig_secret = upsig_secret

    @classmethod
    def _path(cls, uri: str):
        if uri.startswith('upos://'):
            return uri.replace('upos://', '/')
        elif uri.startswith('lfos://'):
            return uri.replace('lfos://', '/')
        elif uri.startswith('http://') or uri.startswith('https://'):
            return urlparse(uri).path
        else:
            return f'/{uri.strip("/")}'

    def url(self, uri: str, *, host: str = None):
        path = self._path(uri)
        host = host or self._client.host
        host = host.rstrip('/')
        return f'{host}{path}'

    def _upsig(self, path: str, query_string: str):
        qs = f'path={path}&{query_string}&secret={self._upsig_secret}'
        return hashlib.md5(qs.encode()).hexdigest().lower()

    def signed_url(
            self, uri: str, *,
            host: str = None,
            deadline: int = None,
            os: str = None,
            gen: str = None,
            drate: int = None,
            trid: str = None,
            uipk: int = None,
    ):
        deadline = deadline if deadline is not None else (int(time.time()) + 60 * 60 * 4)
        os = os if os is not None else 'upos'
        gen = gen if gen is not None else 'pyutils'
        trid = trid if trid is not None else uuid.uuid4().hex
        args = OrderedDict([
            ('deadline', deadline),
            ('gen', gen),
            ('os', os),
            ('trid', trid),
        ])
        if drate is not None:
            args['drate'] = drate
        if uipk is not None:
            args['uipk'] = uipk

        url = self.url(uri, host=host)
        qs = urlencode(args)
        uparams = ','.join(args.keys())
        upsig = self._upsig(self._path(uri), qs)
        return f'{url}?{qs}&uparams={uparams}&upsig={upsig}'

    def _auth_header(self, path):
        auth_key = self._auth_key
        ts = int(time.time())
        access_id = 'k{}'.format(ts)
        auth = dict(
            access_id=access_id,
            timestamp=ts,
            sign=hashlib.md5(f'{access_id}{ts}{path}{auth_key}'.encode()).hexdigest(),
        )
        return urlencode(auth)

    def _request(self, method: str, uri: str, *, query_params: dict = None, json_params: dict = None):
        path = self._path(uri)
        headers = {
            'X-Upos-Auth': self._auth_header(path),
        }
        return super()._request(method, path, headers=headers, query_params=query_params, json_params=json_params)

    def head(self, uri: str, *, debug: bool = None):
        return self._request(
            method='head',
            uri=uri,
            query_params=dict(
                debug=int(debug or 0),
            )
        )

    def _meta_op(self, uri: str, op_method: str, **kwargs):
        return self._request(
            method='post',
            uri=uri,
            query_params=dict(
                r='metaop',
                method=op_method,
            ),
            json_params=kwargs,
        )

    def meta_update(self, uri: str, column: str, value: str):
        return self._meta_op(uri, 'update_col', col=column, val=value)

    def meta_delete_remux_flv(self, uri: str):
        return self._meta_op(uri, 'delete_remux_flv')

    def meta_delete_6kw_flv_mp4(self, uri: str):
        return self._meta_op(uri, 'delete_6kw_flv_mp4')

    def meta_delete_upgcxcode(self, uri: str):
        return self._meta_op(uri, 'delete_upgcxcode')
