import hashlib
import time
from typing import Union

from . import BaseApi
from ..common import get_host_name
from ..errors import DataSourceError
from ..http import HttpClient


class DataSourceApi(BaseApi):
    def __init__(self, client: HttpClient, app_key: str):
        super().__init__(client=client)
        self._app_key = app_key
        self._host_name = get_host_name()

    @classmethod
    def _create_sign(cls, params, secret_key):
        """create sign using md5"""
        params = '&'.join(['%s=%s' % (key, params[key]) for key in sorted(params.keys()) if params[key] is not None])
        content = (params + "&key=" + secret_key).encode()
        sign = hashlib.new('md5', content).hexdigest()
        return {"sign": sign}

    def _signed_params(self, query_params: dict = None):
        qs = dict(query_params or {})
        qs.update(
            hostname=self._host_name,
            timestampe=int(time.time()),
        )
        qs.update(self._create_sign(qs, self._app_key))
        return qs

    def _request(self, method: str, path: str, *, query_params: dict = None, json_params: Union[dict, list] = None):
        qs = self._signed_params(query_params)
        resp = super()._request(method, path, query_params=qs, json_params=json_params)
        code = resp.get('code', -1)
        if code != 0:
            raise DataSourceError(
                method=method, path=path,
                query_params=qs, json_params=json_params,
                message=resp.get('message'), response=resp,
            )
        return resp['info']

    # upgcxcode
    def list_file_metas(self, cid: int, *, res_type: str = None, status: int = None, ver: str = None):
        """
        res_type
            - flv
            - dash

        return: [
            {
                "cid": 10153001,
                "format": 30280,
                "ver": "",
                "vp": 1,
                "md5": "2810bfb0fce20f48f679082693393e36",
                "filesize": 3929242,
                "timelength": 320982,
                "status": 1,  # 对外开放状态
                "res_status": 0,  # 资源位置的删除状态
                "path": "",  # 目前 dash 无 path
                "watermark": 0,
                "rexcode": 2,
                "ctime": "2019-02-14 14:48:16",
                "mtime": "2021-05-21 12:16:21",
            }
        ]
        """
        return self._request(
            method='get',
            path='/v2/videos',
            query_params=dict(
                cid=cid,
                ver=ver,
                res=res_type,
                status=status,
            ),
        )

    def list_dash_metas(self, cid: int):
        return self._request(
            method='get',
            path='/v1/dash/query',
            query_params=dict(
                cid=cid,
            ),
        )

    def disable_status(self, cid: int, format: int, ver: str, status: int):
        """
        将任意状态设置为禁用
        status 必须为禁用状态，status > 100 and status != 127
        """
        return self._request(
            method='post',
            path='/v1/video/status/disable',
            query_params=dict(
                cid=cid,
                format=format,
                ver=ver,
                status=status,
            ),
        )

    def release_flv(self, cid: int, format: int, ver: str):
        """ 将备份状态设置为开放 2/127 => 1 """
        return self._request(
            method='post',
            path='/v1/multi-version/release',
            query_params=dict(
                cid=cid,
                format=format,
                version=ver,
            ),
        )

    def release_dash(self, cid: int, format: int, ver: str):
        """ 将备份状态设置为开放 2/127 => 1 """
        return self._request(
            method='post',
            path='/v1/dash/multi-version/release',
            query_params=dict(
                cid=cid,
                format=format,
                version=ver,
            ),
        )

    def refresh_playurl_cache(self, cid: int):
        return self._request(
            method='post',
            path='/v1/playurl-cache/reset',
            query_params=dict(
                cid=cid,
            ),
        )

    # share video
    def save_share_video(self, cid: int, path: str, filesize: int, md5: str, timelength: int):
        return self._request(
            method='post',
            path='/v1/share/video',
            json_params=dict(
                cid=cid,
                path=path,
                filesize=filesize,
                md5=md5,
                timelength=timelength,
            ),
        )

    def get_share_video(self, cid: int):
        return self._request(
            method='get',
            path=f'/v1/share/video/{cid}',
        )

    # raw video
    def save_raw_video(self, cid: int, type: str, uri: str, *, reason: str = None):
        """
        type
            - xcode
            - replace
        """
        reason = reason or ''
        return self._request(
            method='post',
            path='/v1/raw/video/create',
            json_params=dict(
                cid=cid,
                type=type,
                uri=uri,
                reason=reason,
            ),
        )

    def get_raw_video(self, cid: int, type: str):
        """
        type
            - xcode
            - replace
        """
        return self._request(
            method='get',
            path='/v1/raw/video/query',
            query_params=dict(
                cid=cid,
                type=type,
            ),
        )

    # xcode mission
    def save_xcode_mission(self, cid: int, type: int, filename: str):
        return self._request(
            method='post',
            path='/v1/xcode/archive/new',
            json_params=[dict(
                cid=cid,
                type=type,
                filename=filename,
            )],
        )

    def list_xcode_missions(self, cid: int, *, type: int = None):
        return self._request(
            method='get',
            path='/v1/xcode/archive/query',
            query_params=dict(
                cid=cid,
                type=type,
            ),
        ).get('1', [])
