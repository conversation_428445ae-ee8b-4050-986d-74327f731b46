import logging
import time

from . import BaseApi
from ..errors import BvcflowFopError
from ..errors import BvcflowFopWaitTimeoutError
from ..http import HttpClient


class BvcflowExecutorApi(BaseApi):
    def __init__(self, client: HttpClient):
        super().__init__(client=client)

        self._info_path = '/'
        self._info_r = 'info'

    def ctx_get(self, filename: str, key: str):
        return self._request(
            method='get',
            path=self._info_path,
            query_params=dict(
                r=self._info_r,
                method='ctx_get',
                filename=filename,
                key=key,
            ),
        )

    def ctx_set(self, filename: str, key: str, value):
        return self._request(
            method='post',
            path=self._info_path,
            query_params=dict(
                r=self._info_r,
                method='ctx_set',
                filename=filename,
                key=key,
            ),
            json_params=dict(
                val=value,
            ),
        )

    def ctx_hset(self, filename: str, key: str, field_key: str, field_value: str):
        return self._request(
            method='post',
            path=self._info_path,
            query_params=dict(
                r=self._info_r,
                method='ctx_hset',
                filename=filename,
                key=key,
            ),
            json_params=dict(
                field=field_key,
                val=field_value,
            )
        )


class BvcflowDriverApi(BaseApi):
    def __init__(self, client: HttpClient):
        super().__init__(client=client)

    def init(self, profile: str, ctx: dict = None):
        ctx = ctx or {}
        ctx_params = {}
        for k, v in ctx.items():
            if not k.startswith('ctx_'):
                key = f'ctx_{k}'
            else:
                key = k
            if isinstance(v, (dict, list)):
                value = v
            else:
                value = str(v)
            ctx_params[key] = value
        return self._request(
            method='post',
            path='/',
            query_params=dict(
                r='state',
                method='init',
            ),
            json_params=dict(
                profile=profile,
                **ctx_params,
            ),
        )


class BvcflowFopApi(BaseApi):
    def __init__(self, client: HttpClient):
        super().__init__(client=client)

    def _request(self, method: str, path: str, *, query_params: dict = None, json_params: dict = None):
        resp = super()._request(method, path, query_params=query_params, json_params=json_params)
        code = resp.get('code', -1)
        if code != 0:
            raise BvcflowFopError(
                method=method, path=path,
                query_params=query_params, json_params=json_params,
                message=resp.get('message'), response=resp,
            )
        return resp['data']

    def get(self, uri: str, fop_name: str):
        """ fop get readonly """
        resp = self._request(
            method='post',
            path='/api/get',
            json_params=dict(
                fop=fop_name,
                upos_uri=uri,
            ),
        )
        return resp[0]

    def set(self, uri: str, fop_name: str, fop_value):
        return self._request(
            method='post',
            path='/api/save',
            json_params=dict(
                fop=fop_name,
                upos_uri=uri,
                val=fop_value,
            ),
        )

    def run(self, uri: str, fop_name: str, *, params: dict = None, callback_url: str = None):
        """ fop put """
        params = params or {}
        return self._request(
            method='post',
            path='/api/create',
            json_params=dict(
                fop=fop_name,
                upos_uri=uri,
                notify=callback_url,
                **params,
            ),
        )

    def get_or_run(
            self, uri: str, fop_name: str, *, params: dict = None,
            wait_interval: int = None, wait_timeout: int = None,
    ):
        v = self.get(uri, fop_name)
        if v != '':
            return v

        logging.info(f'[pyutils] fop get_or_run running: uri<{uri}> fop_name<{fop_name}> params<{params}>')
        self.run(uri, fop_name, params=params)

        # waiting for complete
        wait_interval = wait_interval or 10
        wait_timeout = wait_timeout or 1000
        end_time = time.time() + wait_timeout
        while True:
            logging.info(
                f'[pyutils] fop get_or_run waiting: '
                f'uri<{uri}> fop_name<{fop_name}> params<{params}> wait_interval<{wait_interval}>'
            )
            time.sleep(wait_interval)
            v = self.get(uri, fop_name)
            if v != '':
                return v
            if time.time() > end_time:
                raise BvcflowFopWaitTimeoutError(uri=uri, fop_name=fop_name, params=params, wait_timeout=wait_timeout)
