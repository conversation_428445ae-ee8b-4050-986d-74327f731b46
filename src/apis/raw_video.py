import hashlib
import logging
import time

from . import BaseApi
from ..errors import RawVideoError
from ..errors import RawVideoWaitTimeoutError
from ..http import HttpClient


class RawVideoApi(BaseApi):
    """
    https://info.bilibili.co/pages/viewpage.action?pageId=125978492

    prefer: '', 'yase_not_decoded', 'origin'
    """
    def __init__(self, client: HttpClient, app_key: str, app_secret: str):
        super().__init__(client=client)
        self._app_key = app_key
        self._app_secret = app_secret

    def _signed_params(self, query_params: dict = None):
        qs = dict(query_params or {})
        qs.update(
            appkey=self._app_key,
            ts=int(time.time()),
        )
        digest = '&'.join(sorted(['{}={}'.format(k, v) for k, v in list(qs.items()) if v is not None]))
        sign = (hashlib.md5((digest + self._app_secret).encode()).hexdigest()).lower()
        qs['sign'] = sign
        return qs

    def _request(self, method: str, path: str, *, query_params: dict = None, json_params: dict = None):
        qs = self._signed_params(query_params)
        resp = super()._request(method, path, query_params=qs, json_params=json_params)
        code = resp.get('code', -1)
        if code != 0:
            raise RawVideoError(
                method=method, path=path,
                query_params=qs, json_params=json_params,
                message=resp.get('message'), response=resp,
            )
        return resp['info']

    def query(self, cid: int, *, prefer: str = None):
        return self._request(
            method='get',
            path='/v1/raw-video/query',
            query_params=dict(
                cid=cid,
                prefer=prefer,
            ),
        )

    def prepare(self, cid: int, *, prefer: str = None, callback_url: str = None, force: bool = None, vip: bool = None):
        return self._request(
            method='post',
            path='/v1/raw-video/prepare',
            query_params=dict(
                cid=cid,
                prefer=prefer,
                callback_url=callback_url,
                force=int(force or 0),
                vip=int(vip or 0),
            ),
        )

    def query_or_prepare(
            self, cid: int, *, prefer: str = None, vip: bool = None,
            wait_interval: int = None, wait_timeout: int = None,
    ):
        v = self.query(cid, prefer=prefer)
        if v:
            return v

        logging.info(f'[pyutils] raw_video query_or_prepare running: cid<{cid}> prefer<{prefer}> vip<{vip}>')
        self.prepare(cid, prefer=prefer, vip=vip)

        # waiting for complete
        wait_interval = wait_interval or 10
        wait_timeout = wait_timeout or 60
        end_time = time.time() + wait_timeout
        while True:
            logging.info(
                f'[pyutils] raw_video query_or_prepare waiting: '
                f'cid<{cid}> prefer<{prefer}> vip<{vip}> wait_interval<{wait_interval}>'
            )
            time.sleep(wait_interval)
            v = self.query(cid, prefer=prefer)
            if v:
                return v
            if time.time() > end_time:
                raise RawVideoWaitTimeoutError(cid=cid, prefer=prefer, vip=vip, wait_timeout=wait_timeout)

    def replace(self, cid: int, uri: str, *, reason: str = None):
        return self._request(
            method='post',
            path='/v1/raw-video/replace',
            query_params=dict(
                cid=cid,
                uri=uri,
                reason=reason,
            ),
        )

    def replace_reset(self, cid: int):
        return self._request(
            method='post',
            path='/v1/raw-video/replace/reset',
            query_params=dict(
                cid=cid,
            ),
        )

    def common_query(self, filename: str, *, prefer: str = None):
        return self._request(
            method='get',
            path='/v1/raw-video/common/query',
            query_params=dict(
                filename=filename,
                prefer=prefer,
            ),
        )

    def common_prepare(self, filename: str, *, callback_url: str = None, force: bool = None, vip: bool = None):
        return self._request(
            method='post',
            path='/v1/raw-video/common/prepare',
            query_params=dict(
                filename=filename,
                callback_url=callback_url,
                force=int(force or 0),
                vip=int(vip or 0),
            ),
        )
