# Changelog

- 2023-05-19
  - 服务发现支持按 zone 筛选，优先选择同分区节点
- 2023-05-16
  - http & grpc client 新增 with_config 方法，支持为单个请求调整 config
- 2023-05-12
  - upos 签名支持 uipk
- 2023-05-09
  - 新增 GrpcClient，支持服务发现
  - 新增 aio 版本 grpc & http client
  - 新增 rpcs 包存放 rpc 接口（暂时只有 taishan）
- 2023-04-29
  - 新增 grpc 相关方法，支持服务发现
  - 新增 TaishanClient，支持 asyncio
- 2023-04-28
  - 新增 HttpClient，支持服务发现
  - 合并 pyapis 到 pyutils 中
- 2023-04-04
  - 增加 BillionsLoggerV2（ck log）
  - 清理废弃代码
- 2023-03-29
  - 增加快速窄带高清清晰度
- 2023-01-16
  - 增加 Bask 函数名限制
- 2023-01-16
  - common 模块模块移动至 common.py
- 2023-01-12
  - 更新 readme
  - 初始化 changelog
