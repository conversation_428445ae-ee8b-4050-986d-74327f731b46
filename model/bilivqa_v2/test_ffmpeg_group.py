# -*- coding: utf-8 -*-
'''
* <AUTHOR>
* @version [2024/6/25]
* @description [调用ffmpeg进行解码及完成vqa计算]
* @code [test_ffmpeg_sampling.py]
'''
import argparse
import logging
import os
import subprocess
import time
from pathlib import Path

import cv2
import numpy as np
import pandas as pd
import torch
from torch.utils import data

from model.bilivqa_v2.utils import get_video_info, motion_normalize, spatial_normalize
from source.utils.error_type import *

motion_mean = [0.45, 0.45, 0.45]
motion_std = [0.225, 0.225, 0.225]



def decode_and_resize_once(decode_cmd, h, w, frame_idxes, total_num):
    frame_size = h * w * 3
    clip_res = subprocess.Popen(decode_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, bufsize=frame_size)

    frame_seq = []
    find_num, frame_idx = 0, 0
    while True:
        raw_image = clip_res.stdout.read(int(frame_size))
        if len(raw_image) != frame_size:
            break
        if frame_idx in frame_idxes:
            image = np.frombuffer(raw_image, dtype='uint8').reshape(h, w, 3)
            image = np.array(image)
            img_tensor = torch.from_numpy(image).permute(2, 0, 1)  # CHW
            frame_seq.append(img_tensor)
            find_num += 1
        if find_num >= total_num:
            break
        frame_idx += 1
    clip_res.stdout.close()

    try:
        if len(frame_seq) < total_num:
            last_idx = len(frame_seq)
            for i in range(last_idx, total_num):
                frame_seq.append(frame_seq[last_idx - 1])
    except:
        raise BiliVqaPartialFrameError()

    frame_seq = torch.stack(frame_seq)

    return frame_seq


class VideoFPSMotionSptialRsizeDataset(data.Dataset):
    """Read data from the original dataset for feature extraction"""
    def __init__(self, file_path, ffmpeg_path, hard_decode, time_list, clip_list, key_areas, in_w, in_h, spatial_resolution, temporal_resolution, fps, num_frames, vid_time_s, codec, num_threads=2, fps_th=40):
        super(VideoFPSMotionSptialRsizeDataset, self).__init__()

        self.file_path = file_path
        self.ffmpeg_path = ffmpeg_path
        self.num_frames = num_frames
        self.time_list = time_list
        self.clip_list = clip_list
        self.key_areas = key_areas
        self.in_w = in_w
        self.in_h = in_h
        self.fps = fps
        self.fps_th = fps_th
        self.num_threads = num_threads
        self.vid_time_s = vid_time_s
        self.codec = codec
        self.hard_decode = hard_decode
        self.spatial_resolution = spatial_resolution
        self.temporal_resolution = temporal_resolution

    def __len__(self):
        return len(self.key_areas)

    def __getitem__(self, idx):
        """
        support videos of which <= 270fps
        """
        in_w = self.in_w
        in_h = self.in_h
        fps = self.fps
        fps_th = self.fps_th
        n_frames_of_clip = 32

        key_area_idxes = self.key_areas[idx]
        num_clip = len(key_area_idxes)
        start_time = self.time_list[key_area_idxes[0]]
        start_frame = self.clip_list[key_area_idxes[0]][0]
        sptial_frame_idxes = []
        for i in range(num_clip):
            area_idx = key_area_idxes[i]
            sptial_frame_idxes.append(self.clip_list[area_idx][0] - start_frame)
        
        motion_frame_idxes = self.clip_list[key_area_idxes] - start_frame

        if fps > fps_th:
            find_tag = False
            for interval in range(2, 10):
                if fps / interval <= fps_th:
                    find_tag = True
                    break
            if not find_tag:
                interval = 1
                logging.warning("Warning: Not find suitable interval, pleace check the original video!!!!!!")
            
            new_motion_frame_idxes = []
            for i in range(num_clip):
                new_motion_frame_idxes.append(np.arange(motion_frame_idxes[i][0], motion_frame_idxes[i][0]+interval*n_frames_of_clip, step=interval))
            motion_frame_idxes = np.array(new_motion_frame_idxes)            

        frame_idxes = np.unique(np.array(motion_frame_idxes).flatten())
        list_idxes = np.arange(len(frame_idxes))
        map_dict = dict(zip(frame_idxes, list_idxes))
        sptial_frame_idxes = np.array(sptial_frame_idxes)
        clip_list_idxes = np.vectorize(map_dict.get)(motion_frame_idxes)
        num_motionframe = len(frame_idxes)

        if in_w > in_h:
            new_h = self.spatial_resolution
            new_w = int(new_h / in_h * in_w)
        else:
            new_w = self.spatial_resolution
            new_h = int(new_w / in_w * in_h)
        tem_w = self.temporal_resolution
        tem_h = self.temporal_resolution

        if self.hard_decode and self.codec in ['h264', 'hevc']:
            decode_cmd =  f"{self.ffmpeg_path} -ss {start_time} -hwaccel cuvid -i {self.file_path} -threads {self.num_threads} \
                -vf 'scale_npp=w={tem_w}:h={tem_h}:interp_algo=linear,hwdownload,format=nv12,format=rgb24' -an -f rawvideo -y -loglevel quiet -"
        else:
            decode_cmd =  f"\
                        {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                        -sws_flags bilinear -s {tem_w}x{tem_h} -an -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"

        try:
            motion_clip = decode_and_resize_once(decode_cmd, tem_h, tem_w, motion_frame_idxes, num_motionframe)
        except:
            soft_decode_cmd =  f"\
                        {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                        -sws_flags bilinear -s {tem_w}x{tem_h} -an -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"
            motion_clip = decode_and_resize_once(soft_decode_cmd, tem_h, tem_w, motion_frame_idxes, num_motionframe)
        transformed_motion_tensor = motion_clip[clip_list_idxes.flatten()].unflatten(0, (num_clip, n_frames_of_clip))   # N, 32, CHW
        # transformed_motion_tensor = transformed_motion_tensor.float() / 255.
        # transformed_motion_tensor = motion_normalize(transformed_motion_tensor)  # [-1, 1] distribution

        for i in range(num_clip):
            motion_length = len(transformed_motion_tensor[i])
            if motion_length < n_frames_of_clip:
                for k in range(motion_length, n_frames_of_clip):
                    # Repeat padding last frame
                    transformed_motion_tensor[i] = torch.cat((transformed_motion_tensor[i], transformed_motion_tensor[i][motion_length - 1].unsqueeze(0)))

        # spatial data
        if self.hard_decode and self.codec in ['h264', 'hevc']:
            decode_cmd =  f"{self.ffmpeg_path} -ss {start_time} -hwaccel cuvid -i {self.file_path} -threads {self.num_threads} \
                -vf 'scale_npp=w={new_w}:h={new_h}:interp_algo=linear,hwdownload,format=nv12,format=rgb24' -an -f rawvideo -y -loglevel quiet -"
        else:
            decode_cmd =  f"\
                        {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                        -sws_flags bilinear -s {new_w}x{new_h} -an  -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"

        try:
            transformed_spatial_tensor = decode_and_resize_once(decode_cmd, new_h, new_w, sptial_frame_idxes, num_clip)
        except:
            soft_decode_cmd =  f"\
                                    {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                                    -sws_flags bilinear -s {new_w}x{new_h} -an  -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"
            transformed_spatial_tensor = decode_and_resize_once(soft_decode_cmd, new_h, new_w, sptial_frame_idxes, num_clip)

        return transformed_motion_tensor, transformed_spatial_tensor


class TensorDataset(data.Dataset):
    """Dataset for loading preprocessed tensor data"""
    def __init__(self, motion_list, spatial_list):
        self.motion_list = motion_list
        self.spatial_list = spatial_list
        
    def __len__(self):
        return len(self.motion_list)
        
    def __getitem__(self, idx):
        # 直接返回预处理好的tensor数据
        return self.motion_list[idx], self.spatial_list[idx]


def preprocess_data(file_path, hard_decode, spatial_resolution, temporal_resolution, num_processes=2, num_threads=2):
    scale = 1
    video_read_len_max = 60
    n_frames_of_clip = 32
    ffmpeg_path = "/data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu"

    video_info = get_video_info(file_path)
    # total length (s)
    try:
        vid_time_s = int(float(video_info['format']['duration']))
        if vid_time_s < 1.0:
            raise BiliVqaMetaInfoError('duration')

        video_read_len = min(vid_time_s, video_read_len_max)
        video_read_interval = int((vid_time_s / video_read_len) / scale) * scale  # 均匀取帧
    except:
        raise BiliVqaMetaInfoError('duration')
    
    try:
        in_w = int(video_info["width"])
        in_h = int(video_info["height"])
        if in_w <= 0 or in_h <= 0:
            raise BiliVqaMetaInfoError('resolution')
    except:
        raise BiliVqaMetaInfoError('resolution')

    if in_w > in_h:
        new_h = 510
        new_w = int(new_h / in_h * in_w)
    else:
        new_w = 510
        new_h = int(new_w / in_w * in_h)

    if in_w > 4096 or in_h > 4096 or new_w > 4096 or new_h > 4096:
        error_obj = BiliVqaTRTRuntimeError("The video's resolution exceeded!")
        error_obj.should_retry = False
        raise error_obj
    
    try:
        codec = video_info["codec_name"]
    except:
        raise BiliVqaMetaInfoError('codec')
    
    try:
        numerator, denominator = video_info["avg_frame_rate"].split('/')
    except:
        raise BiliVqaMetaInfoError('frame rate')

    fps = float(numerator) / float(denominator)
    try:
        if 'nb_frames' in video_info:
            num_frames = int(video_info['nb_frames'])
        else:
            cap = cv2.VideoCapture(file_path)
            num_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if num_frames == 0:
            logging.info("Missing total frames in meta info!")
            num_frames = int(float(video_info['format']['duration']) * fps)
    except:
        logging.info("Missing total frames in meta info!")
        num_frames = int(float(video_info['format']['duration']) * fps)


    if num_frames == 0:
        raise BiliVqaMetaInfoError('total frames')

    # analysis key frame architectures
    exp_name = "keyframe"
    ffprobe_cmd = f"ffprobe -loglevel error -select_streams v:0 -show_entries packet=pts_time,flags \
        -of csv=print_section=0 {file_path}" +  " | awk -F',' '/K/ {print $1}'>" + f"{os.path.splitext(file_path)[0]}_{exp_name}.csv"
    os.system(ffprobe_cmd)
    key_timestamps = np.array(pd.read_csv(f"{os.path.splitext(file_path)[0]}_{exp_name}.csv", header=None)).flatten()
    os.remove(f"{os.path.splitext(file_path)[0]}_{exp_name}.csv")

    time_list = []
    clip_frame_idxes, first_frame_idxes  = [], []
    for i in range(video_read_len):
        start_time = i * video_read_interval
        time_list.append(start_time)
        start_frame = int(start_time * fps)
        first_frame_idxes.append(start_frame)
        clip_frame_idxes.append(list(range(start_frame, start_frame+n_frames_of_clip)))
    time_np = np.array(time_list)
    ## deal with equal cases
    seek_idxes = np.searchsorted(key_timestamps, time_np+1e-4)
    insert_idxes = np.unique(seek_idxes)

    # split frame idxes with the same key frame range
    first_frame_idxes = np.array(first_frame_idxes)
    clip_frame_idxes = np.array(clip_frame_idxes)
    clip_frame_idxes[clip_frame_idxes>=num_frames - 1] = num_frames - 2

    key_areas = []
    idxes_numframe = np.arange(0, video_read_len)
    for idx in insert_idxes:
        key_areas.append(idxes_numframe[seek_idxes==idx][:1])

    key_areas_num = len(key_areas)

    transformed_motion_sptial_data_loader = torch.utils.data.DataLoader(VideoFPSMotionSptialRsizeDataset(file_path, ffmpeg_path, hard_decode, \
        time_list, clip_frame_idxes, key_areas, in_w, in_h, spatial_resolution, temporal_resolution, fps, video_read_len, vid_time_s, codec, num_threads), batch_size=1, shuffle=False, num_workers=num_processes)

    motion_sptial_iterator = iter(transformed_motion_sptial_data_loader)

    motion_list, spatial_list = [], []
    for i in range(key_areas_num):
        transformed_motion_tensor, transformed_spatial_tensor = next(motion_sptial_iterator)

        # 在保存前就完成归一化和标准化
        transformed_motion_tensor = transformed_motion_tensor.float() / 255.
        transformed_motion_tensor = motion_normalize(transformed_motion_tensor)
        
        transformed_spatial_tensor = transformed_spatial_tensor.float() / 255.
        transformed_spatial_tensor = spatial_normalize(transformed_spatial_tensor)

        motion_list.append(transformed_motion_tensor)
        spatial_list.append(transformed_spatial_tensor)

    torch.save({
        'motion_list': motion_list,
        'spatial_list': spatial_list
    }, 'mid_tensor.pt')

    tensor_file = Path("mid_tensor.pt")
    mid_file_size = tensor_file.stat().st_size
    logging.info(f"Data preprocessing done, filename: {tensor_file}, size: {mid_file_size / 1024 / 1024:.2f} MB")
    return tensor_file

def pred_video_quality(tensor_file, model, lut_file, pure_detection):
    func_start_time = time.time()
    logging.info(f"开始视频质量预测(原版)，tensor文件: {tensor_file}")
    # load mid-tensor
    tensor_load_start = time.time()
    mid_dict = torch.load(tensor_file)
    motion_list = mid_dict['motion_list']
    spatial_list = mid_dict['spatial_list']
    tensor_load_time = time.time() - tensor_load_start
    logging.info(f"tensor文件加载耗时: {tensor_load_time:.3f}s")

    cal_frames = 0
    fft_energy_seq = []
    # get num of clips which need to calculate
    key_areas_num = len(motion_list)
    q_frames = np.zeros(key_areas_num)
    
    logging.info(f"待处理clip数量: {key_areas_num}")

    if model.trt_logger.msg is not None:
        raise BiliVqaLoadingEngineError(model.trt_logger.msg)
    
    # 创建DataLoader用于数据预处理
    tensor_dataset = TensorDataset(motion_list, spatial_list)
    tensor_dataloader = torch.utils.data.DataLoader(tensor_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 数据预处理和推理阶段耗时统计
    data_preprocess_total_time = 0
    inference_total_time = 0
    pure_detection_total_time = 0
    
    inference_loop_start = time.time()
    for i, (transformed_motion_tensor, transformed_spatial_tensor) in enumerate(tensor_dataloader):
        clip_start_time = time.time()
        
        # DataLoader添加了batch维度，需要去除 (batch_size=1)
        preprocess_start = time.time()
        transformed_motion_tensor = transformed_motion_tensor.squeeze(0)  # 移除batch维度
        transformed_spatial_tensor = transformed_spatial_tensor.squeeze(0)  # 移除batch维度
        
        the_clip_num = transformed_spatial_tensor.size(1)
        preprocess_time = time.time() - preprocess_start
        data_preprocess_total_time += preprocess_time
        
        for j in range(the_clip_num):
            frame_start_time = time.time()
            
            try:
                data_motion_cur = transformed_motion_tensor[:, j].contiguous().numpy()
                input_spatial = transformed_spatial_tensor[:, j].unsqueeze(1).contiguous().numpy()
            except:
                data_motion_cur = last_data_motion_cur
                input_spatial = last_input_spatial

            # pure color content detection
            pure_detection_start = time.time()
            if pure_detection and i in [0, key_areas_num//2, key_areas_num-1] and j == 0:
                ## de-normalization
                motion_frame = (data_motion_cur[0][0].copy().transpose(1, 2, 0) * motion_std + motion_mean) * 255. # HWC
                motion_frame = motion_frame.astype(np.float32)
                grey_frame = cv2.cvtColor(motion_frame, cv2.COLOR_RGB2GRAY)

                grey_tensor = torch.tensor(grey_frame)
                grey_fft = torch.abs(torch.fft.fftshift(torch.fft.fft(grey_tensor)))
                fft_energy = grey_fft.mean().numpy()
                fft_energy_seq.append(fft_energy)
            pure_detection_time = time.time() - pure_detection_start
            pure_detection_total_time += pure_detection_time

            # infer model
            model_inference_start = time.time()
            inputs = [input_spatial, data_motion_cur]
            binding_shape_map = {'input_0': input_spatial.shape,
                                'input_1': data_motion_cur.shape}
            
            y_output = model.inference(inputs, binding_shape_map)
            if model.trt_logger.msg is not None:
                raise BiliVqaTRTRuntimeError(model.trt_logger.msg)
            q_frames[cal_frames+j] = y_output[0]
            model_inference_time = time.time() - model_inference_start
            inference_total_time += model_inference_time

            last_data_motion_cur = data_motion_cur
            last_input_spatial = input_spatial
            
            frame_total_time = time.time() - frame_start_time
            if (cal_frames + j + 1) % 10 == 0:  # 每10帧打印一次详细耗时
                logging.info(f"第{cal_frames+j+1}帧处理完成，单帧总耗时: {frame_total_time:.3f}s (推理: {model_inference_time:.3f}s, 纯色检测: {pure_detection_time:.3f}s)")
                
        cal_frames += the_clip_num
        clip_total_time = time.time() - clip_start_time
        logging.info(f"第{i+1}/{key_areas_num}个clip处理完成，clip耗时: {clip_total_time:.3f}s，包含{the_clip_num}帧")

    inference_loop_time = time.time() - inference_loop_start
    logging.info(f"推理循环总耗时: {inference_loop_time:.3f}s")
    logging.info(f"数据预处理总耗时: {data_preprocess_total_time:.3f}s")
    logging.info(f"模型推理总耗时: {inference_total_time:.3f}s")
    logging.info(f"纯色检测总耗时: {pure_detection_total_time:.3f}s")
    logging.info(f"计算帧数: {cal_frames}")
    
    # 后处理阶段
    postprocess_start = time.time()
    ori_score = np.mean(q_frames)
    score_lut_df = pd.read_csv(lut_file)
    score_lut_np = score_lut_df['pred_v2']
    percent_score = np.searchsorted(score_lut_np, ori_score) / len(score_lut_np) * 100
    postprocess_time = time.time() - postprocess_start
    logging.info(f"分数计算耗时: {postprocess_time:.3f}s")

    # judge pure color contents
    pure_tag_start = time.time()
    pure_tag = None
    if pure_detection:
        fft_energy_np = np.array(fft_energy_seq)
        fft_energy_mean = fft_energy_np.mean()
        fft_energy_std = fft_energy_np.std()
        logging.info(f"FFT energy mean: {fft_energy_mean}, std: {fft_energy_std}")

        if fft_energy_mean < 150 and fft_energy_std < 5:
            pure_tag = True
        else:
            pure_tag = False
    pure_tag_time = time.time() - pure_tag_start
    logging.info(f"纯色标签处理耗时: {pure_tag_time:.3f}s")

    func_total_time = time.time() - func_start_time
    logging.info(f"pred_video_quality函数总耗时: {func_total_time:.3f}s")
    logging.info(f"耗时分解, tensor加载: {tensor_load_time:.3f}s ({tensor_load_time/func_total_time*100:.1f}%), 推理循环: {inference_loop_time:.3f}s ({inference_loop_time/func_total_time*100:.1f}%), 后处理: {postprocess_time:.3f}s ({postprocess_time/func_total_time*100:.1f}%)")

    return ori_score, percent_score, pure_tag


if __name__ == '__main__':
    start = time.time()

    parser = argparse.ArgumentParser()
    parser.add_argument('--video_path', type=str)
    parser.add_argument('--result_file', type=str)
    parser.add_argument('--trt_file', type=str, default="/workspace/bilivqa_v2_ckpts/IMDT_v100_cuda113.trt")
    parser.add_argument('--lut_file', type=str, default="/workspace/bilivqa_v2_ckpts/vqa_v2_2_benchmark.csv")
    parser.add_argument('--num_threads', type=int, default=2)
    parser.add_argument('--num_processes', type=int, default=1)
    parser.add_argument('--hard_decode', action="store_true")
    config = parser.parse_args()

    videos, scores, percent_scores = [], [], []
    if os.path.isdir(config.video_path):
        for video in os.listdir(config.video_path):
            score, percent_score = pred_video_quality(config.video_path+'/'+video, config.trt_file, config.lut_file, config.hard_decode, config.num_processes, config.num_threads)
            videos.append(video.split('/')[-1])
            scores.append(score)
            percent_scores.append(percent_score)
    elif os.path.isfile(config.video_path):
        score, percent_score = pred_video_quality(config.video_path, config.trt_file, config.lut_file, config.hard_decode, config.num_processes, config.num_threads)
        videos.append(config.video_path.split('/')[-1])
        scores.append(score)
        percent_scores.append(percent_score)
    else:
        raise ValueError
    if not os.path.exists(config.result_file):
        os.system(r"touch {}".format(config.result_file))

    df = pd.DataFrame({'name': videos, 'ori_score': scores, 'percent_score': percent_scores})
    df.to_csv(config.result_file, index=0)
