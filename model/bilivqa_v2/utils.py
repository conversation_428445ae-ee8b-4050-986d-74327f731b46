# -*- coding: utf-8 -*-
'''
* <AUTHOR>
* @version [2024/6/25]
* @description [bilivqa工具函数]
* @code [utils.py]
'''
import cv2
import sys
import ffmpeg
import subprocess
import numpy as np
from torchvision import transforms
from source.utils.error_type import BiliVqaFullVideoError


spatial_normalize = transforms.Compose([transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])])
motion_normalize = transforms.Compose([transforms.Normalize(mean=[0.45, 0.45, 0.45], std=[0.225, 0.225, 0.225])])

transform_spatial = transforms.Compose([transforms.Resize(510)])
transform_motion = transforms.Compose([transforms.Resize([160, 160])])


def get_video_info(in_file):
    """
    获取视频基本信息
    """
    try:
        probe = ffmpeg.probe(in_file)
        video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        if video_stream is None:
            raise BiliVqaFullVideoError('No video stream found')
        video_stream.update({'format': probe['format']})
        return video_stream
    except ffmpeg.Error as err:
        raise BiliVqaFullVideoError(str(err.stderr, encoding='utf8'))

def ffmpegWorkerSptial(file_path, start_time, in_w, in_h):
    if in_w > in_h:
        new_h = 510
        new_w = int(new_h / in_h * in_w)
    else:
        new_w = 510
        new_h = int(new_w / in_w * in_h)

    first_frame_count = 1

    sptial_size = new_w * new_h * 3
    decode_cmd =  f"\
            /data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu -ss {start_time} -i {file_path} \
            -vframes 1 -sws_flags bilinear -s {new_w}x{new_h} -f rawvideo -pix_fmt rgb24 -an -y -loglevel quiet -"
    sptial_res = subprocess.Popen(decode_cmd, shell=True, stdout=subprocess.PIPE,stderr=subprocess.DEVNULL, bufsize=sptial_size)
    sptial_data = sptial_res.stdout.read()
    sptial_res.wait()
    sptial_res.stdout.close()

    sptial_np = np.frombuffer(sptial_data, np.uint8).reshape([1, new_h, new_w, 3]).transpose([0, 3, 1, 2])

    trainformed_first_frame = sptial_np

    return first_frame_count, trainformed_first_frame

def ffmpegWorkerFPS(file_path, start_time, n_frames_of_clip, in_w, in_h, fps, fps_th=40):
    """
    support videos of which <= 270fps
    """
    if in_w > in_h:
        new_h = 510
        new_w = int(new_h / in_h * in_w)
    else:
        new_w = 510
        new_h = int(new_w / in_w * in_h)

    motion_resize = 160
    first_frame_count = 1

    sptial_size = new_w * new_h * 3
    decode_cmd =  f"\
            /data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu -ss {start_time} -i {file_path} \
            -vframes 1 -sws_flags bilinear -s {new_w}x{new_h} -f rawvideo -pix_fmt rgb24 -an -y -loglevel quiet -"
    sptial_res = subprocess.Popen(decode_cmd, shell=True, stdout=subprocess.PIPE,stderr=subprocess.DEVNULL, bufsize=sptial_size)
    sptial_data = sptial_res.stdout.read()
    sptial_res.wait()
    sptial_res.stdout.close()

    if fps <= fps_th:
        decode_cmd =  f"\
            /data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu -ss {start_time} -i {file_path} \
            -vframes {n_frames_of_clip} -sws_flags bilinear -s {160}x{160} -an -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"
    else:
        find_tag = False
        for interval in range(2, 10):
            if fps / interval <= fps_th:
                find_tag = True
                break
        if not find_tag:
            interval = 1
            print("Warning: Not find suitable interval, pleace check the original video!!!!!!")
            print(file_path)
        decode_cmd =  f"\
            /data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu -ss {start_time} -i {file_path} \
            -vframes {n_frames_of_clip} -vf select='not(mod(n\,{interval}))' -sws_flags bilinear -s {160}x{160} -an -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"
    clip_size = n_frames_of_clip * 160 * 160 * 3
    clip_res = subprocess.Popen(decode_cmd, shell=True, stdout=subprocess.PIPE,stderr=subprocess.DEVNULL, bufsize=clip_size)
    clip_data = clip_res.stdout.read()
    clip_res.wait()
    clip_res.stdout.close()

    # NCHW
    ## if meta info of video is wrong which leads to data loss, return and skip the clip
    try:
        clip_np = np.frombuffer(clip_data, np.uint8).reshape([-1, motion_resize, motion_resize, 3]).transpose([0, 3, 1, 2])
        sptial_np = np.frombuffer(sptial_data, np.uint8).reshape([1, new_h, new_w, 3]).transpose([0, 3, 1, 2])
    except:
        return None, None, None

    transformed_video_clip = clip_np
    trainformed_first_frame = sptial_np

    motion_idx = transformed_video_clip.shape[0]

    if motion_idx < n_frames_of_clip:
        for k in range(motion_idx, n_frames_of_clip):
            # Repeat padding last frame
            transformed_video_clip = np.concatenate((transformed_video_clip, transformed_video_clip[motion_idx - 1][np.newaxis, :]))

    return first_frame_count, transformed_video_clip, trainformed_first_frame

def ffmpegGPUDecodeWorker(file_path, n_frames_of_clip, video_read_len, video_read_interval, fps):
    cap = cv2.VideoCapture(file_path)
    num_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    clip_frame_idxes, first_frame_idxes  = [], []
    for i in range(video_read_len):
        start_time = i * video_read_interval
        start_frame = int(start_time * fps)
        first_frame_idxes.append(start_frame)
        clip_frame_idxes.append(list(range(start_frame, start_frame+n_frames_of_clip)))
    clip_frame_idxes = np.array(clip_frame_idxes)
    clip_frame_idxes[clip_frame_idxes>=num_frames - 1] = num_frames - 2
    frame_idxes = np.unique(np.array(clip_frame_idxes).flatten())
    list_idxes = np.arange(len(frame_idxes))
    map_dict = dict(zip(frame_idxes, list_idxes))
    first_frame_idxes = np.array(first_frame_idxes)
    clip_list_idxes = np.vectorize(map_dict.get)(clip_frame_idxes)

    frame_size = 160 * 160 * 3

    decode_cmd =  f"\
            /data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu -hwaccel cuvid -i {file_path} \
            -vf 'scale_npp=w={160}:h={160}:interp_algo=linear,hwdownload,format=nv12,format=rgb24' -an -f rawvideo -y -loglevel quiet -"
    decode_process = subprocess.Popen(decode_cmd, shell=True, stdout=subprocess.PIPE,stderr=subprocess.DEVNULL, bufsize=frame_size)

    raw_clip = []
    idx = 0
    while True:
        raw_image = decode_process.stdout.read(int(frame_size))
        if len(raw_image) != frame_size:
            break
        if idx in frame_idxes:
            image = np.frombuffer(raw_image, dtype='uint8').reshape(160, 160, 3)
            raw_clip.append(image)
        idx += 1

    raw_clip = np.array(raw_clip).transpose([0, 3, 1, 2])
    transformed_motion_data = raw_clip[clip_list_idxes]
    return transformed_motion_data