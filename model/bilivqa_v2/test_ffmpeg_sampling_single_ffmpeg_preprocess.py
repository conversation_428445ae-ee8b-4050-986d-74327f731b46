# -*- coding: utf-8 -*-
'''
* <AUTHOR>
* @version [2024/6/14]
* @description [调用ffmpeg进行解码及完成vqa计算]
* @code [test_ffmpeg_sampling_single_ffmpeg_preprocess.py]
'''
from genericpath import isdir
import os

import time
import torch
import argparse
import numpy as np
import pandas as pd
import multiprocessing

from utils import get_video_info, ffmpegWorkerSptial, ffmpegWorkerFPS, ffmpegGPUDecodeWorker, spatial_normalize, motion_normalize


def pred_video_quality(file_path, trt_file, lut_file, num_processes=2):
    scale = 1
    video_read_len_max = 60
    n_frames_of_clip = 32

    video_info = get_video_info(file_path)
    # total length (s)
    vid_time_s = int(float(video_info['format']['duration']))
    video_read_len = min(vid_time_s, video_read_len_max)
    video_read_interval = int((vid_time_s / video_read_len) / scale) * scale  # 均匀取帧
    in_w = int(video_info["width"])
    in_h = int(video_info["height"])
    codec = video_info["codec_name"]
    numerator, denominator = video_info["avg_frame_rate"].split('/')
    fps = float(numerator) / float(denominator)

    # multiprocessing opencv version
    transformed_spatial_data = []
    transformed_motion_data = []

    # get transformed data
    frame_idx = 0
    video_read_index = 0

    pool = multiprocessing.Pool(num_processes)
    results = []
    times = []
    if vid_time_s <= 180 and fps <= 40 and codec in ['h264', 'hevc']:
        results.append(pool.apply_async(ffmpegGPUDecodeWorker, (file_path, n_frames_of_clip, video_read_len, video_read_interval, fps)))
        for i in range(video_read_len):
            start_time = i * video_read_interval
            times.append(start_time)
            results.append(pool.apply_async(ffmpegWorkerSptial, (file_path, start_time, in_w, in_h)))
    else:
        for i in range(video_read_len):
            start_time = i * video_read_interval
            times.append(start_time)
            results.append(pool.apply_async(ffmpegWorkerFPS, (file_path, start_time, n_frames_of_clip, in_w, in_h, fps)))
    pool.close()
    # Wait for all processes in the process pool to finish executing
    pool.join()

    if vid_time_s <= 180 and fps <= 40 and codec in ['h264', 'hevc']:
        try:
            for i, res in enumerate(results):
                if i == 0:
                    assert res.successful()
                    transformed_motion_data = res.get()
                else:
                    first_frame_count, single_sptial = res.get()
                    # drop the failed data
                    if single_sptial is None:
                        print(f"Warning!!!!The clip start with {times[i]}s has been dropped!!!!!")
                        video_read_len -= 1
                        continue
                    frame_idx += first_frame_count
                    video_read_index += 1
                    # decode the key frame sucessfully
                    if first_frame_count == 1:
                        transformed_spatial_data.append(single_sptial)
        except:
            print(f"Using hard decoding failed with {file_path}")
            pool = multiprocessing.Pool(num_processes)
            results = []
            times = []
            for i in range(video_read_len):
                start_time = i * video_read_interval
                times.append(start_time)
                results.append(pool.apply_async(ffmpegWorkerFPS, (file_path, start_time, n_frames_of_clip, in_w, in_h, fps)))
            pool.close()
            # Wait for all processes in the process pool to finish executing
            pool.join()
            for i, res in enumerate(results):
                first_frame_count, transformed_video_clip, single_sptial = res.get()
                if single_sptial is None or transformed_video_clip is None:
                    print(f"Warning!!!!The clip start with {times[i]}s has been dropped!!!!!")
                    video_read_len -= 1
                    continue
                frame_idx += first_frame_count
                video_read_index += 1
                # list: [n_frames_of_clip * [H, W, C]]
                transformed_motion_data.append(transformed_video_clip)
                # decode the key frame sucessfully
                if first_frame_count == 1:
                    transformed_spatial_data.append(single_sptial)
        # else:
        #     raise Exception("Hard decoding and soft decoding processes of short-video case are both failed, please check the video infomation!")
    else:
        for i, res in enumerate(results):
            first_frame_count, transformed_video_clip, single_sptial = res.get()
            if single_sptial is None or transformed_video_clip is None:
                print(f"Warning!!!!The clip start with {times[i]}s has been dropped!!!!!")
                video_read_len -= 1
                continue
            frame_idx += first_frame_count
            video_read_index += 1
            # list: [n_frames_of_clip * [H, W, C]]
            transformed_motion_data.append(transformed_video_clip)
            # decode the key frame sucessfully
            if first_frame_count == 1:
                transformed_spatial_data.append(single_sptial)
        if len(transformed_spatial_data) == 0 or len(transformed_motion_data) == 0:
            raise Exception("Soft decoding process of large-video case is failed, please check the video infomation!")

    if frame_idx < video_read_len:
        for i in range(frame_idx, video_read_len):
            transformed_spatial_data.append(transformed_spatial_data[frame_idx - 1])
    if video_read_index < video_read_len:
        for i in range(video_read_index, video_read_len):
            transformed_motion_data.append(transformed_motion_data[video_read_index - 1])

    try:
        from trt_model import TrtBackend
        model = TrtBackend(trt_file, 4096, 4096)
    except:
        raise Exception("TensorRT engine loading failed, please confirm if the GPU matches the trt model!")
    q_frames = np.zeros(video_read_len)
    try:
        for i in range(video_read_len):
            # for ffmpegWorker
            transformed_spatial_tensor = torch.from_numpy(transformed_spatial_data[i]) # 1CHW
            transformed_motion_tensor = torch.from_numpy(transformed_motion_data[i]) # DCHW

            # trasfer to [0, 1]
            transformed_spatial_tensor = transformed_spatial_tensor.float() / 255.
            transformed_motion_tensor = transformed_motion_tensor.float() / 255.

            # normalization
            transformed_spatial_tensor = spatial_normalize(transformed_spatial_tensor)  # [-1, 1] distribution
            transformed_motion_tensor = motion_normalize(transformed_motion_tensor)  # [-1, 1] distribution

            input_spatial = transformed_spatial_tensor.unsqueeze(0).contiguous().numpy()
            data_motion_cur = transformed_motion_tensor.unsqueeze(0).contiguous().numpy()

            # infer model
            inputs = [input_spatial, data_motion_cur]
            binding_shape_map = {'input_0': input_spatial.shape,
                                'input_1': data_motion_cur.shape}
            y_output = model.inference(inputs, binding_shape_map)
            q_frames[i] = y_output[0]
    except:
        raise Exception("Inference failed, please check the memory and GPU memory occupation!")
    ori_score = np.mean(q_frames)
    score_lut_df = pd.read_csv(lut_file)
    score_lut_np = score_lut_df['pred_v2']
    percent_score = np.searchsorted(score_lut_np, ori_score) / len(score_lut_np) * 100

    return ori_score, percent_score


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--video_path', type=str)
    parser.add_argument('--result_file', type=str, help="file path of benchmark's results which used to calculate plcc and srocc")
    parser.add_argument('--trt_file', type=str, default="/workspace/bilivqa_v2_ckpts/IMDT_v100_cuda113.trt")
    parser.add_argument('--lut_file', type=str, default="/workspace/bilivqa_v2_ckpts/vqa_v2_benchmark.csv")
    parser.add_argument('--num_processes', type=int, default=4, help="num of processes while decoding and preprocssing")
    config = parser.parse_args()

    videos, scores, percent_scores = [], [], []
    if os.path.isdir(config.video_path):
        for video in os.listdir(config.video_path):
            score, percent_score = pred_video_quality(config.video_path+'/'+video, config.trt_file, config.lut_file, config.num_processes)
            videos.append(video.split('/')[-1])
            scores.append(score)
            percent_scores.append(percent_score)
    elif os.path.isfile(config.video_path):
        score, percent_score = pred_video_quality(config.video_path, config.trt_file, config.lut_file, config.num_processes)
        videos.append(config.video_path.split('/')[-1])
        scores.append(score)
        percent_scores.append(percent_score)
    else:
        raise ValueError
    if not os.path.exists(config.result_file):
        os.system(r"touch {}".format(config.result_file))

    df = pd.DataFrame({'name': videos, 'ori_score': scores, 'percent_score': percent_scores})
    df.to_csv(config.result_file, index=0)