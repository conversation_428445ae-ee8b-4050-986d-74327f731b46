# -*- coding: utf-8 -*-
'''
* <AUTHOR>
* @version [2024/6/14]
* @description [TensorRT引擎相关]
* @code [trt_model.py]
'''
import logging
import os
import time
import numpy as np
import tensorrt as trt
import pycuda.driver as cuda

from source.utils.error_type import BiliVqaLoadingEngineError, BiliVqaGPUError

try:
    import pycuda.autoinit
except:
    raise BiliVqaGPUError()


class MyLogger(trt.ILogger):
    def __init__(self, min_severity):
        trt.ILogger.__init__(self)
        self.min_severity = min_severity
        self.msg = None

    def log(self, severity, msg):
        if severity <= self.min_severity:
            logging.error(msg)
            self.msg = msg


class HostDeviceMem(object):
    def __init__(self, host_mem, device_mem):
        self.host = host_mem
        self.device = device_mem

    def __str__(self):
        return "Host:\n" + str(self.host) + "\nDevice:\n" + str(self.device)

    def __repr__(self):
        return self.__str__()


class TRTInferenceErr(Exception):
    def __init__(self, e):
        self.code = 1
        self.message = "Trt Inference Error"
        super().__init__(self.message, str(e))


class TrtBackend(object):

    def __init__(self, engine_file_path, max_h=4096, max_w=4096) -> None:
        super().__init__()
        self.trt_logger = MyLogger(trt.Logger.ERROR)
        self.input_names = ["input_0", "input_1"]
        self.output_names = ["output"]
        self.max_h = max_h
        self.max_w = max_w
        
        init_start = time.time()
        try:
            self.engine = self._load_engine(engine_file_path)
        except:
            raise BiliVqaLoadingEngineError(self.trt_logger.msg)
        if self.engine is None:
            raise BiliVqaLoadingEngineError(self.trt_logger.msg)
        load_time = time.time() - init_start
        logging.info(f"TensorRT引擎加载耗时: {load_time:.3f}s")
            
        self.max_batch_size = self.engine.max_batch_size
        self.binding_names = self.input_names + self.output_names
        
        context_start = time.time()
        self.context = self.engine.create_execution_context()
        context_time = time.time() - context_start
        logging.info(f"执行上下文创建耗时: {context_time:.3f}s")
        
        # 延迟分配buffer，使用动态分配策略
        self.buffers = None
        self._buffer_cache = {}  # 缓存不同尺寸的buffer
        
        # 预计算binding信息，避免推理时重复计算
        self._binding_info = self._precompute_binding_info()
        logging.info(f"TensorRT Backend初始化完成，支持动态内存分配")

    def _precompute_binding_info(self):
        """预计算binding信息，避免推理时重复计算"""
        binding_info = {}
        for binding in self.binding_names:
            binding_idx = self.engine[binding]
            if binding_idx != -1:
                binding_info[binding] = {
                    'index': binding_idx,
                    'dtype': trt.nptype(self.engine.get_binding_dtype(binding)),
                    'is_input': self.engine.binding_is_input(binding)
                }
        return binding_info

    def _copy_to_shm(self, engine_file_path):
        """将引擎文件复制到内存文件系统"""
        import shutil
        
        engine_filename = os.path.basename(engine_file_path)
        temp_engine_path = f'/dev/shm/trt_engine_{os.getpid()}_{engine_filename}'
        
        logging.info(f"复制TensorRT引擎到内存文件系统: {engine_file_path} -> {temp_engine_path}")
        copy_start = time.time()
        shutil.copy2(engine_file_path, temp_engine_path)
        copy_time = time.time() - copy_start
        
        # 记录文件大小
        file_size = os.path.getsize(temp_engine_path)
        logging.info(f"引擎文件复制完成，耗时: {copy_time:.3f}s，文件大小: {file_size/1024/1024:.1f}MB")
        
        return temp_engine_path
    
    def _cleanup_temp_file(self, temp_path):
        """清理临时文件"""
        try:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                logging.info("临时引擎文件已清理")
        except Exception as e:
            logging.warning(f"清理临时文件失败: {temp_path}, 错误: {e}")
    
    def _load_engine_from_file(self, engine_file_path):
        """从文件加载引擎"""
        import mmap
        
        load_start = time.time()
        with open(engine_file_path, 'rb') as f:
            with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                with trt.Runtime(self.trt_logger) as runtime:
                    engine = runtime.deserialize_cuda_engine(mm)
        load_time = time.time() - load_start
        logging.info(f"引擎加载完成，耗时: {load_time:.3f}s")
        
        return engine

    def _load_engine(self, engine_file_path):
        """加载TensorRT引擎，优先使用内存文件系统"""
        # 检查/dev/shm是否可用
        shm_available = os.path.exists('/dev/shm') and os.access('/dev/shm', os.W_OK)
        
        if shm_available:
            temp_engine_path = None
            try:
                # 复制到内存文件系统
                temp_engine_path = self._copy_to_shm(engine_file_path)
                
                # 从内存文件系统加载
                engine = self._load_engine_from_file(temp_engine_path)
                
                # 清理临时文件
                self._cleanup_temp_file(temp_engine_path)
                
                return engine
                
            except Exception as e:
                logging.warning(f"使用内存文件系统加载失败，回退到直接加载: {e}")
                # 清理可能存在的临时文件
                if temp_engine_path:
                    self._cleanup_temp_file(temp_engine_path)
        
        # 回退到原始加载方式
        logging.info(f"直接从磁盘加载TensorRT引擎: {engine_file_path}")
        return self._load_engine_from_file(engine_file_path)

    def _get_or_create_buffer(self, spatial_shape, motion_shape):
        """根据实际输入尺寸动态分配或复用buffer"""
        
        # 使用形状作为缓存键
        buffer_key = (spatial_shape, motion_shape)
        
        if buffer_key in self._buffer_cache:
            return self._buffer_cache[buffer_key]
            
        alloc_start = time.time()
        
        # 动态分配buffer
        inputs = []
        output_buffer = []
        output_memory = []
        bindings = []
        stream = cuda.Stream()

        # 根据实际输入形状计算内存大小
        spatial_size = abs(trt.volume(spatial_shape)) * self.max_batch_size
        motion_size = abs(trt.volume(motion_shape)) * self.max_batch_size
        output_size = abs(trt.volume((1,))) * self.max_batch_size
        
        sizes = {'input_0': spatial_size, 'input_1': motion_size, 'output': output_size}
        
        out_idx = 0
        for binding in self.binding_names:
            if binding not in self._binding_info:
                continue
                
            info = self._binding_info[binding]
            size = sizes[binding]
            dtype = info['dtype']

            # Allocate host and device buffers
            host_mem = cuda.pagelocked_empty(size, dtype)
            device_mem = cuda.mem_alloc(host_mem.nbytes)

            if info['is_input']:
                inputs.append(HostDeviceMem(host_mem, device_mem))
                bindings.append(int(device_mem))
            else:
                output_buffer.append(cuda.pagelocked_empty(size, dtype, mem_flags=cuda.host_alloc_flags.DEVICEMAP))
                output_memory.append(cuda.mem_alloc(output_buffer[out_idx].nbytes))
                bindings.append(int(output_memory[out_idx]))
                out_idx += 1

        buffers = (inputs, output_buffer, output_memory, bindings, stream)
        self._buffer_cache[buffer_key] = buffers
        
        alloc_time = time.time() - alloc_start
        logging.info(f"动态分配buffer耗时: {alloc_time:.3f}s, spatial: {spatial_shape}, motion: {motion_shape}")
        
        return buffers

    def inference(self, inf_in_list, binding_shape_map):
        # 获取或创建适合当前输入的buffer
        spatial_shape = inf_in_list[0].shape
        motion_shape = inf_in_list[1].shape
        inputs, output_buffer, output_memory, bindings, stream = self._get_or_create_buffer(spatial_shape, motion_shape)
        
        if binding_shape_map:
            self.context.active_optimization_profile = 0
            for binding_name, shape in binding_shape_map.items():
                if binding_name in self._binding_info:
                    binding_idx = self._binding_info[binding_name]['index']
                    self.context.set_binding_shape(binding_idx, shape)
        # transfer input data to device
        for i in range(len(inputs)):
            inputs[i].host = inf_in_list[i]
            cuda.memcpy_htod_async(inputs[i].device, inputs[i].host, stream)

        # do inference
        self.context.execute_async_v2(bindings=bindings, stream_handle=stream.handle)
        # copy data from device to host
        for idx in range(len(output_buffer)):
            cuda.memcpy_dtoh_async(output_buffer[idx], output_memory[idx], stream)

        stream.synchronize()

        return output_buffer


def main():
    import cv2
    img_path = "/data/wuzhiqiang/code/code/tensort_tutorial/pth2onnx2engine/Akali_Q_0.png"
    img_arr = cv2.imread(img_path)
    img_arr = cv2.resize(img_arr, (256, 256))
    image = []
    image.append(img_arr)
    image.append(img_arr)
    # image.append(img_arr)
    # image.append(img_arr)
    image = np.stack(image)
    image = np.ascontiguousarray(image.transpose(0, 3, 1, 2))
    img_shape = (3, 256, 256)
    engine_path = "/data/wuzhiqiang/code/code/tensort_tutorial/pth2onnx2engine/00022000_model_dynamic.trt"
    trt_model = TrtBackend(engine_path, img_shape)
    outputs = trt_model(image)
    print(len(outputs))
    print(outputs[0].shape)
    print(outputs[0].reshape(5, -1))


if __name__ == "__main__":
    main()