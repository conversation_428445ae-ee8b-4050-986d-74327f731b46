import logging
import os
import subprocess
import time
from pathlib import Path

import cv2
import numpy as np
import pandas as pd
import torch
from torch.utils import data

from model.bilivqa_v2.utils import get_video_info, motion_normalize, spatial_normalize
from source.utils.error_type import *

motion_mean = [0.45, 0.45, 0.45]
motion_std = [0.225, 0.225, 0.225]



def decode_and_resize_once(decode_cmd, h, w, frame_idxes, total_num):
    frame_size = h * w * 3
    clip_res = subprocess.Popen(decode_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.DEVNULL, bufsize=frame_size)

    frame_seq = []
    find_num, frame_idx = 0, 0
    while True:
        raw_image = clip_res.stdout.read(int(frame_size))
        if len(raw_image) != frame_size:
            break
        if frame_idx in frame_idxes:
            image = np.frombuffer(raw_image, dtype='uint8').reshape(h, w, 3)
            image = np.array(image)
            img_tensor = torch.from_numpy(image).permute(2, 0, 1)  # CHW
            frame_seq.append(img_tensor)
            find_num += 1
        if find_num >= total_num:
            break
        frame_idx += 1
    clip_res.stdout.close()

    try:
        if len(frame_seq) < total_num:
            last_idx = len(frame_seq)
            for i in range(last_idx, total_num):
                frame_seq.append(frame_seq[last_idx - 1])
    except:
        raise BiliVqaPartialFrameError()

    frame_seq = torch.stack(frame_seq)

    return frame_seq


class VideoFPSMotionSptialRsizeDataset(data.Dataset):
    """Read data from the original dataset for feature extraction"""
    def __init__(self, file_path, ffmpeg_path, hard_decode, time_list, clip_list, key_areas, in_w, in_h, spatial_resolution, temporal_resolution, fps, num_frames, vid_time_s, codec, num_threads=2, fps_th=40):
        super(VideoFPSMotionSptialRsizeDataset, self).__init__()

        self.file_path = file_path
        self.ffmpeg_path = ffmpeg_path
        self.num_frames = num_frames
        self.time_list = time_list
        self.clip_list = clip_list
        self.key_areas = key_areas
        self.in_w = in_w
        self.in_h = in_h
        self.fps = fps
        self.fps_th = fps_th
        self.num_threads = num_threads
        self.vid_time_s = vid_time_s
        self.codec = codec
        self.hard_decode = hard_decode
        self.spatial_resolution = spatial_resolution
        self.temporal_resolution = temporal_resolution

    def __len__(self):
        return len(self.key_areas)

    def __getitem__(self, idx):
        """
        support videos of which <= 270fps
        """
        in_w = self.in_w
        in_h = self.in_h
        fps = self.fps
        fps_th = self.fps_th
        n_frames_of_clip = 32

        key_area_idxes = self.key_areas[idx]
        num_clip = len(key_area_idxes)
        start_time = self.time_list[key_area_idxes[0]]
        start_frame = self.clip_list[key_area_idxes[0]][0]
        sptial_frame_idxes = []
        for i in range(num_clip):
            area_idx = key_area_idxes[i]
            sptial_frame_idxes.append(self.clip_list[area_idx][0] - start_frame)
        
        motion_frame_idxes = self.clip_list[key_area_idxes] - start_frame

        if fps > fps_th:
            find_tag = False
            for interval in range(2, 10):
                if fps / interval <= fps_th:
                    find_tag = True
                    break
            if not find_tag:
                interval = 1
                logging.warning("Warning: Not find suitable interval, pleace check the original video!!!!!!")
            
            new_motion_frame_idxes = []
            for i in range(num_clip):
                new_motion_frame_idxes.append(np.arange(motion_frame_idxes[i][0], motion_frame_idxes[i][0]+interval*n_frames_of_clip, step=interval))
            motion_frame_idxes = np.array(new_motion_frame_idxes)            

        frame_idxes = np.unique(np.array(motion_frame_idxes).flatten())
        list_idxes = np.arange(len(frame_idxes))
        map_dict = dict(zip(frame_idxes, list_idxes))
        sptial_frame_idxes = np.array(sptial_frame_idxes)
        clip_list_idxes = np.vectorize(map_dict.get)(motion_frame_idxes)
        num_motionframe = len(frame_idxes)

        if in_w > in_h:
            new_h = self.spatial_resolution
            new_w = int(new_h / in_h * in_w)
        else:
            new_w = self.spatial_resolution
            new_h = int(new_w / in_w * in_h)
        tem_w = self.temporal_resolution
        tem_h = self.temporal_resolution

        if self.hard_decode and self.codec in ['h264', 'hevc']:
            decode_cmd =  f"{self.ffmpeg_path} -ss {start_time} -hwaccel cuvid -i {self.file_path} -threads {self.num_threads} \
                -vf 'scale_npp=w={tem_w}:h={tem_h}:interp_algo=linear,hwdownload,format=nv12,format=rgb24' -an -f rawvideo -y -loglevel quiet -"
        else:
            decode_cmd =  f"\
                        {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                        -sws_flags bilinear -s {tem_w}x{tem_h} -an -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"

        try:
            motion_clip = decode_and_resize_once(decode_cmd, tem_h, tem_w, motion_frame_idxes, num_motionframe)
        except:
            soft_decode_cmd =  f"\
                        {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                        -sws_flags bilinear -s {tem_w}x{tem_h} -an -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"
            motion_clip = decode_and_resize_once(soft_decode_cmd, tem_h, tem_w, motion_frame_idxes, num_motionframe)
        transformed_motion_tensor = motion_clip[clip_list_idxes.flatten()].unflatten(0, (num_clip, n_frames_of_clip))   # N, 32, CHW
        # transformed_motion_tensor = transformed_motion_tensor.float() / 255.
        # transformed_motion_tensor = motion_normalize(transformed_motion_tensor)  # [-1, 1] distribution

        for i in range(num_clip):
            motion_length = len(transformed_motion_tensor[i])
            if motion_length < n_frames_of_clip:
                for k in range(motion_length, n_frames_of_clip):
                    # Repeat padding last frame
                    transformed_motion_tensor[i] = torch.cat((transformed_motion_tensor[i], transformed_motion_tensor[i][motion_length - 1].unsqueeze(0)))

        # spatial data
        if self.hard_decode and self.codec in ['h264', 'hevc']:
            decode_cmd =  f"{self.ffmpeg_path} -ss {start_time} -hwaccel cuvid -i {self.file_path} -threads {self.num_threads} \
                -vf 'scale_npp=w={new_w}:h={new_h}:interp_algo=linear,hwdownload,format=nv12,format=rgb24' -an -f rawvideo -y -loglevel quiet -"
        else:
            decode_cmd =  f"\
                        {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                        -sws_flags bilinear -s {new_w}x{new_h} -an  -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"

        try:
            transformed_spatial_tensor = decode_and_resize_once(decode_cmd, new_h, new_w, sptial_frame_idxes, num_clip)
        except:
            soft_decode_cmd =  f"\
                                    {self.ffmpeg_path} -ss {start_time} -i {self.file_path} -threads {self.num_threads} \
                                    -sws_flags bilinear -s {new_w}x{new_h} -an  -f rawvideo -pix_fmt rgb24 -y -loglevel quiet -"
            transformed_spatial_tensor = decode_and_resize_once(soft_decode_cmd, new_h, new_w, sptial_frame_idxes, num_clip)

        return transformed_motion_tensor, transformed_spatial_tensor


class TensorDataset(data.Dataset):
    """Dataset for loading preprocessed tensor data"""
    def __init__(self, motion_list, spatial_list):
        self.motion_list = motion_list
        self.spatial_list = spatial_list
        
    def __len__(self):
        return len(self.motion_list)
        
    def __getitem__(self, idx):
        # 直接返回预处理好的tensor数据
        return self.motion_list[idx], self.spatial_list[idx]


def _validate_video_duration(video_info, vframe_acceleration=False):
    """验证视频时长"""
    try:
        if vframe_acceleration:
            # vframe 加速模式：简化时长计算
            vid_time_s = int(float(video_info['format']['duration']))
        else:
            # 原始模式：考虑开始时间偏移
            vid_time_s = float(video_info['format']['duration'])
            vid_start_s = float(video_info['format']['start_time'])
            vid_time_s = int(vid_time_s - vid_start_s)
            
        if vid_time_s < 1.0:
            raise BiliVqaMetaInfoError('duration')
        return vid_time_s
    except:
        raise BiliVqaMetaInfoError('duration')


def _validate_video_resolution(video_info):
    """验证视频分辨率"""
    try:
        in_w = int(video_info["width"])
        in_h = int(video_info["height"])
        if in_w <= 0 or in_h <= 0:
            raise BiliVqaMetaInfoError('resolution')
    except:
        raise BiliVqaMetaInfoError('resolution')

    if in_w > in_h:
        new_h = 510
        new_w = int(new_h / in_h * in_w)
    else:
        new_w = 510
        new_h = int(new_w / in_w * in_h)

    if in_w > 4096 or in_h > 4096 or new_w > 4096 or new_h > 4096:
        error_obj = BiliVqaTRTRuntimeError("The video's resolution exceeded!")
        error_obj.should_retry = False
        raise error_obj
        
    return in_w, in_h


def _get_video_codec(video_info):
    """获取视频编码格式"""
    try:
        return video_info["codec_name"]
    except:
        raise BiliVqaMetaInfoError('codec')


def _get_video_fps(video_info):
    """获取视频帧率"""
    try:
        numerator, denominator = video_info["avg_frame_rate"].split('/')
        return float(numerator) / float(denominator)
    except:
        raise BiliVqaMetaInfoError('frame rate')


def _get_video_frame_count(video_info, file_path, fps):
    """获取视频总帧数"""
    try:
        if 'nb_frames' in video_info:
            num_frames = int(video_info['nb_frames'])
        else:
            cap = cv2.VideoCapture(file_path)
            num_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if num_frames == 0:
            logging.info("Missing total frames in meta info!")
            num_frames = int(float(video_info['format']['duration']) * fps)
    except:
        logging.info("Missing total frames in meta info!")
        num_frames = int(float(video_info['format']['duration']) * fps)

    if num_frames == 0:
        raise BiliVqaMetaInfoError('total frames')
        
    return num_frames


def _analyze_keyframes(file_path, video_read_len):
    """分析关键帧结构"""
    exp_name = "keyframe"
    ffprobe_cmd = f"ffprobe -loglevel error -select_streams v:0 -show_entries packet=pts_time,flags \
        -of csv=print_section=0 {file_path}" +  " | awk -F',' '/K/ {print $1}'>" + f"{os.path.splitext(file_path)[0]}_{exp_name}.csv"
    os.system(ffprobe_cmd)
    key_timestamps = np.array(pd.read_csv(f"{os.path.splitext(file_path)[0]}_{exp_name}.csv", header=None)).flatten()
    os.remove(f"{os.path.splitext(file_path)[0]}_{exp_name}.csv")

    return key_timestamps


def _compute_frame_indices(vid_time_s, video_read_len, fps, key_timestamps, num_frames, vframe_acceleration=False):
    """计算帧索引和关键区域"""
    n_frames_of_clip = 32
    scale = 1
    video_read_interval = int((vid_time_s / video_read_len) / scale) * scale

    time_list = []
    clip_frame_idxes, first_frame_idxes = [], []
    for i in range(video_read_len):
        start_time = i * video_read_interval
        time_list.append(start_time)
        start_frame = int(start_time * fps)
        first_frame_idxes.append(start_frame)
        clip_frame_idxes.append(list(range(start_frame, start_frame+n_frames_of_clip)))
    
    time_np = np.array(time_list)
    seek_idxes = np.searchsorted(key_timestamps, time_np+1e-4)
    insert_idxes = np.unique(seek_idxes)

    # split frame idxes with the same key frame range
    first_frame_idxes = np.array(first_frame_idxes)
    clip_frame_idxes = np.array(clip_frame_idxes)
    clip_frame_idxes[clip_frame_idxes>=num_frames - 1] = num_frames - 2

    key_areas = []
    idxes_numframe = np.arange(0, video_read_len)
    for idx in insert_idxes:
        if vframe_acceleration:
            # vframe 加速模式：每个 GOP 最多取一个片段
            key_areas.append(idxes_numframe[seek_idxes==idx][:1])
        else:
            # 原始模式：取所有匹配的片段
            key_areas.append(idxes_numframe[seek_idxes==idx])

    return time_list, clip_frame_idxes, key_areas


def _create_dataloader(file_path, ffmpeg_path, hard_decode, time_list, clip_frame_idxes, key_areas, 
                      in_w, in_h, spatial_resolution, temporal_resolution, fps, video_read_len, 
                      vid_time_s, codec, num_threads, num_processes):
    """创建数据加载器"""
    dataset = VideoFPSMotionSptialRsizeDataset(
        file_path, ffmpeg_path, hard_decode, time_list, clip_frame_idxes, key_areas, 
        in_w, in_h, spatial_resolution, temporal_resolution, fps, video_read_len, 
        vid_time_s, codec, num_threads
    )
    dataloader = torch.utils.data.DataLoader(
        dataset, batch_size=1, shuffle=False, num_workers=num_processes
    )
    return dataloader


def _process_tensor_data(dataloader, key_areas_num):
    """处理tensor数据并进行归一化"""
    motion_list, spatial_list = [], []
    iterator = iter(dataloader)
    
    for i in range(key_areas_num):
        transformed_motion_tensor, transformed_spatial_tensor = next(iterator)

        # 归一化和标准化
        transformed_motion_tensor = transformed_motion_tensor.float() / 255.
        transformed_motion_tensor = motion_normalize(transformed_motion_tensor)
        
        transformed_spatial_tensor = transformed_spatial_tensor.float() / 255.
        transformed_spatial_tensor = spatial_normalize(transformed_spatial_tensor)

        motion_list.append(transformed_motion_tensor)
        spatial_list.append(transformed_spatial_tensor)
        
    return motion_list, spatial_list


def preprocess_data_with_timing(file_path, hard_decode, spatial_resolution, temporal_resolution, num_processes=2, num_threads=2, vframe_acceleration=False):
    """带详细耗时统计的数据预处理函数"""
    func_start_time = time.time()
    timing_stats = {}
    
    logging.info(f"VFrame加速模式: {'启用' if vframe_acceleration else '禁用'}")
    
    # 常量配置
    video_read_len_max = 60
    ffmpeg_path = "/data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu"

    # 1. 获取视频基本信息
    info_start = time.time()
    video_info = get_video_info(file_path)
    timing_stats['video_info_time'] = time.time() - info_start

    # 2. 验证视频时长c
    duration_start = time.time()
    vid_time_s = _validate_video_duration(video_info, vframe_acceleration)
    video_read_len = min(vid_time_s, video_read_len_max)
    timing_stats['duration_validation_time'] = time.time() - duration_start
    
    # 3. 验证视频分辨率
    resolution_start = time.time()
    in_w, in_h = _validate_video_resolution(video_info)
    timing_stats['resolution_validation_time'] = time.time() - resolution_start
    
    # 4. 获取编码和帧率信息
    codec_start = time.time()
    codec = _get_video_codec(video_info)
    fps = _get_video_fps(video_info)
    num_frames = _get_video_frame_count(video_info, file_path, fps)
    timing_stats['codec_fps_time'] = time.time() - codec_start
    
    # 5. 分析关键帧
    keyframe_start = time.time()
    key_timestamps = _analyze_keyframes(file_path, video_read_len)
    timing_stats['keyframe_analysis_time'] = time.time() - keyframe_start
    
    # 6. 计算帧索引和关键区域
    indices_start = time.time()
    time_list, clip_frame_idxes, key_areas = _compute_frame_indices(
        vid_time_s, video_read_len, fps, key_timestamps, num_frames, vframe_acceleration
    )
    key_areas_num = len(key_areas)
    timing_stats['frame_indices_time'] = time.time() - indices_start
    timing_stats['key_areas_num'] = key_areas_num
    timing_stats['fps'] = fps
    timing_stats['num_frames'] = num_frames
    
    # 7. 创建数据加载器
    dataloader_start = time.time()
    dataloader = _create_dataloader(
        file_path, ffmpeg_path, hard_decode, time_list, clip_frame_idxes, key_areas,
        in_w, in_h, spatial_resolution, temporal_resolution, fps, video_read_len,
        vid_time_s, codec, num_threads, num_processes
    )
    timing_stats['dataloader_creation_time'] = time.time() - dataloader_start
    
    # 8. 处理tensor数据
    tensor_process_start = time.time()
    motion_list, spatial_list = _process_tensor_data(dataloader, key_areas_num)
    timing_stats['tensor_process_time'] = time.time() - tensor_process_start
    
    # 统计最终的 clip 和帧数
    total_clips = sum(len(motion_tensor[0]) for motion_tensor in motion_list)
    total_frames = sum(motion_tensor[0].shape[0] * motion_tensor[0].shape[1] for motion_tensor in motion_list)
    timing_stats['total_clips'] = total_clips
    timing_stats['total_frames'] = total_frames
    
    # 9. 保存tensor文件
    save_start = time.time()
    # 根据 vframe_acceleration 模式计算 q_frames 的长度
    if vframe_acceleration:
        q_frames_length = key_areas_num  # vframe 加速模式：使用关键区域数量
    else:
        q_frames_length = video_read_len  # 原始模式：使用视频读取长度
    
    torch.save({
        'motion_list': motion_list,
        'spatial_list': spatial_list,
        'q_frames_length': q_frames_length  # 保存 q_frames 数组长度
    }, 'mid_tensor.pt')
    
    tensor_file = Path("mid_tensor.pt")
    mid_file_size = tensor_file.stat().st_size
    timing_stats['tensor_save_time'] = time.time() - save_start
    timing_stats['tensor_file_size_bytes'] = mid_file_size
    timing_stats['func_total_time'] = time.time() - func_start_time
    
    logging.info(f"Data preprocessing done, filename: {tensor_file}, size: {mid_file_size / 1024 / 1024:.2f} MB")
    logging.info(f"Sample统计 - 总clip数: {timing_stats['total_clips']}, 总帧数: {timing_stats['total_frames']}")
    return tensor_file, timing_stats


def preprocess_data(file_path, hard_decode, spatial_resolution, temporal_resolution, num_processes=2, num_threads=2, vframe_acceleration=False):
    """数据预处理函数（保持向后兼容性）"""
    logging.info(f"VFrame加速模式: {'启用' if vframe_acceleration else '禁用'}")
    
    # 常量配置
    video_read_len_max = 60
    ffmpeg_path = "/data/app/archive-qoe/model/bilivqa_v2/ffmpeg_gpu"

    # 1. 获取视频基本信息
    video_info = get_video_info(file_path)
    
    # 2. 验证视频时长
    vid_time_s = _validate_video_duration(video_info, vframe_acceleration)
    video_read_len = min(vid_time_s, video_read_len_max)
    
    # 3. 验证视频分辨率
    in_w, in_h = _validate_video_resolution(video_info)
    
    # 4. 获取编码和帧率信息
    codec = _get_video_codec(video_info)
    fps = _get_video_fps(video_info)
    num_frames = _get_video_frame_count(video_info, file_path, fps)
    
    # 5. 分析关键帧
    key_timestamps = _analyze_keyframes(file_path, video_read_len)
    
    # 6. 计算帧索引和关键区域
    time_list, clip_frame_idxes, key_areas = _compute_frame_indices(
        vid_time_s, video_read_len, fps, key_timestamps, num_frames, vframe_acceleration
    )
    key_areas_num = len(key_areas)
    
    # 7. 创建数据加载器
    dataloader = _create_dataloader(
        file_path, ffmpeg_path, hard_decode, time_list, clip_frame_idxes, key_areas,
        in_w, in_h, spatial_resolution, temporal_resolution, fps, video_read_len,
        vid_time_s, codec, num_threads, num_processes
    )
    
    # 8. 处理tensor数据
    motion_list, spatial_list = _process_tensor_data(dataloader, key_areas_num)
    
    # 9. 保存tensor文件
    # 根据 vframe_acceleration 模式计算 q_frames 的长度
    if vframe_acceleration:
        q_frames_length = key_areas_num  # vframe 加速模式：使用关键区域数量
    else:
        q_frames_length = video_read_len  # 原始模式：使用视频读取长度
    
    torch.save({
        'motion_list': motion_list,
        'spatial_list': spatial_list,
        'q_frames_length': q_frames_length  # 保存 q_frames 数组长度
    }, 'mid_tensor.pt')
    
    tensor_file = Path("mid_tensor.pt")
    mid_file_size = tensor_file.stat().st_size
    logging.info(f"Data preprocessing done, filename: {tensor_file}, size: {mid_file_size / 1024 / 1024:.2f} MB")
    return tensor_file

def _load_tensor_data(tensor_file):
    """加载tensor数据"""
    mid_dict = torch.load(tensor_file)
    q_frames_length = mid_dict.get('q_frames_length', len(mid_dict['motion_list']))  # 兼容旧版本，默认使用 motion_list 长度
    return mid_dict['motion_list'], mid_dict['spatial_list'], q_frames_length


def _prepare_inference_data(motion_list, spatial_list, model):
    """准备推理数据和验证模型"""
    if model.trt_logger.msg is not None:
        raise BiliVqaLoadingEngineError(model.trt_logger.msg)
    
    tensor_dataset = TensorDataset(motion_list, spatial_list)
    tensor_dataloader = torch.utils.data.DataLoader(tensor_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    return tensor_dataloader, len(motion_list)


def _process_pure_detection(data_motion_cur, fft_energy_seq):
    """处理纯色检测"""
    motion_frame = (data_motion_cur[0][0].copy().transpose(1, 2, 0) * motion_std + motion_mean) * 255.
    motion_frame = motion_frame.astype(np.float32)
    grey_frame = cv2.cvtColor(motion_frame, cv2.COLOR_RGB2GRAY)
    
    grey_tensor = torch.tensor(grey_frame)
    grey_fft = torch.abs(torch.fft.fftshift(torch.fft.fft(grey_tensor)))
    fft_energy = grey_fft.mean().numpy()
    fft_energy_seq.append(fft_energy)


def _run_model_inference(model, input_spatial, data_motion_cur):
    """执行模型推理"""
    inputs = [input_spatial, data_motion_cur]
    binding_shape_map = {
        'input_0': input_spatial.shape,
        'input_1': data_motion_cur.shape
    }
    
    y_output = model.inference(inputs, binding_shape_map)
    if model.trt_logger.msg is not None:
        raise BiliVqaTRTRuntimeError(model.trt_logger.msg)
    
    return y_output[0]


def _calculate_scores(q_frames, lut_file):
    """计算质量分数"""
    ori_score = np.mean(q_frames)
    score_lut_df = pd.read_csv(lut_file)
    score_lut_np = score_lut_df['pred_v2']
    percent_score = np.searchsorted(score_lut_np, ori_score) / len(score_lut_np) * 100
    return ori_score, percent_score


def _judge_pure_color(pure_detection, fft_energy_seq):
    """判断纯色内容"""
    pure_tag = None
    if pure_detection:
        fft_energy_np = np.array(fft_energy_seq)
        fft_energy_mean = fft_energy_np.mean()
        fft_energy_std = fft_energy_np.std()

        if fft_energy_mean < 150 and fft_energy_std < 5:
            pure_tag = True
        else:
            pure_tag = False
        logging.info(f"Pure color detection, fft energy mean: {fft_energy_mean}, std: {fft_energy_std}, pure_tag: {pure_tag}")

    return pure_tag


def pred_video_quality(tensor_file, model, lut_file, pure_detection):
    """视频质量预测主函数

    Returns:
        tuple: (ori_score, percent_score, pure_tag, timing_stats)
        其中timing_stats包含详细的耗时统计信息
    """
    func_start_time = time.time()
    timing_stats = {}
    
    # 1. 加载tensor数据
    tensor_load_start = time.time()
    motion_list, spatial_list, q_frames_length = _load_tensor_data(tensor_file)
    timing_stats['tensor_load_time'] = time.time() - tensor_load_start
    
    # 2. 准备推理数据
    tensor_dataloader, key_areas_num = _prepare_inference_data(motion_list, spatial_list, model)

    # 3. 执行推理循环
    cal_frames = 0
    fft_energy_seq = []
    # 直接使用从 tensor 文件中读取的 q_frames_length
    q_frames = np.zeros(q_frames_length)
    
    data_preprocess_total_time = 0
    inference_total_time = 0
    pure_detection_total_time = 0
    
    inference_loop_start = time.time()
    last_data_motion_cur = None
    last_input_spatial = None
    
    for i, (transformed_motion_tensor, transformed_spatial_tensor) in enumerate(tensor_dataloader):
        # 数据预处理
        preprocess_start = time.time()
        transformed_motion_tensor = transformed_motion_tensor.squeeze(0)
        transformed_spatial_tensor = transformed_spatial_tensor.squeeze(0)
        the_clip_num = transformed_spatial_tensor.size(1)
        data_preprocess_total_time += time.time() - preprocess_start
        
        # 逐帧处理
        for j in range(the_clip_num):
            try:
                data_motion_cur = transformed_motion_tensor[:, j].contiguous().numpy()
                input_spatial = transformed_spatial_tensor[:, j].unsqueeze(1).contiguous().numpy()
            except:
                data_motion_cur = last_data_motion_cur
                input_spatial = last_input_spatial

            # 纯色检测
            pure_detection_start = time.time()
            key_areas_num = len(motion_list)  # 获取关键区域数量
            if pure_detection and i in [0, key_areas_num//2, key_areas_num-1] and j == 0:
                _process_pure_detection(data_motion_cur, fft_energy_seq)
            pure_detection_total_time += time.time() - pure_detection_start

            # 模型推理 - 根据 q_frames_length 推断加速模式
            model_inference_start = time.time()
            if q_frames_length == key_areas_num:
                # vframe 加速模式：每个关键区域只存储第一帧的结果
                if j == 0:
                    q_frames[i] = _run_model_inference(model, input_spatial, data_motion_cur)
            else:
                # 原始模式：存储所有帧的结果
                q_frames[cal_frames+j] = _run_model_inference(model, input_spatial, data_motion_cur)
            inference_total_time += time.time() - model_inference_start

            last_data_motion_cur = data_motion_cur
            last_input_spatial = input_spatial
                
        cal_frames += the_clip_num

    timing_stats['total_frames'] = cal_frames * 32
    timing_stats['key_areas_num'] = cal_frames  # 记录关键区域数量用于统计
    timing_stats['inference_loop_time'] = time.time() - inference_loop_start
    timing_stats['data_preprocess_total_time'] = data_preprocess_total_time
    timing_stats['inference_total_time'] = inference_total_time
    timing_stats['pure_detection_total_time'] = pure_detection_total_time

    # 4. 计算分数
    postprocess_start = time.time()
    ori_score, percent_score = _calculate_scores(q_frames, lut_file)
    timing_stats['postprocess_time'] = time.time() - postprocess_start

    # 5. 判断纯色内容
    pure_tag_start = time.time()
    pure_tag = _judge_pure_color(pure_detection, fft_energy_seq)
    timing_stats['pure_tag_time'] = time.time() - pure_tag_start
    timing_stats['func_total_time'] = time.time() - func_start_time

    return ori_score, percent_score, pure_tag, timing_stats

