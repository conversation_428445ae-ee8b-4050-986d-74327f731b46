from subprocess import PIPE
from subprocess import Popen
import time


def run_cmd(cmd: str):
    proc = Popen([cmd], stdout=PIPE, shell=True)
    try:
        out, err = proc.communicate()
    except Exception as e:
        raise Exception(e)
    finally:
        proc.terminate()

    print(f'stdout' + out.decode())
    if err:
        print(f'stderr' + err.decode())
        raise Exception(err)


def read_csv(path: str):
    with open(path, 'r') as f:
        lines = f.readlines()
    return lines


def run_model(upclone: str, upclone1: str):
    upclone_cmd = f'upclone --config /usr/src/app/upos.conf ' \
                  f'copyto {upclone} ' \
                  f'/data/app/archive-qoe/model/videos/test.mp4 ' \
                  f'--upos-endpoint http://uposgate-vip.bilivideo.com:2280'
    run_cmd(upclone_cmd)
    upclone_cmd1 = f'upclone --config /usr/src/app/upos.conf ' \
                   f'copyto {upclone1} ' \
                   f'/data/app/archive-qoe/model/videos/test1.mp4 ' \
                   f'--upos-endpoint http://uposgate-vip.bilivideo.com:2280'
    run_cmd(upclone_cmd1)

    model_cmd_single = f"CUDA_VISIBLE_DEVICES=0 " \
                       f"python3.7 -u /data/app/archive-qoe/model/test_with_3D_features_bilibili_multiscale.py " \
                       f"--database bilibili " \
                       f"--model_name ResNet_tp_mean_std_ResNet_3D " \
                       f"--datainfo_test /data/app/archive-qoe/model/csvfiles/test.csv " \
                       f"--videos_dir_test /data/app/archive-qoe/model/videos " \
                       f"--num_worker 4 " \
                       f"--gpu_ids '0' " \
                       f"--trained_model /usr/src/app/ckpts/ResNet_tp_mean_std_ResNet_3D_epoch_2_SRCC_0.840902.pth " \
                       f"--output_name /data/app/archive-qoe/model/result.csv"

    model_cmd_multi = f"CUDA_VISIBLE_DEVICES=0,1 " \
                      f"python3.7 -u /data/app/archive-qoe/model/test_with_3D_features_bilibili_multiscale.py " \
                      f"--database bilibili " \
                      f"--model_name ResNet_tp_mean_std_ResNet_3D " \
                      f"--datainfo_test /data/app/archive-qoe/model/csvfiles/test.csv " \
                      f"--videos_dir_test /data/app/archive-qoe/model/videos " \
                      f"--num_worker 4 " \
                      f"--gpu_ids '01' " \
                      f"--trained_model /usr/src/app/ckpts/ResNet_tp_mean_std_ResNet_3D_epoch_2_SRCC_0.840902.pth " \
                      f"--output_name /data/app/archive-qoe/model/result.csv"
    run_cmd(model_cmd_multi)

    with open('/data/app/archive-qoe/model/result.csv', 'r') as f:
        lines = f.readlines()

    score1 = float(lines[1].split(',')[1].split('\n')[0])
    score2 = float(lines[2].split(',')[1].split('\n')[0])
    with open('/data/app/archive-qoe/model/100_3-1-6_test/test_100.txt', 'a') as f:
        f.write(str(score1) + '\r\n' + str(score2) + '\r\n')


if __name__ == '__main__':
    # gpus = torch.cuda.device_count()
    # print('Total GPU: ', gpus)
    csvlines = read_csv('/data/app/archive-qoe/model/100_30106_test/downloads1.csv')
    time1 = time.time()
    for i in range(1, len(csvlines) - 1, 2):
        run_model(csvlines[i].split('\n')[0], csvlines[i + 1].split('\n')[0])
    time2 = time.time()
    used = time2 - time1
    with open('/data/app/archive-qoe/model/100_30106_test/time.txt', 'a') as f:
        res = f'Total Time Used: {str(used)} Seconds\r\n'
        f.write(res)
