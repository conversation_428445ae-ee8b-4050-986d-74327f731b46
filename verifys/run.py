import logging
import os
from subprocess import check_output

from src.log import init_std_log

VERIFY_DIR = os.path.dirname(__file__)


def load_files():
    for f in os.listdir(VERIFY_DIR):
        if f not in ['run.py'] and not f.startswith('__'):
            yield os.path.join('verifys', f)


def check_python3():
    cmd = 'which python3'
    return check_output(cmd, shell=True)


def main():
    init_std_log()
    python_path = check_python3().strip().decode()
    for module_path in load_files():
        logging.info(f'[pyutils] verify run: <{module_path}>')
        cmd = f'{python_path} {module_path}'
        logging.info(f'[pyutils] verify cmd: <{cmd}>')
        check_output(cmd, shell=True)
    logging.info('[pyutils] verify all passed!')


if __name__ == '__main__':
    main()
