import logging

from src.apis.raw_video import RawVideoApi
from src.errors import RawVideoError
from src.http import HttpClient


def main():
    api = RawVideoApi(
        client=HttpClient('http://uat-raw-video.bilibili.co'),
        app_key='48834f0b81124c599a9f6b4d28ac8415',
        app_secret='4efdd06502f34f778b8d3dfb62f2374f',
    )

    cid = 10214111
    r = api.query(cid)
    if r:
        assert r['cid'] == cid

    r = api.prepare(cid)
    assert r

    cid = 999
    try:
        api.query(cid=cid)
    except RawVideoError as e:
        logging.info('test passed... with expected error:({})'.format(e))
    else:
        assert False

    try:
        api.prepare(cid=cid)
    except RawVideoError as e:
        logging.info('test passed... with expected error:({})'.format(e))
    else:
        assert False

    filename = '1210730atyuyanzbpmbsq1ypmqteinvw'
    r = api.common_query(filename)
    assert r


if __name__ == '__main__':
    main()
