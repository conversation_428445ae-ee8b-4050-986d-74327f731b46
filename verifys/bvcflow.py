import random

from src.apis.bvcflow import Bvcflow<PERSON><PERSON><PERSON>or<PERSON>pi
from src.apis.bvcflow import BvcflowFopApi
from src.http import HttpClient


def verify_executor():
    api = BvcflowExecutorApi(client=HttpClient('http://uat-bvcflow-executor3.bilibili.co'))

    r = api.ctx_get('0201013at3q2buccy867972cf4wd73kt', 'cfile_url')
    assert r == 'upos://ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'

    r = api.ctx_get('0200812at28tq5kr9xzrss5gow9z5t48', 'profile')
    assert r == 'ugcupos/bup'

    r = api.ctx_get('020081743b65eef0eec13f2993a6611a', 'profile')
    assert r == 'xlab/no-wm'

    r = api.ctx_get('020081743b65eef0eec13f2993a6611a', 'empty_key')
    assert r == ''

    filename = '0200824at37otiqcy5rfxy2gw8piqeuh'
    key = 'test_key'
    value = 'test_value'
    r = api.ctx_set(filename, key, value)
    assert r
    r = api.ctx_get(filename, key)
    assert r == value

    filename = '0200824at37otiqcy5rfxy2gw8piqeuh'
    key = 'test_key'
    value = {'test': 'test_value'}
    r = api.ctx_set(filename, key, value)
    assert r
    r = api.ctx_get(filename, key)
    assert r == value

    filename = '0200824at37otiqcy5rfxy2gw8piqeuh'
    key = 'test_hset'
    field_key = 'test_field'
    field_value = 'test_field_value'
    r = api.ctx_hset(filename, key, field_key, field_value)
    assert r
    r = api.ctx_get(filename, key)
    assert r[field_key] == field_value

    filename = '0200824at37otiqcy5rfxy2gw8piqeuh'
    key = 'test_hset_2'
    value = {'test_field_1': 'test_field_value_1'}
    field_key = 'test_field_2'
    field_value = 'test_field_value_2'
    r = api.ctx_set(filename, key, value)
    assert r
    r = api.ctx_hset(filename, key, field_key, field_value)
    assert r
    # r = api.ctx_get(filename, key)
    # assert r == {
    #     'test_field_1': 'test_field_value_1',
    #     'test_field_2': 'test_field_value_2',
    # }


def verify_fop():
    api = BvcflowFopApi(client=HttpClient('http://uat-bvcflow-fop.bilibili.co'))

    uri = 'upos://ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'
    r = api.get(uri, 'filesize')
    assert r == 96198814

    r = api.get(uri, 'md5')
    assert r == 'e7a500ca4ee6f23f0617ac27e78ca569'

    r = api.get(uri, 'dash_xcode2')
    assert r == ''

    uri = 'upos://ugcboss/0210329at2xfk8bl18pv4anqlhur7aip.mp4'

    random_num = random.randint(100000, 999999)
    api.set(uri, 'test_fop', random_num)
    r = api.get(uri, 'test_fop')
    assert r == random_num

    api.run(uri, 'test_put_fop', callback_url='http://www.baidu.com')


def main():
    verify_executor()
    verify_fop()


if __name__ == '__main__':
    main()
