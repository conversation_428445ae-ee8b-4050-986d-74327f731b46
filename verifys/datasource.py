from src.apis.datasouce import DataSourceApi
from src.http import HttpClient


def verify_upgcxcode(api):
    r = api.list_file_metas(cid=123)
    assert r == []

    r = api.list_file_metas(cid=10167905)
    assert r

    r = api.disable_status(cid=10167905, format=80, ver='', status=106)
    assert r

    r = api.list_file_metas(cid=10167905, status=1)
    assert all([i['status'] == 1 for i in r])

    r = api.list_file_metas(cid=10167905, ver='')
    assert all([i['ver'] == '' for i in r])

    r = api.list_file_metas(cid=10167905, res_type='dash')
    assert all([i['format'] > 30000 for i in r])

    r = api.list_dash_metas(cid=10167905)
    assert all(i['codecs'] for i in r)


def verify_raw_video(api):
    r = api.save_raw_video(cid=123, type='xcode', uri='upos://tmp/xxx')
    assert r

    # run get share video test.
    r = api.get_raw_video(123, 'xcode')
    assert r["type"] == 'xcode'
    assert r["uri"] == 'upos://tmp/xxx'
    assert r["reason"] == ''


def verify_share_video(api):
    r = api.save_share_video(
        cid=123,
        path='upos://tmp/123.mp4',
        filesize=1024,
        md5='6c4c463e8505b9c9dd1a30377ab0f3b8',
        timelength=60,
    )
    assert r

    r = api.get_share_video(123)
    assert r['path'] == 'upos://tmp/123.mp4'
    assert r['filesize'] == 1024


def verify_xcode_mission(api):
    # api.save_xcode_mission(123, 3, '1221001wni31n1zv09nh51p5qzu6ix2h')
    r = api.list_xcode_missions(cid=123)
    assert r


def main():
    api = DataSourceApi(
        client=HttpClient('http://uat-bili-vod-datasource.bilibili.co'),
        app_key='d5487ea02b97',
    )

    verify_upgcxcode(api)
    verify_raw_video(api)
    verify_share_video(api)
    verify_xcode_mission(api)


if __name__ == '__main__':
    main()
