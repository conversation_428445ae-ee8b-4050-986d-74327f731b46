import time

from src.apis.bili_main import ArchiveApi
from src.apis.bili_main import ArchiveVideoUpApi
from src.apis.bili_main import ArchiveVideoUpOpenApi
from src.apis.bili_main import OttApi
from src.http import HttpClient


def verify_archive():
    api = ArchiveApi(
        client=HttpClient('archive.service'),
        app_key='dd5403f3941cc988',
        app_secret='c3424bed53fd1a99fe76b978a232d0ad',
    )

    r = api.get_archive(520113365)
    assert r['tid'] == 171
    assert r['owner']['mid'] == 27515403

    r = api.get_archive_view(760087444)
    assert r['pages']

    r = api.get_archive_cids(10113302)
    assert 10162088 in r


def verify_archive_video_up_open():
    api = ArchiveVideoUpOpenApi(
        client=HttpClient('http://uat-api.bilibili.co'),
        app_key='dd5403f3941cc988',
        app_secret='c3424bed53fd1a99fe76b978a232d0ad',
    )

    r = api.get_video_filename(10162088)
    assert r == '0190528at1h6gpu3jg0thubw1ok2imyx'

    r = api.get_video_aid(10162088)
    assert r == 10113302

    r = api.batch_get_video([10162088, 700309085])
    assert 10162088 in r
    assert 700309085 in r


def verify_archive_video_up():
    api = ArchiveVideoUpApi(
        client=HttpClient('http://uat-archive.api.bilibili.co'),
        app_key='dd5403f3941cc988',
        app_secret='c3424bed53fd1a99fe76b978a232d0ad',
    )

    r = api.all_types()
    assert r

    r = api.get_video(10162088)
    assert r['cid'] == 10162088

    r = api.get_video_aid(10162088)
    assert r == 10113302

    r = api.get_archive(10113302)
    assert r['mid'] == 111005049


def verify_ott():
    api = OttApi(
        client=HttpClient('http://callback-api.bilibili.cn'),
        app_key='44f85bc37590a00d',
        app_secret='test',
    )
    api.apply_audit_pgc(10162088, int(time.time()))


def main():
    verify_archive()
    verify_archive_video_up_open()
    verify_archive_video_up()
    verify_ott()


if __name__ == '__main__':
    main()
