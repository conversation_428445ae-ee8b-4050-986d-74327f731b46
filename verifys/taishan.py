import asyncio
import time

from src.grpc import GrpcClient
from src.grpc import GrpcConfig
from src.grpc.aio import GrpcAioClient
from src.rpcs.taishan import TaishanAioRpc
from src.rpcs.taishan import TaishanRp<PERSON>


def verify():
    client = TaishanRpc(
        GrpcClient(
            'inf.taishan.proxy',
            config=GrpcConfig(
                discovery_cluster='common',
            ),
        ),
        table='bvc_test_kv',
        token='bvc_test_kv',
    )

    key = 'pyutils:test_key'
    value = 'test_value'
    client.set(key, value)
    r = client.get(key)
    assert r == value

    client.expire(key, 1)
    time.sleep(2)
    r = client.get(key)
    assert r == ''

    # key not existed
    key = 'pyutils:test_key_2'
    r = client.get(key)
    assert r == ''
    client.expire(key, 1)


async def verify_aio_run():
    client = TaishanAioRpc(
        GrpcAioClient(
            'inf.taishan.proxy',
            config=GrpcConfig(
                discovery_cluster='common',
            )
        ),
        table='bvc_test_kv',
        token='bvc_test_kv',
    )

    key = 'pyutils:test_key'
    value = 'test_value'
    await client.set(key, value)
    r = await client.get(key)
    assert r == value

    await client.expire(key, 1)
    time.sleep(2)
    r = await client.get(key)
    assert r == ''

    # key not existed
    key = 'pyutils:test_key_2'
    r = await client.get(key)
    assert r == ''
    await client.expire(key, 1)


def verify_aio():
    loop = asyncio.get_event_loop()
    loop.run_until_complete(verify_aio_run())


def main():
    verify()
    verify_aio()


if __name__ == '__main__':
    main()
