from src.apis.upos import UposApi
from src.http import HttpClient
from src.http import HttpConfig


def main():
    api = UposApi(
        client=HttpClient('http://uat-uposgate.bilibili.co:2280/'),
        auth_key='3c3989e9e0f4e64d9c817be4caf8a9c1',
        upsig_secret='1902_724b86e0b80a18f765386a5285e',
    )
    api_for_head = UposApi(
        client=HttpClient(
            'http://uat-uposgate.bilibili.co:2280/',
            config=HttpConfig(
                allowed_status_codes=(404, 410),
            )
        ),
        auth_key='3c3989e9e0f4e64d9c817be4caf8a9c1',
        upsig_secret='1902_724b86e0b80a18f765386a5285e',
    )

    uri = 'upos://ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'
    assert api.url(uri) == 'http://uat-uposgate.bilibili.co:2280/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4', api.url(uri)
    assert api.url(uri, host='http://*************:2280') \
           == 'http://*************:2280/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'
    assert api.url(uri, host='http://*************:2280/') \
           == 'http://*************:2280/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'

    r = api.signed_url(uri, host='http://upos-hz-uat.bilivideo.com', deadline=1673321766, trid='e6fa17cdea6a4c50a05b25f12574fed4')
    assert r == 'http://upos-hz-uat.bilivideo.com/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4?deadline=1673321766&gen=pyutils&os=upos&trid=e6fa17cdea6a4c50a05b25f12574fed4&uparams=deadline,gen,os,trid&upsig=e59f3663f88d702e9fb9c2953e1f19f1'
    r = api.signed_url(uri, host='http://upos-hz-uat.bilivideo.com/', deadline=1673321766, trid='e6fa17cdea6a4c50a05b25f12574fed4')
    assert r == 'http://upos-hz-uat.bilivideo.com/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4?deadline=1673321766&gen=pyutils&os=upos&trid=e6fa17cdea6a4c50a05b25f12574fed4&uparams=deadline,gen,os,trid&upsig=e59f3663f88d702e9fb9c2953e1f19f1'

    uri = 'http://*************:2280/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'
    assert api.url(uri) == 'http://uat-uposgate.bilibili.co:2280/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4', api.url(uri)
    r = api.signed_url(uri, host='http://upos-hz-uat.bilivideo.com', deadline=1673321766, trid='e6fa17cdea6a4c50a05b25f12574fed4')
    assert r == 'http://upos-hz-uat.bilivideo.com/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4?deadline=1673321766&gen=pyutils&os=upos&trid=e6fa17cdea6a4c50a05b25f12574fed4&uparams=deadline,gen,os,trid&upsig=e59f3663f88d702e9fb9c2953e1f19f1'

    uri = '/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4'
    assert api.url(uri) == 'http://uat-uposgate.bilibili.co:2280/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4', api.url(uri)
    r = api.signed_url(uri, host='http://upos-hz-uat.bilivideo.com', deadline=1673321766, trid='e6fa17cdea6a4c50a05b25f12574fed4')
    assert r == 'http://upos-hz-uat.bilivideo.com/ugcboss/0201013at3q2buccy867972cf4wd73kt.mp4?deadline=1673321766&gen=pyutils&os=upos&trid=e6fa17cdea6a4c50a05b25f12574fed4&uparams=deadline,gen,os,trid&upsig=e59f3663f88d702e9fb9c2953e1f19f1'

    meta = api_for_head.head(uri, debug=True)
    assert meta


if __name__ == '__main__':
    main()
