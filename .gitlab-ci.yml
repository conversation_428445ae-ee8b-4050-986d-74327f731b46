stages:
  - prepare
  - build_docker


variables:
  GIT_DEPTH: 10
  # 默认镜像仓库
  DOCKER_REPOSITORY: hub.bilibili.co/video-bili-vxcode
  DOCKER_REPOSITORY_1: ""
  DOCKER_REPOSITORY_2: ""
  # rider项目名（服务树路径）
  RIDER_PROJECT_NAME_CPU: video.bili-vod.bili-vod-archive-qoe
  RIDER_PROJECT_NAME_GPU: video.bili-vod.bili-vod-archive-qoe-gpu
  # 构建产物保存（如构建产物在根目录下面的./dist/目录，则此处paths填写 - ./dist/）
  ARTIFACTS_PATH: ./


prepare:
  stage: prepare
  tags:
    - rider-shared-shell
  # 构建需要用到的docker镜像
  image: docker-reg.bilibili.co/vxcode-swarm-base:latest
  script:
    # 自定义构建命令
    - echo "prepare docker builds"
  only:
    - tags
  artifacts:
    paths:
      - $ARTIFACTS_PATH
    expire_in: 1 week


build_docker:
  stage: build_docker
  tags:
    - rider-shared-shell
  script:
    - export DOCKER_BRANCH_CPU=$DOCKER_REPOSITORY/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA
    - export DOCKER_TAG_CPU=$DOCKER_REPOSITORY/$RIDER_PROJECT_NAME_CPU:$CI_BUILD_TAG
    - docker build -t $DOCKER_BRANCH_CPU .
    - if [ "$CI_BUILD_TAG" != "" ];then docker tag $DOCKER_BRANCH_CPU $DOCKER_TAG_CPU && docker push $DOCKER_TAG_CPU; python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME $DOCKER_TAG_CPU $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_1" != "" ] && [ "$CI_BUILD_TAG" != "" ];then docker tag $DOCKER_BRANCH_CPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_TAG && docker push $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_TAG;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_CPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_TAG $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_2" != "" ] && [ "$CI_BUILD_TAG" != "" ];then docker tag $DOCKER_BRANCH_CPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_TAG && docker push $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_TAG;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_CPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_TAG $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_1" != "" ] && [ "$CI_BUILD_TAG" == "" ];then docker tag $DOCKER_BRANCH_CPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA && docker push $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_CPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_2" != "" ] && [ "$CI_BUILD_TAG" == "" ];then docker tag $DOCKER_BRANCH_CPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA && docker push $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_CPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_CPU:$CI_COMMIT_SHA $CI_COMMIT_TAG; fi
    - export DOCKER_BRANCH_GPU=$DOCKER_REPOSITORY/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA
    - export DOCKER_TAG_GPU=$DOCKER_REPOSITORY/$RIDER_PROJECT_NAME_GPU:$CI_BUILD_TAG
    - docker build -t $DOCKER_REPOSITORY/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA --build-arg SUB_IMAGE="-gpu" --build-arg RT_IMAGE="nvidia/cuda:10.2-runtime-ubuntu16.04" --build-arg LINUX_TOOLS="" .
    - if [ "$CI_BUILD_TAG" != "" ];then docker tag $DOCKER_BRANCH_GPU $DOCKER_TAG_GPU && docker push $DOCKER_TAG_GPU; python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME $DOCKER_TAG_GPU $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_1" != "" ] && [ "$CI_BUILD_TAG" != "" ];then docker tag $DOCKER_BRANCH_GPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_TAG && docker push $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_TAG;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_GPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_TAG $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_1" != "" ] && [ "$CI_BUILD_TAG" != "" ];then docker tag $DOCKER_BRANCH_GPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_TAG && docker push $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_TAG;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_GPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_TAG $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_1" != "" ] && [ "$CI_BUILD_TAG" == "" ];then docker tag $DOCKER_BRANCH_GPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA && docker push $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_GPU $DOCKER_REPOSITORY_1/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA $CI_COMMIT_TAG; fi
    - if [ "$DOCKER_REPOSITORY_2" != "" ] && [ "$CI_BUILD_TAG" == "" ];then docker tag $DOCKER_BRANCH_GPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA && docker push $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA;python /home/<USER>/rider.py $CI_JOB_ID $CI_COMMIT_REF_NAME $CI_COMMIT_SHA "$CI_COMMIT_MESSAGE" $GITLAB_USER_EMAIL $RIDER_PROJECT_NAME_GPU $DOCKER_REPOSITORY_2/$RIDER_PROJECT_NAME_GPU:$CI_COMMIT_SHA $CI_COMMIT_TAG; fi
  dependencies:
    - prepare
  only:
    - tags
