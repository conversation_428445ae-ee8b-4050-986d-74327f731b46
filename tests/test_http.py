import asyncio

from src.errors import HttpRequestError
from src.http import HttpAioClient
from src.http import HttpClient
from src.http import HttpConfig
from tests import BaseTestCase


class TestHttp(BaseTestCase):
    def test_get(self):
        client = HttpClient('https://baidu.com', config=HttpConfig(
            timeout=3,
        ))
        r = client.request('get', '/')
        assert r

    def test_post(self):
        client = HttpClient('https://baidu.com', config=HttpConfig(
            timeout=3,
        ))
        r = client.request('post', '/')
        assert r

    def test_discovery(self):
        client = HttpClient('video.bili-vod.bili-vod-raw-video-api', config=HttpConfig(
            timeout=3,
        ))
        r = client.request('get', '/')
        assert r

    def test_with_config_0(self):
        client = HttpClient('https://baidu.com')
        with self.assertRaises(HttpRequestError):
            r = client.with_config(timeout=0.001).request('get', '/')
        r = client.with_config(retry_times=0).request('get', '/')
        assert r

    def test_with_config_1(self):
        client = HttpClient('https://baidu.com')

        c = client.with_config(timeout=0.001)
        assert c._config.timeout == 0.001

        c = client.with_config(timeout=3).with_config(retry_times=10)
        assert c._config.timeout == 3
        assert c._config.retry_times == 10

        c = client.with_config(timeout=10, retry_times=5, retry_interval=1)
        assert c._config.timeout == 10
        assert c._config.retry_times == 5
        assert c._config.retry_interval == 1


class TestAioHttp(BaseTestCase):
    async def _test_get(self):
        client = HttpAioClient('https://baidu.com', config=HttpConfig(
            timeout=3,
        ))
        r = await client.request('get', '/')
        assert r

    async def _test_post(self):
        client = HttpAioClient('https://baidu.com', config=HttpConfig(
            timeout=3,
        ))
        r = await client.request('post', '/')
        assert r

    async def _test_discovery(self):
        client = HttpAioClient('video.bili-vod.bili-vod-raw-video-api', config=HttpConfig(
            timeout=3,
        ))
        r = await client.request('get', '/')
        assert r

    async def _test_with_config(self):
        client = HttpAioClient('https://baidu.com')
        with self.assertRaises(HttpRequestError):
            r = await client.with_config(timeout=0.001).request('get', '/')
        r = client.with_config(retry_times=0).request('get', '/')
        assert r

    def test_aio(self):
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self._test_get())
        loop.run_until_complete(self._test_post())
        loop.run_until_complete(self._test_discovery())
        loop.run_until_complete(self._test_with_config())
