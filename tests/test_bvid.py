import unittest

from src.bvid import AvToBv
from src.bvid import BvToAv
from src.bvid import exchange


class TestBvid(unittest.TestCase):

    def setUp(self):
        pass

    def test_exchange(self):
        self.assertEqual(exchange(list('abcd'), 0, 1), 'bacd')

    def test_AvToBv(self):
        cases = [
            (327, 'BV1xx411c7QG'),
            (22712, 'BV1Hx411c78B'),
            (2147483647, 'BV1sZ411p7j9'),
            (1, 'BV1xx411c7mQ'),
            (12, 'BV1xx411c7mU'),
            (123, 'BV1xx411c72Q'),
            (124, 'BV1xx411c72f'),
            (2345, 'BV1xx411c7jp'),
            (245678, 'BV1zx411w7dc'),
            (2147483647, 'BV1sZ411p7j9'),
            (2147483648, 'BV1Dr4U127Xc'),
            (2251799813685247, 'BV1aPPTfmvQq'),
        ]
        for case in cases:
            i = case[0]
            e = case[1]
            r = AvToBv(i)
            self.assertEqual(e, r)

    def test_BvToAv(self):
        cases = [
            ('BV1xx411c7QG', 327),
            ('BV1Hx411c78B', 22712),
            ('BV1sZ411p7j9', 2147483647),
            ('S54S1M7Hw', 4294967295),
            ('B3bk2R7gj', 2438234234235),
            ('Ex411U7NE', 9999999),
            ('bv1Ex411U7NE', 9999999),
            ('aPPTfmvQq', 2251799813685247),
        ]
        for case in cases:
            i = case[0]
            e = case[1]
            r = BvToAv(i)
            self.assertEqual(e, r)


if __name__ == '__main__':
    unittest.main()
