import src.vod_format as vf
from tests import BaseTestCase


class TestQuality(BaseTestCase):
    def test_find_1(self):
        qs = vf.Quality.find(qn=16)
        self.assertEqual(len(qs), 1)

        q = qs[0]
        self.assertEqual(q.qn, 16)

    def test_find_2(self):
        qs = vf.Quality.find(qn=-1)
        self.assertEmpty(qs)

    def test_find_3(self):
        qs = vf.Quality.find(resolution='320x240')
        self.assertEqual(len(qs), 1)

    def test_find_4(self):
        qs = vf.Quality.find(resolution='640x360')
        self.assertEqual(len(qs), 2)

    def test_find_one_1(self):
        q = vf.Quality.find_one(qn=16)
        self.assertEqual(q.description, '360P 流畅')
        self.assertEqual(q.resolution, '640x360')
        self.assertEqual(q.is_audio, False)

    def test_find_one_2(self):
        q = vf.Quality.find_one(qn=30216)
        self.assertEqual(q.description, '64K 立体声')
        self.assertEqual(q.resolution, '')
        self.assertEqual(q.is_audio, True)

    def test_find_one_3(self):
        q = vf.Quality.find_one(qn=-1)
        self.assertIsNone(q)

    def test_data_1(self):
        q = vf.Quality.find_one(qn=29)
        self.assertEqual(q.description, '预览')

    def test_data_2(self):
        q = vf.Quality.find_one(qn=30229)
        self.assertEqual(q.description, '预览立体声')


class TestVodFormat(BaseTestCase):
    def test_find_1(self):
        fs = vf.VodFormat.find(id=16)
        self.assertNotEmpty(fs)
        self.assertEqual(len(fs), 1)

        f = fs[0]
        self.assertEqual(f.id, 16)

    def test_find_2(self):
        fs = vf.VodFormat.find(id=-1)
        self.assertEmpty(fs)

    def test_find_one_1(self):
        f = vf.VodFormat.find_one(id=16)
        self.assertIsNotNone(f)

        self.assertEqual(f.id, 16)
        self.assertEqual(f.qn, 16)
        self.assertEqual(f.container, 'mp4')
        self.assertEqual(f.media_type, 'video_audio')
        self.assertEqual(f.video_codec, 'avc')
        self.assertEqual(f.audio_codec, 'aac')
        self.assertEqual(f.frame_rate, 30)
        self.assertEqual(f.is_normal, True)

    def test_find_one_2(self):
        f = vf.VodFormat.find_one(id=-1)
        self.assertIsNone(f)

    def test_quality_1(self):
        f = vf.VodFormat.find_one(id=16)
        q = f.quality
        self.assertEqual(q.qn, 16)

    def test_quality_2(self):
        f = vf.VodFormat.find_one(id=11)
        q = f.quality
        self.assertEqual(q.qn, 16)

    def test_transform_1(self):
        fs = vf.VodFormat.transform(None, vf.TransType.dash())
        self.assertEmpty(fs)

    def test_transform_2(self):
        f = vf.VodFormat.find_one(id=16)
        fs = vf.VodFormat.transform(f, vf.TransType.dash())
        fids = [i.id for i in fs]
        self.assertCountEqual(fids, [30016, 30216])

    def test_transform_3(self):
        f = vf.VodFormat.find_one(id=16)
        fs = vf.VodFormat.transform(f, vf.TransType.hevc())
        fids = [i.id for i in fs]
        self.assertCountEqual(fids, [11])

    def test_transform_4(self):
        f = vf.VodFormat.find_one(id=16)
        fs = vf.VodFormat.transform(f, vf.TransType.hevc(), vf.TransType.dash())
        fids = [i.id for i in fs]
        self.assertCountEqual(fids, [30011])

    def test_transform_5(self):
        f = vf.VodFormat.find_one(id=11)
        fs = vf.VodFormat.transform(f, vf.TransType.hevc(reverse=True))
        fids = [i.id for i in fs]
        self.assertCountEqual(fids, [15, 16])

    def test_transform_6(self):
        f = vf.VodFormat.find_one(id=16)
        fs = vf.VodFormat.transform(f, vf.TransType.dash(), vf.TransType.dash(reverse=True))
        fids = [i.id for i in fs]
        # print(fids)
        self.assertCountEqual(fids, [15, 16])

    def test_transform_7(self):
        f = vf.VodFormat.find_one(id=16)
        fs = vf.VodFormat.transform(f, vf.TransType.dash(), vf.TransType.dash())
        self.assertEmpty(fs)

    def test_transform_8(self):
        f = vf.VodFormat.find_one(id=32)
        fs = vf.VodFormat.transform(f, vf.TransType.dash(), vf.TransType.dash(reverse=True))
        fids = [i.id for i in fs]
        # print(fids)
        self.assertCountEqual(fids, [32])

    def test_sort_key(self):
        fs = vf.VodFormat.find()
        fs = sorted(fs, key=lambda x: x.sort_key)
        self.assertNotEmpty(fs)

        # with open('/tmp/sorted_vf.txt', 'w') as f:
        #     for vfmt in fs:
        #         f.write(f'{vfmt}\n')

    def test_dash_1(self):
        vfmts = vf.VodFormat.find()
        dash_vfmts = [i for i in vfmts if 30000 < i.id < 100000]
        for vfmt in dash_vfmts:
            self.assertEqual(vfmt.container, 'm4s', vfmt)

    def test_dash_2(self):
        dash_vfmts = vf.VodFormat.find(container='m4s', is_encrypt=False, xcode_mode=vf.XcodeMode.unset)
        # 除了杜比音频、无损音频，dash 都应该有映射
        dash_vfmts = [i for i in dash_vfmts if i.qn not in [30250, 30255, 30251]]
        for vfmt in dash_vfmts:
            fs = vf.VodFormat.transform(vfmt, vf.TransType.dash(reverse=True))
            self.assertNotEmpty(fs, vfmt)

    def test_dash_3(self):
        test_cases = [
            (704, [30064, 30280]),
            (720, [30080, 30280]),
        ]
        for tc in test_cases:
            i = tc[0]
            e = tc[1]
            vfmt = vf.VodFormat.find_one(id=i)
            # print(vfmt)
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.dash())
            # print(tvfmts)
            r = [i.id for i in tvfmts]
            self.assertCountEqual(r, e, i)

    def test_dash_4(self):
        vfmts = vf.VodFormat.find()
        dash_vfmts = [i for i in vfmts if (30000 < i.id < 100000)]
        for vfmt in dash_vfmts:
            self.assertEqual(vfmt.container, 'm4s', vfmt)
            if vfmt.media_type == vf.MediaType.video:
                # video dash
                tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.dash(reverse=True))
                for tvfmt in tvfmts:
                    if tvfmt.id not in [704, 720]:
                        self.assertEqual(tvfmt.id + 30000, vfmt.id)
            else:
                # audio dash
                tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.dash(reverse=True))
                for tvfmt in tvfmts:
                    self.assertNotEqual(tvfmt.id + 30000, vfmt.id)

    def test_dash_5(self):
        vfmts = vf.VodFormat.find(container=vf.Container.m4s)
        for vfmt in vfmts:
            if vfmt.quality.is_audio:
                self.assertEqual(vfmt.media_type, vf.MediaType.audio)
                self.assertTrue(vfmt.audio_codec)
                self.assertTrue(vfmt.audio_bit_rate)
                self.assertFalse(vfmt.video_codec)
                self.assertFalse(vfmt.video_bit_rate)
                self.assertFalse(vfmt.frame_rate)
            else:
                self.assertEqual(vfmt.media_type, vf.MediaType.video)
                self.assertTrue(vfmt.video_codec)
                self.assertTrue(vfmt.video_bit_rate)
                self.assertTrue(vfmt.frame_rate)
                self.assertFalse(vfmt.audio_codec)
                self.assertFalse(vfmt.audio_bit_rate, vfmt)

    def test_hevc_1(self):
        vfmts = vf.VodFormat.find(video_codec=vf.VideoCodec.hevc)
        for vfmt in vfmts:
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.hevc())
            self.assertEmpty(tvfmts)

    def test_hevc_2(self):
        test_cases = [
            (15, [11]),
            (16, [11]),
            (271, [267]),
            (272, [267]),
            (1039, [1035]),
            (1040, [1035]),
        ]
        for tc in test_cases:
            i = tc[0]
            e = tc[1]
            vfmt = vf.VodFormat.find_one(id=i)
            # print(vfmt)
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.hevc())
            # print(tvfmts)
            r = [i.id for i in tvfmts]
            self.assertCountEqual(r, e, i)

    def test_hevc_3(self):
        vfmts = vf.VodFormat.find(is_preview=True)
        for vfmt in vfmts:
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.hevc())
            self.assertEmpty(tvfmts)

    def _validate_normal_preview(self, vfmt):
        tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.preview())
        if not tvfmts:
            return
        tfid = tvfmts[0].id
        self.assertEqual(tfid - 384, vfmt.id)

    def _validate_ott_preview(self, vfmt):
        tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.preview())
        if not tvfmts:
            return
        tfid = tvfmts[0].id
        self.assertEqual(tfid - 256, vfmt.id)

    def test_preview(self):
        vfmts = vf.VodFormat.find(is_preview=False)
        for vfmt in vfmts:
            if vfmt.id > 100000:
                continue
            if vfmt.is_normal:
                self._validate_normal_preview(vfmt)
            elif vfmt.is_ott:
                self._validate_ott_preview(vfmt)

    def test_ott(self):
        vfmts = vf.VodFormat.find(is_ott=False)
        for vfmt in vfmts:
            if vfmt.id > 100000:
                continue
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.ott())
            if not tvfmts:
                return
            tfid = tvfmts[0].id
            self.assertEqual(tfid - 256, vfmt.id)

    def test_narrow_band_phone(self):
        vfmts = vf.VodFormat.find()
        for vfmt in vfmts:
            if vfmt.id > 100000:
                continue
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.narrow_band_phone())
            if not tvfmts:
                continue
            tid = tvfmts[0].id
            self.assertEqual(tid - 1024, vfmt.id)

    def test_ts_encrypt(self):
        vfmts = vf.VodFormat.find(container=vf.Container.ts)
        for vfmt in vfmts:
            if vfmt.id > 100000:
                continue
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.encrypt())
            if not tvfmts:
                continue
            tvfmt = tvfmts[0]
            self.assertEqual(tvfmt.container, vf.Container.bbts)
            self.assertEqual(tvfmt.id + 1, vfmt.id)

    def test_dash_encrypt(self):
        vfmts = vf.VodFormat.find(container=vf.Container.m4s, is_encrypt=False)
        for vfmt in vfmts:
            if vfmt.is_preview \
                    or (vfmt.qn in [6, 15, 74]) \
                    or (vfmt.xcode_mode != vf.XcodeMode.unset) \
                    or (vfmt.enhance_mode != vf.EnhanceMode.unset) \
                    or (vfmt.media_type == vf.MediaType.video and vfmt.video_codec != vf.VideoCodec.avc) \
                    or (vfmt.media_type == vf.MediaType.audio and vfmt.audio_codec != vf.AudioCodec.aac):
                tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.encrypt())
                self.assertEmpty(tvfmts, f'vfmt<{vfmt}>, tvfmts<{tvfmts}>')
            else:
                tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.encrypt())
                # print(f'vfmt<{vfmt}>, tvfmts<{tvfmts}>')
                self.assertEqual(len(tvfmts), 1, f'vfmt<{vfmt}>, tvfmts<{tvfmts}>')
                tvfmt = tvfmts[0]
                self.assertTrue(tvfmt.is_encrypt)
                self.assertEqual(tvfmt.container, vf.Container.m4s)

    def test_av1(self):
        cases = [
            (15, [100011]),
            (16, [100011]),
            (32, [100012]),
        ]
        for case in cases:
            f = case[0]
            vfmt = vf.VodFormat.find_one(id=f)
            tvfmts = vf.VodFormat.transform(vfmt, vf.TransType.av1())

            fids = [i.id for i in tvfmts]
            e = case[1]
            self.assertCountEqual(e, fids)

    def test_sr(self):
        vfmts = vf.VodFormat.find(enhance_mode=vf.EnhanceMode.super_resolution)
        for vfmt in vfmts:
            if vfmt.container == vf.Container.m4s:
                self.assertTrue(vfmt.is_open)
            else:
                self.assertFalse(vfmt.is_open)

    def test_open(self):
        vfmts = vf.VodFormat.find()
        for vfmt in vfmts:
            if vfmt.container in [vf.Container.m4s, vf.Container.bbts]:
                self.assertTrue(vfmt.is_open)
            elif (vfmt.video_codec == vf.VideoCodec.avc) \
                    and (vfmt.xcode_mode == vf.XcodeMode.narrow_band_phone):
                if vfmt.container == vf.Container.flv and not vfmt.is_ott:
                    self.assertFalse(vfmt.is_open)
                else:
                    self.assertTrue(vfmt.is_open)
            elif (vfmt.video_codec == vf.VideoCodec.avc) \
                    and (vfmt.xcode_mode == vf.Container.unset) \
                    and (vfmt.enhance_mode == vf.Container.unset) \
                    and (vfmt.container != vf.Container.ts):
                if vfmt.container == vf.Container.flv and not vfmt.is_ott:
                    self.assertFalse(vfmt.is_open)
                else:
                    self.assertTrue(vfmt.is_open)
            else:
                self.assertFalse(vfmt.is_open)

    def test_unique(self):
        vfmts = vf.VodFormat.find()

        uks = set()
        for vfmt in vfmts:
            vs = [
                vfmt.qn,
                vfmt.container,
                vfmt.video_codec,
                vfmt.audio_codec,
                vfmt.xcode_mode,
                vfmt.enhance_mode,
                vfmt.is_preview,
                vfmt.is_ott,
                vfmt.is_proj,
                vfmt.is_encrypt,
            ]
            uk = '-'.join([str(i) for i in vs])
            self.assertNotIn(uk, uks, vfmt)
            uks.add(uk)

    def test_video_bit_rate(self):
        k = 1000
        m = 1000 * k

        cases = [
            (6, 200 * k),
            (30006, 200 * k),
            (262, 200 * k),
            (15, 400 * k),
            (16, 400 * k),
            (11, 280 * k),
            (30011, 280 * k),
            (100011, 280 * k),
            (125, 14 * m),
            (127, 28 * m),
            (100032, 28 * m),
            (30216, 0),
            (30250, 0),
        ]
        for vid, br in cases:
            vfmt = vf.VodFormat.find_one(id=vid)
            self.assertEqual(vfmt.video_bit_rate, br)

    def test_audio_bit_rate(self):
        k = 1000
        cases = [
            (30216, 64 * k),
            (30232, 128 * k),
            (30006, 0),
            (6, 32 * k),
            (15, 64 * k),
            (32, 128 * k),
            (127, 320 * k),
        ]
        for vid, br in cases:
            vfmt = vf.VodFormat.find_one(id=vid)
            self.assertEqual(vfmt.audio_bit_rate, br)

    def test_normal(self):
        vfmt = vf.VodFormat.find_one(id=29)
        self.assertFalse(vfmt.is_normal)

    def test_data_1(self):
        vfmt = vf.VodFormat.find_one(id=31145)
        self.assertEqual(vfmt.video_codec, vf.VideoCodec.hevc)

    def test_data_2(self):
        vfmt = vf.VodFormat.find_one(id=30258)
        self.assertIsNotNone(vfmt)
        self.assertEqual(vfmt.container, vf.Container.m4s)
        self.assertEqual(vfmt.media_type, vf.MediaType.audio)
        self.assertEqual(vfmt.is_preview, True)
        self.assertEqual(vfmt.qn, 30229)

    def test_data_3(self):
        vfmt = vf.VodFormat.find_one(id=31144)
        self.assertIsNotNone(vfmt)
        self.assertEqual(vfmt.container, vf.Container.m4s)
        self.assertEqual(vfmt.media_type, vf.MediaType.video)
        self.assertEqual(vfmt.xcode_mode, vf.XcodeMode.narrow_band_phone)
        self.assertEqual(vfmt.video_codec, vf.VideoCodec.avc)

    def test_data_4(self):
        vfmt = vf.VodFormat.find_one(id=30390)
        self.assertIsNotNone(vfmt)
        self.assertEqual(vfmt.container, vf.Container.m4s)
        self.assertEqual(vfmt.media_type, vf.MediaType.audio)
        self.assertEqual(vfmt.qn, 30216)

    def test_data_5(self):
        vfmt = vf.VodFormat.find_one(id=30518)
        self.assertIsNotNone(vfmt)
        self.assertEqual(vfmt.container, vf.Container.m4s)
        self.assertEqual(vfmt.media_type, vf.MediaType.audio)
        self.assertEqual(vfmt.qn, 30216)

    def test_data_6(self):
        """ nb265 dash"""
        for case in [
            (16, 100056),
            (32, 100057),
            (64, 100058),
            (74, 100059),
            (80, 100060),
        ]:
            input, expected = case
            vfmt = vf.VodFormat.find_one(id=input)
            tvfmts = vf.VodFormat.transform(
                vfmt,
                vf.TransType.narrow_band(),
                vf.TransType.hevc(),
                vf.TransType.dash(),
            )
            self.assertNotEmpty(tvfmts)
            self.assertEqual(len(tvfmts), 1)

            tvfmt = tvfmts[0]
            self.assertEqual(tvfmt.id, expected)
            self.assertEqual(tvfmt.container, vf.Container.m4s)
            self.assertEqual(tvfmt.media_type, vf.MediaType.video)
            self.assertEqual(tvfmt.video_codec, vf.VideoCodec.hevc)

    def test_data_7(self):
        """ ott nb265 dash"""
        for case in [
            (16, 100076),
            (32, 100077),
            (64, 100078),
            (74, 100079),
            (80, 100080),
        ]:
            input, expected = case
            vfmt = vf.VodFormat.find_one(id=input)
            tvfmts = vf.VodFormat.transform(
                vfmt,
                vf.TransType.ott(),
                vf.TransType.narrow_band(),
                vf.TransType.hevc(),
                vf.TransType.dash(),
            )
            self.assertNotEmpty(tvfmts)
            self.assertEqual(len(tvfmts), 1)

            tvfmt = tvfmts[0]
            self.assertEqual(tvfmt.id, expected)
            self.assertEqual(tvfmt.container, vf.Container.m4s)
            self.assertEqual(tvfmt.media_type, vf.MediaType.video)
            self.assertEqual(tvfmt.video_codec, vf.VideoCodec.hevc)
            self.assertTrue(tvfmt.is_ott)

    def test_data_8(self):
        """ sr h265 dash"""
        for case in [
            (100033, 100036),
            (100037, 100040),
        ]:
            input, expected = case
            vfmt = vf.VodFormat.find_one(id=input)
            self.assertEqual(vfmt.enhance_mode, vf.EnhanceMode.super_resolution)
            tvfmts = vf.VodFormat.transform(
                vfmt,
                vf.TransType.hevc(),
                vf.TransType.dash(),
            )
            self.assertNotEmpty(tvfmts, case)
            self.assertEqual(len(tvfmts), 1)

            tvfmt = tvfmts[0]
            self.assertEqual(tvfmt.id, expected)
            self.assertEqual(tvfmt.container, vf.Container.m4s)
            self.assertEqual(tvfmt.media_type, vf.MediaType.video)
            self.assertEqual(tvfmt.video_codec, vf.VideoCodec.hevc)

    def test_data_9(self):
        for case in [
            (125, vf.HdrType.hdr10),
            (126, vf.HdrType.dolby_vision),
        ]:
            qn, hdr_type = case
            vfmts = vf.VodFormat.find(qn=qn)
            for vfmt in vfmts:
                self.assertEqual(vfmt.hdr_type, hdr_type)


class TestVodFormatNBQuick(BaseTestCase):
    def test_find(self):
        fs = vf.VodFormat.find(id=100094)
        self.assertNotEmpty(fs)
        self.assertEqual(len(fs), 1)

        f = fs[0]
        self.assertEqual(f.qn, 16)

    def test_transform_1(self):
        cases = [
            (16, 100099),
            (32, 100100),
            (64, 100101),
            (74, 100102),
            (80, 100103),
        ]
        for fid, tfid in cases:
            f = vf.VodFormat.find_one(id=fid)
            tfs = vf.VodFormat.transform(f, vf.TransType.narrow_band_quick(), vf.TransType.dash())
            self.assertNotEmpty(tfs)
            self.assertEqual(len(tfs), 1)

            tf = tfs[0]
            self.assertEqual(tf.id, tfid)

    def test_transform_2(self):
        cases = [
            (16, 100109),
            (32, 100110),
            (64, 100111),
            (74, 100112),
            (80, 100113),
        ]
        for fid, tfid in cases:
            f = vf.VodFormat.find_one(id=fid)
            tfs = vf.VodFormat.transform(f, vf.TransType.narrow_band_quick(), vf.TransType.hevc(), vf.TransType.dash())
            self.assertNotEmpty(tfs)
            self.assertEqual(len(tfs), 1)

            tf = tfs[0]
            self.assertEqual(tf.id, tfid)

    def test_transform_3(self):
        cases = [
            (16, 100119),
            (32, 100120),
            (64, 100121),
            (74, 100122),
            (80, 100123),
        ]
        for fid, tfid in cases:
            f = vf.VodFormat.find_one(id=fid)
            tfs = vf.VodFormat.transform(
                f, vf.TransType.ott(), vf.TransType.narrow_band_quick(), vf.TransType.dash()
            )
            self.assertNotEmpty(tfs)
            self.assertEqual(len(tfs), 1)

            tf = tfs[0]
            self.assertEqual(tf.id, tfid)

    def test_transform_4(self):
        cases = [
            (16, 100129),
            (32, 100130),
            (64, 100131),
            (74, 100132),
            (80, 100133),
        ]
        for fid, tfid in cases:
            f = vf.VodFormat.find_one(id=fid)
            tfs = vf.VodFormat.transform(
                f, vf.TransType.ott(), vf.TransType.narrow_band_quick(), vf.TransType.hevc(), vf.TransType.dash()
            )
            self.assertNotEmpty(tfs)
            self.assertEqual(len(tfs), 1)

            tf = tfs[0]
            self.assertEqual(tf.id, tfid)


class TestUsage(BaseTestCase):
    @staticmethod
    def _dash_format(format):
        f = vf.VodFormat.find_one(id=format)
        fs = vf.VodFormat.transform(f, vf.TransType.dash())
        result = {}
        for f in fs:
            if f.media_type == vf.MediaType.video:
                result['video'] = f.id
            elif f.media_type == vf.MediaType.audio:
                result['audio'] = f.id
            else:
                assert False
        return result

    def test_dash_format_1(self):
        fmt = 16
        df = self._dash_format(fmt)
        self.assertEqual(df['video'], 30016)
        self.assertEqual(df['audio'], 30216)

    def test_dash_format_2(self):
        fmt = 11
        df = self._dash_format(fmt)
        self.assertEqual(df['video'], 30011)
        self.assertNotIn('audio', df)

    def test_dash_format_3(self):
        fmt = 10
        df = self._dash_format(fmt)
        self.assertNotIn('video', df)
        self.assertNotIn('audio', df)

    @staticmethod
    def _max_format_for_raw(formats):
        fs = []
        for format in formats:
            f = vf.VodFormat.find_one(id=format)
            if f.is_normal:
                fs.append(f)
        if len(fs) > 0:
            return sorted(fs, key=(lambda x: x.qn))[-1].id
        else:
            return None

    def test_max_format_for_raw_1(self):
        fmts = [16, 32]
        f = self._max_format_for_raw(fmts)
        self.assertEqual(f, 32)

    def test_max_format_for_raw_2(self):
        fmts = [16, 32, 11, 33]
        f = self._max_format_for_raw(fmts)
        self.assertEqual(f, 32)
