import asyncio

from src.retry import retry
from tests import BaseTestCase


class TestRetry(BaseTestCase):
    def test_retry_0(self):
        @retry(exceptions=(RuntimeError,))
        def run():
            return True
        r = run()
        self.assertTrue(r)

    def test_retry_1(self):
        error_count = 1

        @retry(exceptions=(RuntimeError,))
        def run():
            nonlocal error_count
            while error_count > 0:
                error_count -= 1
                raise RuntimeError
            return True

        r = run()
        self.assertTrue(r)

    def test_retry_2(self):
        error_count = 1

        @retry(exceptions=(RuntimeError,))
        def run():
            nonlocal error_count
            while error_count > 0:
                error_count -= 1
                raise ValueError
            return True

        with self.assertRaises(ValueError):
            r = run()
            self.assertTrue(r)

    def test_retry_3(self):
        error_count = 1

        @retry(exceptions=(RuntimeError,))
        async def run():
            nonlocal error_count
            while error_count > 0:
                error_count -= 1
                raise RuntimeError
            return True

        loop = asyncio.get_event_loop()
        r = loop.run_until_complete(run())
        self.assertTrue(r)

    def test_retry_4(self):
        error_count = 1

        @retry(exceptions=(RuntimeError,), tries=1)
        def run():
            nonlocal error_count
            while error_count > 0:
                error_count -= 1
                raise RuntimeError
            return True

        r = run()
        self.assertTrue(r)

    def test_retry_5(self):
        error_count = 2

        @retry(exceptions=(RuntimeError,), tries=1)
        def run():
            nonlocal error_count
            while error_count > 0:
                error_count -= 1
                raise RuntimeError
            return True

        with self.assertRaises(RuntimeError):
            run()
