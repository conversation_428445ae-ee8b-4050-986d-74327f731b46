from src.common import NoneFilterDict
from src.common import chunked
from tests import BaseTestCase


class TestHelp(BaseTestCase):
    def test_NoneFilterDict_0(self):
        e = {'key': 'value'}
        v = {'key': 'value', 'key2': None}
        r = NoneFilterDict(v)
        self.assertEqual(e, r)

    def test_NoneFilterDict_1(self):
        e = {'key': 'value', 'key2': 'value2'}
        v = {'key': 'value', 'key2': 'value2'}
        r = NoneFilterDict(v)
        self.assertEqual(e, r)

    def test_chunked_0(self):
        v = []
        e = []
        r = list(chunked(v, 1))
        self.assertEqual(e, r)

    def test_chunked_1(self):
        v = [1, 2, 3]
        e = [[1], [2], [3]]
        r = list(chunked(v, 1))
        self.assertEqual(e, r)

    def test_chunked_2(self):
        v = [1, 2, 3]
        e = [[1, 2], [3]]
        r = list(chunked(v, 2))
        self.assertEqual(e, r)
