from unittest import TestCase

from src.errors import PyUtilError


class TestErrors(TestCase):
    def test_PyApiError(self):
        message = 'this is a pyapi error'
        try:
            raise PyUtilError(message=message, cid=123, reason='bad')
        except PyUtilError as e:
            self.assertEqual(e.name, 'PyUtilError')
            self.assertEqual(e.code, PyUtilError.code)
            self.assertEqual(e.message, message)
            self.assertEqual(e.extra_info['cid'], 123)
            self.assertEqual(e.extra_info['reason'], 'bad')

    def test_all_errors(self):
        message = 'this is a pyutil error'
        for error in PyUtilError.__subclasses__():
            try:
                raise error(message=message)
            except PyUtilError as e:
                self.assertEqual(e.name, error.__name__)
                self.assertEqual(e.code, error.code)
                self.assertEqual(e.message, message)
