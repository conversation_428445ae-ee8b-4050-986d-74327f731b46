import unittest
from typing import Container


class BaseTestCase(unittest.TestCase):
    def assertEmpty(self, obj: Container, msg=None) -> None:
        """Asserts whether the given object is an empty container."""
        self.assertIsInstance(obj, Container, msg=msg)
        self.assertFalse(obj, msg=msg)

    def assertNotEmpty(self, obj: Container, msg=None) -> None:
        """Asserts whether the given object is a non-empty container."""
        self.assertIsInstance(obj, Container, msg=msg)
        self.assertTrue(obj, msg=msg)
