import asyncio

from src.errors import GrpcRequestError
from src.grpc import GrpcClient
from src.grpc.aio import GrpcAioClient
from src.rpcs.taishan import proxy_pb2
from src.rpcs.taishan import proxy_pb2_grpc
from tests import BaseTestCase


class TestGrpc(BaseTestCase):
    def test_request(self):
        client = GrpcClient('inf.taishan.proxy')
        r: proxy_pb2.GetResp = client.request(
            stub_class=proxy_pb2_grpc.TaishanProxyStub,
            method='get',
            request=proxy_pb2.GetReq(),
        )
        assert r

    def test_with_config_0(self):
        client = GrpcClient('inf.taishan.proxy')
        with self.assertRaises(GrpcRequestError):
            r = client.with_config(timeout=0.001).request(
                stub_class=proxy_pb2_grpc.TaishanProxyStub,
                method='get',
                request=proxy_pb2.GetReq(),
            )
        r = client.with_config(retry_times=0).request(
            stub_class=proxy_pb2_grpc.TaishanProxyStub,
            method='get',
            request=proxy_pb2.GetReq(),
        )
        assert r

    def test_with_config_1(self):
        client = GrpcClient('inf.taishan.proxy')

        c = client.with_config(timeout=0.001)
        assert c._config.timeout == 0.001

        c = client.with_config(timeout=3).with_config(retry_times=10)
        assert c._config.timeout == 3
        assert c._config.retry_times == 10

        c = client.with_config(timeout=10, retry_times=5, retry_interval=1)
        assert c._config.timeout == 10
        assert c._config.retry_times == 5
        assert c._config.retry_interval == 1


class TestAioGrpc(BaseTestCase):
    async def _test_request(self):
        client = GrpcAioClient('inf.taishan.proxy')
        r: proxy_pb2.GetResp = await client.request(
            stub_class=proxy_pb2_grpc.TaishanProxyStub,
            method='get',
            request=proxy_pb2.GetReq(),
        )
        assert r

    async def _test_with_config(self):
        client = GrpcAioClient('inf.taishan.proxy')
        with self.assertRaises(GrpcRequestError):
            r = await client.with_config(timeout=0.001).request(
                stub_class=proxy_pb2_grpc.TaishanProxyStub,
                method='get',
                request=proxy_pb2.GetReq(),
            )
        r = await client.with_config(retry_times=0).request(
            stub_class=proxy_pb2_grpc.TaishanProxyStub,
            method='get',
            request=proxy_pb2.GetReq(),
        )
        assert r

    def test_aio(self):
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self._test_request())
        loop.run_until_complete(self._test_with_config())
