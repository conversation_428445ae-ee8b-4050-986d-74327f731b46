import src.objkey as objkey
from tests import BaseTestCase


class TestObjkey(BaseTestCase):
    def test_objkey_1(self):
        r = objkey.objkey(12345, 1, format=16)
        e = '/45/23/12345/12345-1-16.mp4'
        self.assertEqual(e, r)

    def test_objkey_2(self):
        r = objkey.objkey(12345, 1, format=16, ver='da3')
        e = '/45/23/12345/12345_da3-1-16.mp4'
        self.assertEqual(e, r)

    def test_objkey_3(self):
        r = objkey.objkey(12345, 1, format=30016)
        e = '/45/23/12345/12345-1-30016.m4s'
        self.assertEqual(e, r)

    def test_objkey_4(self):
        r = objkey.objkey(12345, 1, format=30016, ver=None)
        e = '/45/23/12345/12345-1-30016.m4s'
        self.assertEqual(e, r)

    def test_uri_prefix_1(self):
        r = objkey.uri_prefix('upgcxcode', 12345)
        e = 'upos://upgcxcode/45/23/12345/12345'
        self.assertEqual(e, r)

    def test_uri_prefix_2(self):
        r = objkey.uri_prefix('upgcxcode', 12345, ver='da3')
        e = 'upos://upgcxcode/45/23/12345/12345_da3'
        self.assertEqual(e, r)

    def test_uri_prefix_3(self):
        r = objkey.uri_prefix('tmp', 12345)
        e = 'upos://tmp/45/23/12345/12345'
        self.assertEqual(e, r)

    def test_uri_prefix_4(self):
        r = objkey.uri_prefix('tmp/projection', 12345)
        e = 'upos://tmp/projection/45/23/12345/12345'
        self.assertEqual(e, r)

    def test_uri_1(self):
        r = objkey.uri('upgcxcode', 12345, 1, 16)
        e = 'upos://upgcxcode/45/23/12345/12345-1-16.mp4'
        self.assertEqual(e, r)

    def test_uri_2(self):
        r = objkey.uri('upgcxcode', 12345, 1, 16, ver='da3')
        e = 'upos://upgcxcode/45/23/12345/12345_da3-1-16.mp4'
        self.assertEqual(e, r)

    def test_uri_parse_1(self):
        r = objkey.uri_parse('/45/23/12345/12345-1-16.mp4')
        e = {
            'prefix': '/45/23/12345/12345',
            'cid': 12345,
            'ver': '',
            'vp': 1,
            'format': 16,
            'ext': 'mp4',
        }
        self.assertEqual(e, r)

    def test_uri_parse_2(self):
        r = objkey.uri_parse('/45/23/12345/12345_da3-1-16.mp4')
        e = {
            'prefix': '/45/23/12345/12345_da3',
            'cid': 12345,
            'ver': 'da3',
            'vp': 1,
            'format': 16,
            'ext': 'mp4',
        }
        self.assertEqual(e, r)

    def test_uri_parse_3(self):
        r = objkey.uri_parse('upos://upgcxcode/45/23/12345/12345-1-16.mp4')
        e = {
            'prefix': 'upos://upgcxcode/45/23/12345/12345',
            'cid': 12345,
            'ver': '',
            'vp': 1,
            'format': 16,
            'ext': 'mp4',
        }
        self.assertEqual(e, r)

    def test_uri_parse_4(self):
        r = objkey.uri_parse('upos://upgcxcode/45/23/12345/12345_da3-1-16.mp4')
        e = {
            'prefix': 'upos://upgcxcode/45/23/12345/12345_da3',
            'cid': 12345,
            'ver': 'da3',
            'vp': 1,
            'format': 16,
            'ext': 'mp4',
        }
        self.assertEqual(e, r)


class TestCommonObjkey(BaseTestCase):
    def test_common_objkey_1(self):
        filename = 'm210111a22psimf3fjxzr1pzfmnxgspx'
        vp = 1
        flag = '133111110023'

        e = '/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx-1-133111110023.flv'
        r = objkey.common_objkey(filename, vp, flag)
        self.assertEqual(e, r)

    def test_common_objkey_2(self):
        filename = 'm210111a22psimf3fjxzr1pzfmnxgspx'
        vp = 1
        flag = '133111110023'
        ver = 'da3'

        e = '/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3-1-133111110023.flv'
        r = objkey.common_objkey(filename, vp, flag, ver=ver)
        self.assertEqual(e, r)

    def test_common_uri_prefix_1(self):
        bucket = 'iupxcodeboss'
        filename = 'm210111a22psimf3fjxzr1pzfmnxgspx'

        e = 'upos://iupxcodeboss/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx'
        r = objkey.common_uri_prefix(bucket, filename)
        self.assertEqual(e, r)

    def test_common_uri_prefix_2(self):
        bucket = 'iupxcodeboss'
        filename = 'm210111a22psimf3fjxzr1pzfmnxgspx'
        ver = 'da3'

        e = 'upos://iupxcodeboss/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3'
        r = objkey.common_uri_prefix(bucket, filename, ver=ver)
        self.assertEqual(e, r)

    def test_common_uri_1(self):
        bucket = 'iupxcodeboss'
        filename = 'm210111a22psimf3fjxzr1pzfmnxgspx'
        vp = 1
        flag = '133111110023'

        e = 'upos://iupxcodeboss/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx-1-133111110023.flv'
        r = objkey.common_uri(bucket, filename, vp, flag)
        self.assertEqual(e, r)

    def test_common_uri_2(self):
        bucket = 'iupxcodeboss'
        filename = 'm210111a22psimf3fjxzr1pzfmnxgspx'
        vp = 1
        flag = '133111110023'
        ver = 'da3'

        e = 'upos://iupxcodeboss/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3-1-133111110023.flv'
        r = objkey.common_uri(bucket, filename, vp, flag, ver=ver)
        self.assertEqual(e, r)

    def test_common_uri_parse_1(self):
        uri = 'upos://iupxcodeboss/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3-1-112111110022.mp4'
        r = objkey.common_uri_parse(uri)
        e = {
            'prefix': 'upos://iupxcodeboss/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3',
            'filename': 'm210111a22psimf3fjxzr1pzfmnxgspx',
            'ver': 'da3',
            'vp': 1,
            'flag': '112111110022',
            'ext': 'mp4',
        }
        self.assertEqual(e, r)

    def test_common_uri_parse_2(self):
        uri = '/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3-1-112111110022.mp4'
        r = objkey.common_uri_parse(uri)
        e = {
            'prefix': '/px/gs/m210111a22psimf3fjxzr1pzfmnxgspx_da3',
            'filename': 'm210111a22psimf3fjxzr1pzfmnxgspx',
            'ver': 'da3',
            'vp': 1,
            'flag': '112111110022',
            'ext': 'mp4',
        }
        self.assertEqual(e, r)


class TestCommonFlagModel(BaseTestCase):
    def test_0(self):
        r = objkey._CommonFlagModel.get('business', 'ugc')
        e = '1'
        self.assertEqual(e, r)

        r = objkey._CommonFlagModel.get_inverted('business', '1')
        e = 'ugc'
        self.assertEqual(e, r)

    def test_1(self):
        with self.assertRaises(ValueError):
            objkey._CommonFlagModel.get('bad_key', '1')

        with self.assertRaises(ValueError):
            objkey._CommonFlagModel.get_inverted('bad_key', '1')

        with self.assertRaises(ValueError):
            objkey._CommonFlagModel.get('business', 'bad_value')

        with self.assertRaises(ValueError):
            objkey._CommonFlagModel.get_inverted('business', '-1')

    def test_2(self):
        r = objkey._CommonFlagModel.get('frame_rate', 35.5)
        e = '2'  # 35_60
        self.assertEqual(e, r)

        r = objkey._CommonFlagModel.get('frame_rate', 60.5)
        e = '3'  # 60+
        self.assertEqual(e, r)

    def test_3(self):
        r = objkey._CommonFlagModel.get('frame_rate', '35.5')
        e = '2'  # 35_60
        self.assertEqual(e, r)

        r = objkey._CommonFlagModel.get('frame_rate', '60.5')
        e = '3'  # 60+
        self.assertEqual(e, r)

    def test_4(self):
        r = objkey._CommonFlagModel.get('frame_rate', None)
        e = '0'
        self.assertEqual(e, r)


class TestCommonFlag(BaseTestCase):
    def test_0(self):
        r = objkey.common_flag({})
        e = '000000000000'
        self.assertEqual(e, r)

    def test_1(self):
        r = objkey.common_flag({
            'business': 'ugc',
            'qn': '29',
            'container': 'm4s',
            'media_type': 'video',
            'video_codec': 'avc',
            'audio_codec': 'flac',
            'frame_rate': '0_35',
            'bit_depth': '10',
            'video_characteristic': 'spherical',
            'audio_characteristic': 'dolby',
            'audio_channels': '1',
            'audio_sample_rate': '22050_44100',
        })
        e = '121212121112'
        self.assertEqual(e, r)

    def test_2(self):
        r = objkey.common_flag_parse('')
        e = {
            'business': None,
            'qn': None,
            'container': None,
            'media_type': None,
            'video_codec': None,
            'audio_codec': None,
            'frame_rate': None,
            'bit_depth': None,
            'video_characteristic': None,
            'audio_characteristic': None,
            'audio_channels': None,
            'audio_sample_rate': None,
        }
        self.assertEqual(e, r)

    def test_3(self):
        r = objkey.common_flag_parse('121212121112')
        e = {
            'business': 'ugc',
            'qn': '29',
            'container': 'm4s',
            'media_type': 'video',
            'video_codec': 'avc',
            'audio_codec': 'flac',
            'frame_rate': '0_35',
            'bit_depth': '10',
            'video_characteristic': 'spherical',
            'audio_characteristic': 'dolby',
            'audio_channels': '1',
            'audio_sample_rate': '22050_44100',
        }
        self.assertEqual(e, r)

    def test_4(self):
        r = objkey.common_flag_update('000000000000', {'business': 'ugc'})
        e = '100000000000'
        self.assertEqual(e, r)

        r = objkey.common_flag_update('000000000000', {'qn': '29'})
        e = '020000000000'
        self.assertEqual(e, r)
