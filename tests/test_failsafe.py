import asyncio
import time
import unittest

from src.failsafe import failsafe


class TestFailsafe(unittest.TestCase):
    def test_0(self):
        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            raise ValueError

        r = func()
        self.assertEqual(0, r)

    def test_1(self):
        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            raise KeyError

        with self.assertRaises(KeyError):
            func()

    def test_2(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        self.assertEqual(1, func())
        must_raise = True
        self.assertEqual(0, func())

    def test_3(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        self.assertEqual(1, func())
        must_raise = True
        self.assertEqual(0, func())
        self.assertEqual(0, func())

        # mask
        must_raise = False
        self.assertEqual(0, func())
        self.assertEqual(0, func())

    def test_4(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func1():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func2():
            return 1

        must_raise = True
        self.assertEqual(0, func1())
        # mask
        self.assertEqual(0, func1())
        self.assertEqual(0, func1())
        # other function not masked
        self.assertEqual(1, func2())

    def test_5(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        self.assertEqual(1, func())
        self.assertEqual(1, func())
        # not mask
        must_raise = True
        self.assertEqual(0, func())
        must_raise = False
        self.assertEqual(1, func())

    def test_6(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        must_raise = True
        self.assertEqual(0, func())

        must_raise = False
        # mask
        self.assertEqual(0, func())
        # recover
        time.sleep(20)
        self.assertEqual(1, func())

    def test_7(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        def func():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        must_raise = True
        with self.assertRaises(ValueError):
            func.direct()

    def test_8(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=0)
        async def func():
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return 1

        loop = asyncio.get_event_loop()
        r = loop.run_until_complete(func())
        self.assertEqual(1, r)

        must_raise = True
        r = loop.run_until_complete(func())
        self.assertEqual(0, r)

    def test_9(self):
        must_raise = False

        @failsafe(exceptions=(ValueError,), fallback=(lambda x: [0] * x))
        def func(v):
            nonlocal must_raise
            if must_raise:
                raise ValueError
            return list(range(v))

        self.assertEqual([0, 1, 2], func(3))

        must_raise = True
        self.assertEqual([0, 0, 0], func(3))

    def test_instance_method_0(self):
        must_raise = True

        class C:
            @failsafe(exceptions=(ValueError,), fallback=0)
            def func(self):
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        with self.assertRaises(ValueError):
            c = C()
            self.assertEqual(0, c.func.direct())

    def test_instance_method_1(self):
        must_raise = True

        class C:
            @failsafe(exceptions=(ValueError,), fallback=0)
            async def func(self):
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        with self.assertRaises(ValueError):
            c = C()
            loop = asyncio.get_event_loop()
            r = loop.run_until_complete(c.func.direct())
            self.assertEqual(0, r)

    def test_instance_method_2(self):
        must_raise = True

        class C:
            @failsafe(exceptions=(ValueError,), fallback=0)
            def func(self):
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        c = C()
        self.assertEqual(0, c.func())

    def test_instance_method_3(self):
        must_raise = False

        class C:
            @failsafe(exceptions=(ValueError,), fallback=0)
            def func(self):
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        c = C()
        self.assertEqual(1, c.func())

    def test_staticmethod_0(self):
        must_raise = True

        class C:
            @staticmethod
            @failsafe(exceptions=(ValueError,), fallback=0)
            def func():
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        with self.assertRaises(ValueError):
            self.assertEqual(0, C.func.direct())

    def test_staticmethod_1(self):
        must_raise = True

        class C:
            @staticmethod
            @failsafe(exceptions=(ValueError,), fallback=0)
            async def func():
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        with self.assertRaises(ValueError):
            loop = asyncio.get_event_loop()
            r = loop.run_until_complete(C.func.direct())
            self.assertEqual(0, r)

    def test_staticmethod_2(self):
        must_raise = True

        class C:
            @staticmethod
            @failsafe(exceptions=(ValueError,), fallback=0)
            def func():
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        self.assertEqual(0, C.func())

    def test_staticmethod_3(self):
        must_raise = False

        class C:
            @staticmethod
            @failsafe(exceptions=(ValueError,), fallback=0)
            def func():
                nonlocal must_raise
                if must_raise:
                    raise ValueError
                return 1

        self.assertEqual(1, C.func())

    # def test_classmethod_0(self):
    #     must_raise = True
    #
    #     class C:
    #         @classmethod
    #         @failsafe(exceptions=(ValueError,), fallback=0)
    #         def func(cls):
    #             nonlocal must_raise
    #             if must_raise:
    #                 raise ValueError
    #             return 1
    #
    #     with self.assertRaises(ValueError):
    #         self.assertEqual(0, C.func.direct(C))
    #
    # def test_classmethod_1(self):
    #     must_raise = True
    #
    #     class C:
    #         @classmethod
    #         @failsafe(exceptions=(ValueError,), fallback=0)
    #         async def func(cls):
    #             nonlocal must_raise
    #             if must_raise:
    #                 raise ValueError
    #             return 1
    #
    #     with self.assertRaises(ValueError):
    #         loop = asyncio.get_event_loop()
    #         r = loop.run_until_complete(C.func.direct(C))
    #         self.assertEqual(0, r)
    #
    # def test_classmethod_2(self):
    #     must_raise = True
    #
    #     class C:
    #         @classmethod
    #         @failsafe(exceptions=(ValueError,), fallback=0)
    #         def func(cls):
    #             nonlocal must_raise
    #             if must_raise:
    #                 raise ValueError
    #             return 1
    #
    #     self.assertEqual(0, C.func())
    #
    # def test_classmethod_3(self):
    #     must_raise = True
    #
    #     class C:
    #         @classmethod
    #         @failsafe(exceptions=(ValueError,), fallback=0)
    #         async def func(cls):
    #             nonlocal must_raise
    #             if must_raise:
    #                 raise ValueError
    #             return 1
    #
    #     with self.assertRaises(ValueError):
    #         c = C()
    #         loop = asyncio.get_event_loop()
    #         r = loop.run_until_complete(c.func.direct(C))
    #         self.assertEqual(0, r)


if __name__ == '__main__':
    unittest.main()
