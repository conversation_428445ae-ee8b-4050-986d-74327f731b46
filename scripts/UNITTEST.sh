#!/bin/bash
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname $PROJECT_ROOT)"
PROJECT_NAME="$(basename $PROJECT_ROOT)"
source $PROJECT_ROOT/scripts/IMAGE_BUILD.sh

function start_image() {
  (
    set -x
    docker run -it --rm $DOCKER_RT --cap-add sys_admin \
      -e SWARM_POSITION=$SWARM_POSITION \
      -e VXCODE_SWARM_ENV=$VXCODE_SWARM_ENV \
      -w /data/app/$PROJECT_NAME/source/tests \
      --volume /var/run/lancer:/var/run/lancer:rw \
      --volume $SWARM_LOG_PATH:/data/log/bili-vxcode:rw \
      --volume $SWARM_WORKSPACE_PATH:/data/bili_vxcode_workspace:rw \
      $IMAGE_NAME:latest python3.7 -m unittest discover -v
  )
  set +x
}



function main() {
  cd $PROJECT_ROOT
  build_image "latest"
  start_image
}

main $@
