#!/bin/bash
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname $PROJECT_ROOT)"
PROJECT_NAME="$(basename $PROJECT_ROOT)"
DOCKER_FILE_ROOT="$PROJECT_ROOT/dockerfiles"

nvidia-docker --version
if [ $? -eq 0 ]; then
  NV_DOCKER_ENABLED=1
  IMAGE_NAME="$PROJECT_NAME-gpu"
  DOCKER_RT="--runtime=nvidia --cap-add SYS_NICE"
  GPU_PREFIX="-gpu"
  RUNTIME_IMAGE="nvidia/cuda:10.2-runtime-ubuntu16.04"
  LINUX_TOOLS=""
else
  NV_DOCKER_ENABLED=0
  IMAGE_NAME=$PROJECT_NAME
  DOCKER_RT=""
  GPU_PREFIX=""
  RUNTIME_IMAGE="debian:stretch-slim"
  LINUX_TOOLS="linux-tools"
fi
echo "Nvidia Docker: $NV_DOCKER_ENABLED"

function build_latest_image() {
  (
    set -x
    docker image build --build-arg SUB_IMAGE=$GPU_PREFIX \
          --build-arg app_name=$PROJECT_NAME \
          --build-arg RT_IMAGE=$RUNTIME_IMAGE \
          --build-arg LINUX_TOOLS=$LINUX_TOOLS \
          -t $IMAGE_NAME:latest .
  )
  set +x
}

function build_release_image() {
  latest_tag=$(git describe --abbrev=0 --tags)
  build_tagged_image $latest_tag
}

function build_tagged_image() {
  git stash
  git checkout -b tag_$latest_tag $latest_tag
  (
    set -x
    docker image build --build-arg SUB_IMAGE=$GPU_PREFIX \
          --build-arg app_name=$PROJECT_NAME \
          --build-arg RT_IMAGE=$RUNTIME_IMAGE \
          --build-arg LINUX_TOOLS=$LINUX_TOOLS \
          -t $IMAGE_NAME:$1 .
  )
  set +x
  git checkout -
  git branch -d tag_$latest_tag
  git stash pop
}

function build_image() {
  IMG="latest"
  if [ "$#" -eq 1 ]; then
    IMG=$1
  fi
  echo "Start Image: $IMG"
  cd $PROJECT_ROOT
  if [ "$IMG" == "release" ]; then
    build_release_image
  elif [ "$IMG" == "latest" ]; then
    build_latest_image
  fi
  cd -
}
