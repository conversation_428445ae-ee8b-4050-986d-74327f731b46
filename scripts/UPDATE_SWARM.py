# -*- coding: utf-8 -*-
import os
import sys
import shlex
import shutil
import logging
from logging import Formatter
from subprocess import check_call, check_output

GIT_REPOSITORY = "*******************:bili-vxcode/vxcode-swarm-larva.git"
NO_OVERWRITE_OCCUPIER = ".no_overwrite"
SCRIPT_ROOT = os.path.dirname(os.path.realpath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_ROOT)
PROJECT_NAME = os.path.basename(PROJECT_ROOT)
LARVA_WORKSPACE = os.path.join(PROJECT_ROOT, "__larva")
LARVA_ROOT = os.path.join(LARVA_WORKSPACE, "vxcode-swarm-larva")


def init_local_log(logging_level=logging.INFO):
    fmt = '[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s'
    date_fmt = '%y%m%d %H:%M:%S'
    logging.basicConfig(format=fmt, datefmt=date_fmt, level=logging_level)
    logging_path = os.path.join(PROJECT_ROOT, "larva_update.log")
    file_handler = logging.FileHandler(logging_path)
    formatter = Formatter(fmt, date_fmt)
    file_handler.setFormatter(formatter)
    logger = logging.getLogger()
    logger.setLevel(logging_level)
    logger.addHandler(file_handler)


def create_empty_folder(dpath):
    if os.path.exists(dpath):
        shutil.rmtree(dpath, ignore_errors=True)
    os.mkdir(dpath)


def ensure_local_folder(dpath):
    if os.path.isfile(dpath):
        os.unlink(dpath)
    if not os.path.exists(dpath):
        os.mkdir(dpath)


def sys_call(cmd, no_result=True):
    logging.info("Exec: " + cmd)
    if no_result:
        check_call(shlex.split(cmd))
    else:
        if sys.version_info.major == 3:
            return check_output(shlex.split(cmd)).decode("utf-8").strip()
        else:
            return check_output(shlex.split(cmd)).strip()


def enforce_project_name():
    for f in ["Dockerfile", ".gitlab-ci.yml"]:
        fpath = os.path.join(PROJECT_ROOT, f)
        tmp_path = fpath + ".tmp"
        with open(tmp_path, "w") as wfile:
            with open(fpath, "r") as rfile:
                content = rfile.read()
                content = content.replace("vxcode-swarm-larva", PROJECT_NAME)
                wfile.write(content)
        os.rename(tmp_path, fpath)


class LarvaWorkspace:
    def __init__(self, tag=None):
        self.pwd = os.curdir
        self.tag = tag

    def _prepare_code_base(self):
        sys_call("git clone " + GIT_REPOSITORY)
        os.chdir(LARVA_ROOT)
        sys_call("git fetch --tags")
        if self.tag is None:
            latest_tag_hash = sys_call("git rev-list --tags --max-count=1", False)
            latest_tag = sys_call("git describe --tags " + latest_tag_hash, False)
            self.tag = latest_tag
            logging.info("Checkout the latest tag: " + latest_tag)
        else:
            logging.info("Checkout the given tag: " + self.tag)
        sys_call("git checkout " + self.tag + " -b " + self.tag)
        sys_call("git lfs fetch")
        sys_call("git lfs checkout")

    def __enter__(self):
        create_empty_folder(LARVA_WORKSPACE)
        os.chdir(LARVA_WORKSPACE)
        logging.info("Enter workspace: " + LARVA_WORKSPACE)
        self._prepare_code_base()
        # We may run some tag tricks
        shutil.rmtree(os.path.join(LARVA_ROOT, ".git"), ignore_errors=True)

    def __exit__(self, exc_type, exc_val, exc_tb):
        os.chdir(self.pwd)
        shutil.rmtree(LARVA_WORKSPACE, ignore_errors=True)
        logging.info("Clear workspace: " + LARVA_WORKSPACE)


class LarvaUpdater:
    @classmethod
    def run(cls):
        try:
            with LarvaWorkspace():
                for root, _, files in os.walk(LARVA_ROOT):
                    no_overwrite = NO_OVERWRITE_OCCUPIER in files
                    for file in [os.path.join(root, f) for f in files]:
                        cls._morph(file, no_overwrite)
            enforce_project_name()
            logging.info("Larva update finishes.")
        except Exception as ex:
            logging.exception("Fail to update larva due to: " + str(type(ex)))

    @classmethod
    def _morph(cls, src_path, no_overwrite):
        dst_path = src_path.replace(LARVA_ROOT, PROJECT_ROOT)
        if no_overwrite and os.path.exists(dst_path):
            logging.info("Skip {} --> {}".format(src_path, dst_path))
        else:
            ensure_local_folder(os.path.dirname(dst_path))
            shutil.copyfile(src_path, dst_path)
            logging.info("Copy {} --> {}".format(src_path, dst_path))


def main():
    LarvaUpdater.run()


if __name__ == '__main__':
    init_local_log()
    main()
