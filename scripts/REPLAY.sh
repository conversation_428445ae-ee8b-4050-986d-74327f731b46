#!/bin/bash
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname $PROJECT_ROOT)"
PROJECT_NAME="$(basename $PROJECT_ROOT)"
source $PROJECT_ROOT/scripts/IMAGE_BUILD.sh

function start_image() {
  (
    set -x
    docker run -it --rm $DOCKER_RT \
      -e JOB_ID=$1 \
      -e SWARM_POSITION=$SWARM_POSITION \
      -e VXCODE_SWARM_ENV=$VXCODE_SWARM_ENV \
      -e VXCODE_DRY_RUN=1 \
      --add-host uat-vxcode-transcode-api.bilibili.co:************* \
      --add-host uat-vxcode-hive-api.bilibili.co:************* \
      --add-host uat-vxcode-narrowband-api.bilibili.co:************* \
      --volume /var/run/lancer:/var/run/lancer:rw \
      --volume $SWARM_LOG_PATH:/data/log/bili-vxcode:rw \
      --volume $SWARM_WORKSPACE_PATH:/data/bili_vxcode_workspace:rw \
      $IMAGE_NAME:latest python3.7 /data/app/$PROJECT_NAME/source/app/main.pyc
  )
  set +x
}

function main() {
  echo "Start Job: $1"
  cd $PROJECT_ROOT
  build_image "latest"
  start_image $1
}

main $@
