#!/bin/bash
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname $PROJECT_ROOT)"
PROJECT_NAME="$(basename $PROJECT_ROOT)"
SWARM_POSITION="idc"
VXCODE_SWARM_ENV="prod"
source $PROJECT_ROOT/scripts/IMAGE_BUILD.sh

function start_image() {
  (
    set -x
    docker run -it --rm $DOCKER_RT \
      --shm-size 2g \
      --cpus 8 \
      -e JOB_ID=$1 \
      -e CPU_NUM=8 \
      -e THREAD_NUM=2 \
      -e HARD_DECODE=0 \
      -e NVIDIA_VISIBLE_DEVICES=1 \
      -e DEPLOY_ENV=prod \
      -e VXCODE_VQA_PURE_DETECTION=1 \
      -e SWARM_POSITION=$SWARM_POSITION \
      -e VXCODE_SWARM_ENV=$VXCODE_SWARM_ENV \
      -e VXCODE_DRY_RUN=1 \
      -e VXCODE_HIVE_V2_ENABLE=1 \
      -v /var/run/lancer:/var/run/lancer:rw \
      -v /data/log/bili-vxcode:/data/log/bili-vxcode:rw \
      -v /data/bili_vxcode_workspace:/data/bili_vxcode_workspace:rw \
      $IMAGE_NAME:latest python3.7 app/main.pyc
  )
  set +x
}

function main() {
  echo "Start Job: $1"
  cd $PROJECT_ROOT
#   build_image "latest"
#  build_image "latest"
  start_image $1
}

main $@
