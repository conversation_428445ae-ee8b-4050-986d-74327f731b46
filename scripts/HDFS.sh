#!/bin/bash
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname $PROJECT_ROOT)"
PROJECT_NAME="$(basename $PROJECT_ROOT)"
source $PROJECT_ROOT/scripts/IMAGE_BUILD.sh

function start_image() {
  (
    set -x
    docker run -it --rm $DOCKER_RT \
      -e SWARM_POSITION=$SWARM_POSITION \
      -e VXCODE_SWARM_ENV=$VXCODE_SWARM_ENV \
      -e HADOOP_CONF_DIR=/data/app/$PROJECT_NAME/conf/hadoop/$VXCODE_SWARM_ENV \
      --volume /var/run/lancer:/var/run/lancer:rw \
      --volume $SWARM_LOG_PATH:/data/log/bili-vxcode:rw \
      --volume $SWARM_WORKSPACE_PATH:/data/bili_vxcode_workspace:rw \
      $IMAGE_NAME:latest hadoop fs $@
  )
  set +x
}

function main() {
  cd $PROJECT_ROOT
  build_image "latest"
  start_image $@
}

main $@
