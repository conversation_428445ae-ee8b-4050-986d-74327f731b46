FROM hub.bilibili.co/maxinjun/bilivqa_v2:v20241225

ENV pypi_host pypi.douban.com/simple
ENV pypi_source "https://${pypi_host}"
ENV TZ=Asia/Shanghai

ARG app_name
ARG app_name_local="archive-qoe"
ADD release/${app_name}/assets /data/app/${app_name_local}/assets
ADD release/${app_name}/source /data/app/${app_name_local}/source
ADD release/${app_name}/conf /data/app/${app_name_local}/conf
ADD release/${app_name}/README.md /data/app/${app_name_local}/README.md
ADD release/${app_name}/scripts /data/app/${app_name_local}/scripts
ADD release/${app_name}/model /data/app/${app_name_local}/model

WORKDIR /data/app/${app_name}

WORKDIR /data/app/${app_name_local}

RUN mkdir /data/app/${app_name_local}/model/images
RUN mkdir /data/app/${app_name_local}/model/images/test

WORKDIR /data/app/${app_name_local}/source

RUN curl -sf "http://upos-sz-staticcos.bilivideo.com/bvcstaticboss/upclone/upclone-install.sh" | bash -s v2.0.20
RUN pip3 install -r /data/app/${app_name_local}/scripts/internals/requirements.txt
RUN pip3 install -r /data/app/${app_name_local}/source/utils/pyutils/requirements.txt -i https://pypi.bilibili.co/repository/pypi-public/simple/
RUN pip3 install pynvml

ENV PATH=/usr/lib/linux-tools/4.15.0-213-generic:${PATH}

RUN chown root:root -R /data/app/${app_name_local}/assets/keys/* && \
	chmod 400 -R /data/app/${app_name_local}/assets/keys/* && \
	mkdir -p /data/bili_vxcode_workspace && \
	chmod 777 -R /data/bili_vxcode_workspace && \
	python3.7 -m compileall -b . && \
    find . -name '__pycache__' -type d -delete



ENV PYTHONPATH=/data/app/${app_name_local}/source
ENV NVIDIA_DRIVER_CAPABILITIES=all
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/workspace/TensorRT-8.2.5.1_cu114/lib
ENV APP_NAME=${app_name_local}