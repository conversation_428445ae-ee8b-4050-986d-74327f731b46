# -*- coding: utf-8 -*-
import datetime
import os
import sys

import requests
import logging
import settings
from importlib import import_module

from utils import IPCClient
from utils import job
from utils import get_hostname
from utils import init_log
from utils import init_local_log
from utils import load_job_request
from utils import generate_error_ctx
from utils import url_add_params
from utils import generate_curl_cmd
from utils import VXCodeHive
from utils import IPCContext
from utils import CostSupervisor
from utils import PerfProfiler
from utils import SHOW_JOB_REQUESTS
from utils import SHOW_SWARM_PROPERTIES
from utils import init_lancer_log
from utils.lancer.lancer import init_lancer_http_log
from utils.lancer.lancer import log_to_lancer


def summarize_platform():
    result = {
        "cpu": settings.SWARM_CPU_MODEL,
        "simd": settings.SWARM_CPU_SIMD,
        "hostname": get_hostname(),
        'env': settings.ENV,
        'position': settings.SWARM_POSITION,
    }
    if settings.SWARM_GPU_EQUIPPED:
        result.update({
            "gpu": settings.SWARM_GPU_NAME,
            "nv-driver": settings.SWARM_GPU_DRIVER_VERSION,
        })
        if settings.SWARM_GPU_CUDA_EQUIPPED:
            result.update({
                "cuda": settings.SWARM_GPU_CUDA_VERSION
            })
    return result


def report_summary(job_id, is_completed, overall_cost):
    summary_data = {
        "is_completed": is_completed,
        "platform": summarize_platform(),
        "cost": overall_cost,
    }
    VXCodeHive.report_summary(job_id, summary_data)
    resource_report = {
        "vxcode_id": job.JOB_VXCODE_ID,
        "task_id": job.JOB_REQUEST.get("task_id", ""),
        "buid": job.JOB_REQUEST.get("buid", ""),
        "image": job.JOB_IMAGE,
        "version": job.JOB_VERSION,
        "queen": job.JOB_QUEEN,
        "request_info": job.JOB_REQUEST,
        "resource_info": job.JOB_RESOURCE,
        "route_info": job.JOB_ROUTE,
        "cost_info": summary_data["cost"],
        "platform_info": summary_data["platform"],
        "utime": int(datetime.datetime.now().timestamp() * 1000),
        "status": "DONE" if is_completed else "FAIL",
    }
    if settings.VXCODE_DRY_RUN:
        logging.debug("Skip summary report in dry-run mode")
        logging.info(f'Dry-run summary: <{resource_report}>')
    else:
        logging.info(f'Report summary: <{resource_report}>')
        IPCClient.report_resource(**resource_report)

    log_to_lancer(f'Report resource to lancer, {resource_report}', extra=resource_report)


class JobContext:
    def __init__(self, job_id):
        self.job_id = job_id
        self.is_done = False
        self.result = None
        self.result_status = "FAIL"

    def fire_callback(self):
        if settings.VXCODE_DRY_RUN:
            logging.info("Skip callback report in dry-run mode")
        elif settings.SWARM_IS_EDGE:
            logging.info("Relay callback report in edge clusters")
            self.relay_callback()
        elif job.JOB_CALLBACK is None:
            logging.info("Relay callback as no callback url available")
            self.relay_callback()
        else:
            method = "post"
            swarm_params = {
                "swarm_queen": settings.SWARM_QUEEN,
                "swarm_instance": settings.SWARM_INSTANCE_NAME,
            }
            callback_url = url_add_params(job.JOB_CALLBACK, swarm_params)
            callback_data = {
                "vxcode_id": self.job_id,
                "result": self.result_status,
                "details": self.result
            }
            request_kwargs = {"json": callback_data}
            for attempt in range(settings.CALLBACK_RETRIES):
                try:
                    resp = requests.request(method, callback_url, **request_kwargs)
                    resp.raise_for_status()
                    logging.info(f"Callback [{self.result_status}] to {callback_url} finishes")
                    return
                except Exception as ex:
                    logging.error(
                        f"Callback [{self.result_status}] to {callback_url} failed @attemp {attempt} due to {ex}")
            else:
                logging.error(f"Give up callback [{self.result_status}] to {callback_url}")
                curl_cmd = generate_curl_cmd(method, callback_url, **request_kwargs)
                logging.info(f"Please retry with: {curl_cmd}")
                logging.info(f"Ask HIVE to relay the callback")
                self.relay_callback()

    def relay_callback(self):
        if settings.VXCODE_DRY_RUN:
            logging.debug("Skip callback relay in dry-run mode")
        else:
            callback_data = {
                "vxcode_id": self.job_id,
                "result": self.result_status,
                "details": self.result
            }
            VXCodeHive.relay_callback(callback_data)

    def run(self):
        try:
            load_job_request(self.job_id)
            SHOW_JOB_REQUESTS()
            self.result = self._run()
            self.is_done = True
            self.result_status = "DONE"
            logging.info(f"Job [{self.job_id}] get result: {self.result}")
        except Exception as ex:
            logging.exception(f"Job [{self.job_id}] fails due to {ex}")
            self.result = generate_error_ctx(ex)
        self.fire_callback()

    def _run(self):
        flow_name = job.JOB_REQUEST["flow"]
        class_name = f"{flow_name.capitalize()}Flow"
        module = import_module(f"flows.{flow_name}")
        flow = getattr(module, class_name)
        return flow.run()


def main():
    job_id = os.getenv("JOB_ID")
    app_id = settings.OPS_LOG_APP_ID
    init_local_log()
    init_lancer_log("compare_lancer_logger", settings.COMPARE_LANCER_LOG_ID, settings.COMPARE_LANCER_FIELD_NAMES)
    init_lancer_log("quality_lancer_logger", settings.QUALITY_LANCER_LOG_ID, settings.QUALITY_LANCER_FIELD_NAMES)
    init_lancer_log("resource_lancer_logger", settings.RESOURCE_LANCER_LOG_ID, settings.RESOURCE_LANCER_FIELD_NAMES)
    init_lancer_http_log()
    with IPCContext():
        SHOW_SWARM_PROPERTIES()
        with PerfProfiler() as main_profiler:
            init_log(app_id)
            job_ctx = JobContext(job_id)
            job_ctx.run()
        main_cost = main_profiler.latest_cost
        CostSupervisor.merge_cost(main_cost)
        report_summary(job_id, job_ctx.is_done, CostSupervisor.summarize())


if __name__ == '__main__':
    sys.exit(main())
