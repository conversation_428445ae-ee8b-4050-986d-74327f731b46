# -*- coding: utf-8 -*-
import os
from urllib.parse import urlparse

import settings
from utils import VXCodeKnownError


class UnsupportedFuseMapScheme(VXCodeKnownError):
    def __init__(self, scheme):
        super(UnsupportedFuseMapScheme, self).__init__()
        self.err_dict['error_details'] = {'scheme': scheme}


class Fuse:
    LocalMappings = {
        settings.UPOS_OBS_SCHEMA: settings.SWARM_FUSE_UPOS_ROOT,
        settings.BOSS_OBS_SCHEMA: settings.SWARM_FUSE_BOSS_ROOT,
        settings.LUFFY_OBS_SCHEMA: settings.SWARM_FUSE_LUFFY_ROOT,
        settings.HDFS_SCHEMA: settings.SWARM_FUSE_HDFS_ROOT,
        settings.FILE_SCHEMA: "",
    }

    @classmethod
    def can_be_mapped(cls, url):
        return settings.SWARM_FUSE_DAEMON_WORKING and (
                cls.LocalMappings.get(urlparse(url).scheme, None) is not None
        )

    @classmethod
    def run(cls, url):
        scheme = urlparse(url).scheme
        if scheme not in cls.LocalMappings:
            raise UnsupportedFuseMapScheme(scheme)

        # 3 means "://"
        _url = url[len(scheme) + 3:]
        return os.path.join(cls.LocalMappings[scheme], _url)
