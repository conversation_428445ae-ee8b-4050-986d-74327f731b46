# -*- coding: utf-8 -*-
import logging
import os
from urllib.parse import urlparse

import settings
from flows.operations.IOs.Download import RsyncDownloadFailure
from flows.operations.IOs.Download import UnsupportedDownloadScheme
from utils import copy_file
from utils import CostSupervisor
from utils import LOCAL_PATH
from utils import VXCodeKnownError


class UnsupportedUploadScheme(UnsupportedDownloadScheme):
    pass


class RsyncUploadFailure(RsyncDownloadFailure):
    pass


class HDFSUploadFailure(RsyncDownloadFailure):
    pass


class UPOSUploadFailure(RsyncDownloadFailure):
    pass


class LFOSUploadFailure(RsyncDownloadFailure):
    pass


class HTTPUploadFailure(RsyncUploadFailure):
    pass


class FileUploadFailure(RsyncUploadFailure):
    pass


class LuffyUploadFailure(VXCodeKnownError):
    def __init__(self, url, ex):
        self.err_dict.update({
            "should_retry": True,
            "error_details": {"url": url,
                              "reason": str(ex)}
        })


class Upload:
    @classmethod
    def dry_run(cls, path, url):
        scheme = urlparse(url).scheme
        download_method_name = f"_run_{scheme}_upload"
        logging.info(f"Transfer {path} --> {url}, scheme: {scheme}")
        if download_method_name not in vars(cls):
            raise UnsupportedUploadScheme(scheme)
        upload_method = getattr(cls, download_method_name)
        if not callable(upload_method):
            raise UnsupportedUploadScheme(scheme)

        upload_size = os.path.getsize(path)
        timeout_in_sec = cls._calculate_timeout(upload_size)
        upload_method(path, url, timeout_in_sec)

    @classmethod
    def run(cls, path, url):
        if settings.VXCODE_DRY_RUN:
            logging.debug("Skip uploading in dry-run mode")
            remote_filename = os.path.basename(url)
            remote_mapping = LOCAL_PATH(remote_filename)
            if remote_mapping != path:
                copy_file(path, remote_mapping)
        else:
            logging.info(f"Transfer {path} --> {url}")
            scheme = urlparse(url).scheme
            download_method_name = f"_run_{scheme}_upload"
            if download_method_name not in vars(cls):
                raise UnsupportedUploadScheme(scheme)
            upload_method = getattr(cls, download_method_name)
            if not callable(upload_method):
                raise UnsupportedUploadScheme(scheme)

            upload_size = os.path.getsize(path)
            timeout_in_sec = cls._calculate_timeout(upload_size)
            upload_method(path, url, timeout_in_sec)

    @classmethod
    def _run_http_upload(cls, path, url, timeout):
        logging.info(f"Run HTTP Upload: {url}")
        cmd = f"curl -F 'data=@{path}' {url}"
        for _ in range(settings.UPLOAD_RETRIES):
            result = CostSupervisor.run(cmd, timeout)
            if not result["retcode"]:
                return result
        raise HTTPUploadFailure(url, result)

    @classmethod
    def _run_upos_upload(cls, path, url, timeout):
        logging.info(f"Run UPOS Upload: {url}")
        for retry in range(settings.UPLOAD_RETRIES):
            endpoint_idx = retry % len(settings.UPOS_UPLOAD_END_POINTS)

            upload_cmd = f"{settings.UPCLONE_CMD} copyto {path} {url} " \
                         f"--retries 3 --config {settings.UPCLONE_UPOS_CONF_PATH} " \
                         f"--upos-chunk-size {settings.UPOS_PART_SIZE}M " \
                         f"--upos-upload-concurrency {settings.UPOS_THREAD_NUM} " \
                         f"--timeout {settings.UPOS_UPLOAD_TIMEOUT}s " \
                         f"--upos-endpoint {settings.UPOS_UPLOAD_END_POINTS[endpoint_idx]} " \
                         f"--upos-extra-query \"{settings.UPOS_EXTRA_QUERY}\""

            result = CostSupervisor.run(upload_cmd, timeout)
            if not result["retcode"]:
                return result
        raise UPOSUploadFailure(url, result)

    @classmethod
    def _run_lfos_upload(cls, path, url, timeout):
        logging.info(f"Run LFOS Upload: {url}")
        result = {}
        for retry in range(settings.UPLOAD_RETRIES):
            upload_cmd = f"{settings.UPCLONE_CMD} copyto {path} {url} " \
                         f"--retries 3 --config {settings.UPCLONE_LFOS_CONF_PATH} " \
                         f"--upos-chunk-size {settings.UPOS_PART_SIZE}M " \
                         f"--s3-upload-concurrency {settings.UPOS_THREAD_NUM} " \
                         f"--timeout {max(settings.UPOS_UPLOAD_TIMEOUT, timeout)}s"

            result = CostSupervisor.run(upload_cmd, timeout)
            if not result["retcode"]:
                return result
        raise LFOSUploadFailure(url, result)

    @classmethod
    def _run_hdfs_upload(cls, path, url, timeout):
        logging.info(f"Run HDFS Upload: {url}")
        hdfs_path = url.replace("hdfs://", "")
        upload_cmd = f"hdfscli upload --alias={settings.SWARM_NAME} {path} {hdfs_path} --force"
        for _ in range(settings.UPLOAD_RETRIES):
            result = CostSupervisor.run(upload_cmd, timeout)
            if not result["retcode"]:
                return result
        raise HDFSUploadFailure(url, result)

    @classmethod
    def _run_rsync_upload(cls, path, url, timeout):
        logging.info(f"Run RSYNC Upload: {url}")
        rsync_url = url.replace("rsync://", f"rsync://{settings.RSYNC_USER}")
        download_cmd = f"rsync -tv --contimeout=3 --password-file={settings.RSYNC_PSD_PATH} {path} {rsync_url}"
        for _ in range(settings.UPLOAD_RETRIES):
            result = CostSupervisor.run(download_cmd, timeout)
            if not result["retcode"]:
                return result
        raise RsyncUploadFailure(url, result)

    @classmethod
    def _run_file_upload(cls, path, url, timeout):
        logging.info(f"Run FILE Upload: {url}")
        remote_path = url.replace("file://", "")
        for _ in range(settings.UPLOAD_RETRIES):
            result = copy_file(path, remote_path)
            if not result["retcode"]:
                return result
        raise FileUploadFailure(url, result)

    @classmethod
    def _calculate_timeout(cls, size):
        return max(float(size) / settings.MIN_DOWNLOAD_BYTES_PER_SEC, 15)
