# -*- coding: utf-8 -*-
import logging
import os
from urllib.parse import urlparse

import settings
from flows.operations.IOs.FUSE import Fuse
from utils import checksum_md5
from utils import CostSupervisor
from utils import ExecutionError
from utils import link_file
from utils import move_file
from utils import VXCodeKnownError


class UnsupportedDownloadScheme(VXCodeKnownError):
    def __init__(self, scheme):
        super(UnsupportedDownloadScheme, self).__init__()
        self.err_dict['error_details'] = {'scheme': scheme}


class ChecksumIncorrect(VXCodeKnownError):
    def __init__(self, request_md5, received_md5, url):
        super(ChecksumIncorrect, self).__init__()
        self.err_dict.update({
            "should_retry": True,
            "error_details": {
                "url": url,
                "request_md5": request_md5,
                "received_md5": received_md5,
            }})


class FileSizeIncorrect(VXCodeKnownError):
    def __init__(self, requested_size, received_size, url):
        super(FileSizeIncorrect, self).__init__()
        self.err_dict.update({
            "should_retry": True,
            "error_details": {
                "url": url,
                "request_size": requested_size,
                "received_size": received_size,
            }})


class DecryptionFailure(ExecutionError):
    pass


class RsyncDownloadFailure(ExecutionError):
    def __init__(self, url, result):
        super(RsyncDownloadFailure, self).__init__(result)
        self.err_dict['error_details'].update({'url': url})
        self.err_dict['should_retry'] = True


class HDFSDownloadFailure(RsyncDownloadFailure):
    pass


class UPOSDownloadFailure(RsyncDownloadFailure):
    pass


class FILEDownloadFailure(RsyncDownloadFailure):
    pass


class HTTPDownloadFailure(RsyncDownloadFailure):
    ARIA_EXIT_REASONS = [
        'NoError', 'UnknownError', 'DownloadTimeout',
        'ResourceNotFound', 'MaxFileNotFound', 'SpeedTooSlow',
        'NetworkProblem', 'Interrupted', 'RemoteNotSupportedResume',
        'NoSpace', 'PieceLengthDifferent', 'SameFile',
        'SameInfoHashTorrent', 'AlreadyExists', 'RenameFailed',
        'OpenFileError', 'CreateFileError', 'IOError',
        'MkDirFail', 'NameResolutionFail', 'MetalinkParseFail',
        'FTPCommandFail', 'BadHTTPResponseHeader', 'TooManyRedirects',
        'HTTPAuthFail', 'BencodeParseFail', 'CorruptedTorrentFile',
        'BadMagnetURI', 'InvalidArgument', 'RemoteMaintenance',
        'JSON-RPCParseFail', 'Reserved', 'ChecksumInvalid',
    ]
    UNEXPECTED_REASON = "UnableToExecute"

    def __init__(self, url, result):
        super(HTTPDownloadFailure, self).__init__(url, result)
        retcode = result["retcode"]
        if retcode == CostSupervisor.RET_TIMEOUT:
            fail_reason = self.ARIA_EXIT_REASONS[5]
        elif retcode < len(self.ARIA_EXIT_REASONS):
            fail_reason = self.ARIA_EXIT_REASONS[retcode]
        else:
            fail_reason = self.UNEXPECTED_REASON
        self.err_dict["error_details"].update({"reason": fail_reason})


class Download:
    @classmethod
    def dry_run(cls, url, path, need_fuse: bool = False):
        logging.info(f"Transfer {url} --> {path}")
        if need_fuse and Fuse.can_be_mapped(url):
            return Fuse.run(url)
        temp_download_path = f"{path}.tmp"
        scheme = urlparse(url).scheme
        download_method = getattr(cls, f"_run_{scheme}_download", None)
        if not callable(download_method):
            raise UnsupportedDownloadScheme(scheme)
        timeout_in_sec = 3600
        download_method(url, temp_download_path, timeout_in_sec)
        move_file(temp_download_path, path)
        return path

    @classmethod
    def run(cls, url, md5, size, path):
        logging.info(f"Transfer {url} --> {path}")
        if os.path.exists(path) and os.path.getsize(path) == size:
            if settings.VXCODE_DRY_RUN:
                logging.info(f"{path} already exists, bypassing download")
                return path
            elif md5 == checksum_md5(path):
                logging.info(f"{path} already exists, bypassing download")
                return path
        # Try to use FUSE daemon to get remote files
        if Fuse.can_be_mapped(url):
            mapped_path = Fuse.run(url)
            mapped_size = os.path.getsize(mapped_path)
            if mapped_size != size:
                raise FileSizeIncorrect(size, mapped_size, url)
            return mapped_path

        filename = os.path.basename(urlparse(url).path)
        decrypt_needed = filename.endswith("yase")
        temp_download_path = f"{path}.tmp"
        scheme = urlparse(url).scheme
        download_method_name = f"_run_{scheme}_download"
        if download_method_name not in vars(cls):
            raise UnsupportedDownloadScheme(scheme)
        download_method = getattr(cls, download_method_name)
        if not callable(download_method):
            raise UnsupportedDownloadScheme(scheme)
        timeout_in_sec = cls._calculate_timeout(decrypt_needed, size)
        download_method(url, temp_download_path, timeout_in_sec)
        if decrypt_needed:
            cls._decrypt_file(temp_download_path, path)
        else:
            move_file(temp_download_path, path)
        if not settings.VXCODE_DRY_RUN:
            received_md5 = checksum_md5(path)
            if md5 != received_md5:
                raise ChecksumIncorrect(md5, received_md5, url)
        return path

    @classmethod
    def _run_http_download(cls, url, path, timeout):
        logging.info(f"Run HTTP download: {url}")
        download_folder = os.path.dirname(path)
        download_filename = os.path.basename(path)
        concurrency = 4 if settings.SWARM_IS_EDGE else 1
        download_cmd = f"aria2c -j {concurrency} -s {concurrency} -x {concurrency} " \
                       f"--max-file-not-found={settings.DOWNLOAD_RETRIES} " \
                       f"--max-tries={settings.DOWNLOAD_RETRIES} " \
                       f"-d {download_folder} -o {download_filename} {url}"
        result = CostSupervisor.run(download_cmd, timeout)
        if result["retcode"]:
            raise HTTPDownloadFailure(url, result)
        return result

    @classmethod
    def _run_hdfs_download(cls, url, path, timeout):
        logging.info(f"Run HDFS download: {url}")
        hdfs_path = url.replace("hdfs://", "")
        download_cmd = f"hdfscli download --alias={settings.SWARM_NAME} {hdfs_path} {path} --force"

        for _ in range(settings.DOWNLOAD_RETRIES):
            result = CostSupervisor.run(download_cmd, timeout)
            if not result["retcode"]:
                return result
        raise HDFSDownloadFailure(url, result)

    @classmethod
    def _run_upos_download(cls, url, path, timeout):
        logging.info(f"Run UPOS download: {url}")

        for retry in range(settings.DOWNLOAD_RETRIES):
            endpoint_idx = retry % len(settings.UPOS_DOWNLOAD_END_POINTS)
            download_cmd = f"{settings.UPCLONE_CMD} --config {settings.UPCLONE_UPOS_CONF_PATH} copyfileto {url} {path}" \
                           f" --upos-endpoint {settings.UPOS_DOWNLOAD_END_POINTS[endpoint_idx]} --upos-extra-query" \
                           f" \"{settings.UPOS_EXTRA_QUERY}\""
            result = CostSupervisor.run(download_cmd, timeout)
            if not result["retcode"]:
                return result
        raise UPOSDownloadFailure(url, result)

    @classmethod
    def _run_rsync_download(cls, url, path, timeout):
        logging.info(f"Run RSYNC download: {url}")
        rsync_url = url.replace("rsync://", f"rsync://{settings.RSYNC_USER}")
        download_cmd = f"rsync -tv --contimeout=3 --password-file={settings.RSYNC_PSD_PATH} {rsync_url} {path}"
        for _ in range(settings.DOWNLOAD_RETRIES):
            result = CostSupervisor.run(download_cmd, timeout)
            if not result["retcode"]:
                return result
        raise RsyncDownloadFailure(url, result)

    @classmethod
    def _run_file_download(cls, url, path, timeout):
        logging.info(f"Run FILE download: {url}")
        src_path = url.replace("file://", "")
        for _ in range(settings.DOWNLOAD_RETRIES):
            result = link_file(src_path, path)
            if not result["retcode"]:
                return result
        raise FILEDownloadFailure(url, result)

    @classmethod
    def _decrypt_file(cls, src, dst):
        YASE_GENERAL_EXE = os.path.join(settings.EXEC_ROOT, "yase")
        YASE_KEY_PATH = os.path.join(settings.KEYS_ROOT, "yase.psd")
        decrypt_cmd = f"{YASE_GENERAL_EXE} -i {src} -o {dst} -c {YASE_KEY_PATH}"
        result = CostSupervisor.run(decrypt_cmd)
        if result["retcode"]:
            raise DecryptionFailure(result)

    @classmethod
    def _calculate_timeout(cls, decrypted, size):
        min_download_bytes_per_sec = settings.MIN_DOWNLOAD_BYTES_PER_SEC
        if decrypted:
            min_download_bytes_per_sec /= 2
        return max(float(size) / min_download_bytes_per_sec, 15)
