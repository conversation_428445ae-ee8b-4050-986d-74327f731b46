# -*- coding: utf-8 -*-
"""
Archive QoE 完整处理流程模块

整合数据预处理和质量推理的完整工作流程
通过本地文件传递，避免中间的网络传输开销
"""
import logging
import time

from flows.sample import SampleFlow
from flows.vqa import VqaFlow
from utils import job


class QoeFlow(object):
    """完整的QoE处理流程类"""
    
    @classmethod
    def run(cls, job_request=None):
        """执行完整的QoE处理流程"""
        if not job_request:
            job_request = job.JOB_REQUEST

        start_time = time.time()
        logging.info(f'Starting complete QoE pipeline, job params: cid={job_request.get("cid", "N/A")}, video_uri={job_request.get("video_uri", "N/A")}')

        try:
            # 步骤1: 执行数据预处理（不上传tensor文件）
            logging.info("Step 1: Data preprocessing with SampleFlow")
            
            sample_job_request = job_request.copy()
            sample_job_request['upload_tensor'] = False
            
            sample_result = SampleFlow.run(sample_job_request)
            logging.info(f"SampleFlow completed, local tensor: {sample_result.get('local_tensor_path')}")
            
            local_tensor_path = sample_result.get('local_tensor_path')
            if not local_tensor_path:
                raise Exception("SampleFlow did not return local_tensor_path")
            
            # 步骤2: 使用本地tensor文件执行质量推理
            logging.info("Step 2: Quality prediction with VqaFlow")
            
            vqa_job_request = job_request.copy()
            vqa_job_request['local_tensor_path'] = local_tensor_path
            
            vqa_result = VqaFlow.run(vqa_job_request)
            logging.info(f"VqaFlow completed with scores: ori_score={vqa_result.get('score')}, percent_score={vqa_result.get('percent_score')}")

            elapsed_time = time.time() - start_time
            logging.info(f'Complete QoE pipeline finished successfully, total_duration: {elapsed_time:.2f}s')

            # 添加总执行时间到结果中
            vqa_result['exec_time'] = elapsed_time
            return vqa_result

        except Exception as ex:
            elapsed_time = time.time() - start_time
            logging.error(f"QoE pipeline failed, duration: {elapsed_time:.2f}s, error: {str(ex)}")
            raise


def main():
    """命令行入口，支持直接运行完整的QoE流程（sample+vqa）"""
    import argparse
    import json
    import os
    
    parser = argparse.ArgumentParser(description='Archive QoE 完整处理流程（数据预处理+质量推理）')
    parser.add_argument('--cid', type=str, default='manual_test', help='内容ID')
    parser.add_argument('--uri', type=str, default='manual://test', help='视频URI')
    parser.add_argument('--video_uri', type=str, default='', help='视频源URI')
    parser.add_argument('--hard_decode', action='store_true', help='启用硬件解码')
    parser.add_argument('--spatial_resolution', type=int, default=510, help='空间分辨率')
    parser.add_argument('--temporal_resolution', type=int, default=160, help='时间分辨率')
    parser.add_argument('--num_processes', type=int, default=2, help='并行进程数')
    parser.add_argument('--num_threads', type=int, default=2, help='线程数')
    parser.add_argument('--vqa_version', type=str, choices=['original', 'cpu_optimized'], 
                        default='original', help='VQA版本选择')
    parser.add_argument('--pure_detection', action='store_true', help='启用纯色检测')
    parser.add_argument('--output_json', type=str, help='输出JSON结果到文件')
    parser.add_argument('--verbose', action='store_true', help='详细日志输出')
    
    args = parser.parse_args()
    
    # 配置日志级别
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置环境变量
    if args.vqa_version:
        os.environ['VXCODE_VQA_VERSION'] = args.vqa_version
    
    if args.pure_detection:
        os.environ['VXCODE_VQA_PURE_DETECTION'] = '1'
    
    # 设置其他环境变量
    os.environ['VXCODE_VQA_SPATIAL_RESOLUTION'] = str(args.spatial_resolution)
    os.environ['VXCODE_VQA_TEMPORAL_RESOLUTION'] = str(args.temporal_resolution)
    
    # 构建作业请求
    job_request = {
        'cid': args.cid,
        'uri': args.uri,
        'video_uri': args.video_uri if args.video_uri else args.video_path,
        'hard_decode': args.hard_decode,
        'spatial_resolution': args.spatial_resolution,
        'temporal_resolution': args.temporal_resolution,
        'num_processes': args.num_processes,
        'num_threads': args.num_threads
    }
    
    try:
        print(f"开始完整QoE流程（sample+vqa），VQA版本: {args.vqa_version}")
        print(f"参数: {json.dumps(job_request, indent=2, ensure_ascii=False)}")
        
        # 执行完整QoE流程
        result = QoeFlow.run(job_request)
        
        print(f"\n=== QoE处理结果 ===")
        print(f"原始分数: {result.get('score', 'N/A')}")
        print(f"百分制分数: {result.get('percent_score', 'N/A')}")
        print(f"纯色标识: {result.get('pure_tag', 'N/A')}")
        print(f"总执行时间: {result.get('exec_time', 'N/A'):.2f}s")
        
        # 输出JSON结果到文件
        if args.output_json:
            with open(args.output_json, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"结果已保存到: {args.output_json}")
        
        logging.info(f'完整QoE流程结果: {json.dumps(result, indent=2, ensure_ascii=False)}')
        return 0
        
    except Exception as e:
        print(f"QoE流程失败: {e}")
        if args.verbose:
            logging.exception("详细错误信息:")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())