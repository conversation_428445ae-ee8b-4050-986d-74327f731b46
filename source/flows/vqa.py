# -*- coding: utf-8 -*-
"""
Archive QoE VQA 视频质量推理模块

基于预处理的tensor特征文件进行视频质量评估
支持本地tensor文件或从云端URI下载，使用TensorRT优化的深度学习模型
"""
import logging
import os
import sys
import time

import numpy as np
import pycuda.driver as cuda
import tensorrt as trt

from flows.operations.IOs.Download import Download
from utils import job
from utils import VQAExecutionError
from utils.error_type import *

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))

try:
    import pycuda.autoinit
except:
    raise BiliVqaGPUError()


class VqaFlow(object):
    """视频质量推理流程类"""
    
    @classmethod
    def run(cls, job_request=None):
        """执行视频质量推理的完整流程"""
        cls._validate_environment()

        if not job_request:
            job_request = job.JOB_REQUEST

        start_time = time.time()
        run_timing_stats = {}
        logging.info(f'Starting Archive QoE inference, job params: cid={job_request.get("cid", "N/A")}, tensor_uri={job_request.get("tensor_uri", "N/A")}, local_tensor_path={job_request.get("local_tensor_path", "N/A")}')

        try:
            cls._validate_job_params(job_request)
            params = cls._parse_job_params(job_request)
            
            # 并行化：tensor文件获取与TensorRT模型初始化同时进行
            from concurrent.futures import ThreadPoolExecutor
            
            logging.info("开始并行处理：获取tensor文件 & 创建TensorRT模型")
            parallel_start = time.time()

            # 启动tensor文件获取任务
            with ThreadPoolExecutor(max_workers=1) as executor:
                tensor_start_time = time.time()
                tensor_future = executor.submit(cls._get_tensor_file, params)
                
                # 在主线程中同时进行TensorRT模型初始化
                logging.info("在主线程中创建TensorRT模型...")
                model_init_start = time.time()
                from model.bilivqa_v2.trt_model import TrtBackend
                
                # 获取model配置用于后续处理
                model_config = cls._select_model()
                trt_model = TrtBackend(model_config['trt_file'], 4096, 4096)
                model_init_time = time.time() - model_init_start
                logging.info(f"TensorRT模型创建完成，耗时: {model_init_time:.3f}s")
                
                # 等待tensor文件获取完成
                local_tensor_file = tensor_future.result()
                tensor_get_time = time.time() - tensor_start_time
                logging.info(f"tensor文件获取完成，耗时: {tensor_get_time:.3f}s")

            parallel_time = time.time() - parallel_start
            logging.info(f"并行处理完成，耗时: {parallel_time:.3f}s")
            
            # 记录并行处理耗时
            run_timing_stats['parallel_time'] = parallel_time
            run_timing_stats['model_init_time'] = model_init_time
            run_timing_stats['tensor_get_time'] = tensor_get_time
            
            ori_score, percent_score, pure_tag, vqa_timing_stats = cls._execute_inference(local_tensor_file, trt_model)
            elapsed_time = time.time() - start_time
            
            # 汇总所有耗时统计
            run_timing_stats.update(vqa_timing_stats)
            run_timing_stats['total_run_time'] = elapsed_time
            
            # 输出完整的耗时统计
            cls._log_complete_timing_stats(run_timing_stats)
            cls._log_results(elapsed_time, (ori_score, percent_score, pure_tag))
            return cls._format_response_vqa(params, (ori_score, percent_score, pure_tag), elapsed_time)
            
        except Exception as ex:
            elapsed_time = time.time() - start_time
            logging.error(f'VQA inference failed, duration: {elapsed_time:.2f}s, error: {str(ex)}')
            raise

    @staticmethod
    def _parse_job_params(job_request):
        """解析作业请求参数"""
        params = {
            'cid': job_request.get('cid', ''),
            'uri': job_request['uri'],
            'video_uri': job_request.get('video_uri', ''),
        }
        
        if 'local_tensor_path' in job_request:
            params['local_tensor_path'] = job_request['local_tensor_path']
        if 'tensor_uri' in job_request:
            params['tensor_uri'] = job_request['tensor_uri']
            
        return params

    @classmethod
    def _get_tensor_file(cls, params):
        """获取tensor文件，优先使用本地路径"""
        if 'local_tensor_path' in params and params['local_tensor_path']:
            local_tensor_path = params['local_tensor_path']
            if os.path.exists(local_tensor_path):
                logging.info(f'Using local tensor file: {local_tensor_path}')
                return local_tensor_path
            else:
                raise VQAExecutionError(f'Local tensor file not found: {local_tensor_path}')
        
        if 'tensor_uri' in params and params['tensor_uri']:
            logging.info(f'Downloading tensor file from URI: {params["tensor_uri"]}')
            return cls._download_tensor(params)
        
        raise VQAExecutionError('Neither local_tensor_path nor tensor_uri provided')

    @staticmethod
    def _download_file(tensor_uri, local_path):
        """底层文件下载函数"""
        try:
            return Download.dry_run(tensor_uri, local_path)
        except Exception as ex:
            err_obj = VQAExecutionError(str(ex))
            err_obj.should_retry = True
            raise err_obj

    @staticmethod
    def _validate_environment():
        """验证运行环境配置"""
        env = os.getenv("VXCODE_SWARM_ENV")
        if env is None or env not in {'prod', 'uat'}:
            raise VQAExecutionError(f'ENV [{env}] is incorrect, should be set to uat or prod')

    @classmethod
    def _download_tensor(cls, params):
        """从云端URI下载tensor文件"""
        tensor_uri = params['tensor_uri']
        tensor_ext = os.path.splitext(tensor_uri)[1]
        tensor_file = f'tmp_tensor{tensor_ext}'
        
        logging.info(f'Downloading tensor file from {tensor_uri}')
        local_tensor_file = cls._download_file(tensor_uri, tensor_file)
        logging.info(f'Tensor file downloaded successfully: {local_tensor_file}')
        
        return local_tensor_file
    
    @classmethod
    def _execute_inference(cls, local_tensor_file, trt_model):
        """执行视频质量推理"""
        model_config = cls._select_model()
        detection_config = cls._configure_detection()
        
        logging.info('Starting video quality prediction...')
        ori_score, percent_score, pure_tag, timing_stats = cls._predict_video_quality(
            local_tensor_file, trt_model, model_config, detection_config)
        
        logging.info(f'Video quality prediction completed: ori_score={ori_score}, percent_score={percent_score}, pure_tag={pure_tag}')
        
        return ori_score, percent_score, pure_tag, timing_stats

    @staticmethod
    def _validate_job_params(job_request):
        """验证作业请求参数"""
        if 'tensor_uri' not in job_request and 'local_tensor_path' not in job_request:
            raise VQAExecutionError('Either tensor_uri or local_tensor_path must be provided')
        
        if 'uri' not in job_request:
            raise VQAExecutionError('Parameter [uri] is not in job request')

    @staticmethod
    def _format_file_size(file_size):
        """格式化文件大小为可读字符串"""
        if file_size >= 1024 * 1024 * 1024:  # GB
            return f"{file_size / (1024 * 1024 * 1024):.2f} GB"
        elif file_size >= 1024 * 1024:  # MB
            return f"{file_size / (1024 * 1024):.2f} MB"
        elif file_size >= 1024:  # KB
            return f"{file_size / 1024:.2f} KB"
        else:  # Bytes
            return f"{file_size} Bytes"

    @staticmethod
    def _select_model():
        """根据GPU设备选择TensorRT模型"""
        device = cuda.Device(0)
        major, minor = device.compute_capability()
        sm_version = major * 10 + minor

        trt_file = None
        if sm_version == 86:
            trt_file = "/workspace/bilivqa_v2_ckpts/IMDT_stju_a10_cuda113_reso_plus.trt"
        elif sm_version == 75:
            trt_file = "/workspace/bilivqa_v2_ckpts/IMDT_stju_t4_cuda113_reso_plus.trt"
        elif sm_version == 70:
            trt_file = "/workspace/bilivqa_v2_ckpts/IMDT_stju_v100_cuda113_reso_plus.trt"
        else:
            raise BiliVqaGetEngineError(sm_version)

        # 记录模型文件大小
        try:
            model_size = os.path.getsize(trt_file)
            size_str = VqaFlow._format_file_size(model_size)
            logging.info(f"选择TensorRT模型: {os.path.basename(trt_file)}, 大小: {size_str}, GPU算力: {sm_version}")
        except OSError:
            logging.warning(f"无法获取模型文件大小: {trt_file}")

        return {
            'trt_file': trt_file,
            'lut_file': "/workspace/bilivqa_v2_ckpts/vqa_v2_2_benchmark.csv"
        }

    @staticmethod
    def _configure_detection():
        """配置检测参数"""
        return {
            'pure_detection': int(os.getenv("VXCODE_VQA_PURE_DETECTION", 0)) == 1,
            'spatial_resolution': int(os.getenv("VXCODE_VQA_SPATIAL_RESOLUTION", 510)),
            'temporal_resolution': int(os.getenv("VXCODE_VQA_TEMPORAL_RESOLUTION", 160))
        }

    @staticmethod
    def _predict_video_quality(tensor_file, trt_model, model_config, detection_config):
        try:
            from model.bilivqa_v2.vqa_process import pred_video_quality
            return pred_video_quality(
                tensor_file,
                trt_model,
                model_config['lut_file'],
                detection_config['pure_detection']
            )
        except Exception as ex:
            if isinstance(ex, (BiliVqaTensorRTError, BiliVqaVideoError)):
                raise
            else:
                raise VQAExecutionError(str(ex))

    @staticmethod
    def _log_results(duration, result):
        """记录推理结果"""
        ori_score, percent_score, pure_tag = result
        logging.info(f'VQA inference completed successfully, duration: {duration:.2f}s, ori_score: {ori_score}, percent_score: {percent_score}, pure_color: {pure_tag}')

    @staticmethod
    def _log_complete_timing_stats(run_timing_stats):
        """输出完整的VQA流程耗时统计（从run开始汇总）"""
        import json
        
        total_run_time = run_timing_stats.get('total_run_time', 0)
        parallel_time = run_timing_stats.get('parallel_time', 0)
        model_init_time = run_timing_stats.get('model_init_time', 0)
        tensor_get_time = run_timing_stats.get('tensor_get_time', 0)
        func_total_time = run_timing_stats.get('func_total_time', 0)
        tensor_load_time = run_timing_stats.get('tensor_load_time', 0)
        inference_loop_time = run_timing_stats.get('inference_loop_time', 0)
        inference_total_time = run_timing_stats.get('inference_total_time', 0)
        data_preprocess_total_time = run_timing_stats.get('data_preprocess_total_time', 0)
        pure_detection_total_time = run_timing_stats.get('pure_detection_total_time', 0)
        postprocess_time = run_timing_stats.get('postprocess_time', 0)
        
        # 构建JSON格式的耗时统计
        timing_json = {
            "VQA总耗时": f"{round(total_run_time, 3)}s",
            "clips数量": run_timing_stats.get('key_areas_num', 0),
            "帧数": run_timing_stats.get('total_frames', 0),
            "并行": {
                "耗时": f"{round(parallel_time, 3)}s", 
                "占比": f"{round(parallel_time/total_run_time*100, 1)}%",
                "子任务": {
                    "Tensor下载": {"耗时": f"{round(tensor_get_time, 3)}s", "占比": f"{round(tensor_get_time/total_run_time*100, 1)}%"},
                    "模型初始化": {"耗时": f"{round(model_init_time, 3)}s", "占比": f"{round(model_init_time/total_run_time*100, 1)}%"}
                }
            },
            "VQA处理": {
                "耗时": f"{round(func_total_time, 3)}s", 
                "占比": f"{round(func_total_time/total_run_time*100, 1)}%",
                "子任务": {
                    "tensor加载": {"耗时": f"{round(tensor_load_time, 3)}s", "占比": f"{round(tensor_load_time/total_run_time*100, 1)}%"},
                    "推理循环": {
                        "耗时": f"{round(inference_loop_time, 3)}s", 
                        "占比": f"{round(inference_loop_time/total_run_time*100, 1)}%",
                        "子任务": {
                            "模型推理": {"耗时": f"{round(inference_total_time, 3)}s", "占比": f"{round(inference_total_time/total_run_time*100, 1)}%"},
                            "数据预处理": {"耗时": f"{round(data_preprocess_total_time, 3)}s", "占比": f"{round(data_preprocess_total_time/total_run_time*100, 1)}%"},
                            "纯色检测": {"耗时": f"{round(pure_detection_total_time, 3)}s", "占比": f"{round(pure_detection_total_time/total_run_time*100, 1)}%"}
                        }
                    },
                    "后处理": {"耗时": f"{round(postprocess_time, 3)}s", "占比": f"{round(postprocess_time/total_run_time*100, 1)}%"}
                }
            }
        }
        logging.info('vqa time cost: ' + json.dumps(timing_json, ensure_ascii=False))

    @staticmethod
    def _format_response_vqa(params, result, elapsed_time):
        """格式化响应结果"""
        ori_score, percent_score, pure_tag = result
        
        # 确保数值类型可JSON序列化
        def make_json_serializable(obj):
            if isinstance(obj, (np.float32, np.float64)):
                return float(obj)
            elif isinstance(obj, (np.int32, np.int64)):
                return int(obj)
            elif isinstance(obj, np.bool_):
                return bool(obj)
            return obj
        
        return {
            'code': 0,
            'cid': params['cid'],
            'uri': params['uri'],
            'video_uri': params.get('video_uri', ''),
            'score': make_json_serializable(ori_score),
            'percent_score': make_json_serializable(percent_score),
            'pure_tag': make_json_serializable(pure_tag),  # 纯色检测标识
            'exec_time': make_json_serializable(elapsed_time),
            'version': '2.2',
            'status': 1
        }


def main():
    """命令行入口，支持直接运行VQA推理"""
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description='Archive QoE VQA 视频质量推理')
    parser.add_argument('--tensor_uri', type=str, help='Tensor文件URI')
    parser.add_argument('--local_tensor_path', type=str, help='本地Tensor文件路径')
    parser.add_argument('--cid', type=str, default='manual_test', help='内容ID')
    parser.add_argument('--uri', type=str, default='manual://test', help='视频URI')
    parser.add_argument('--video_uri', type=str, default='', help='视频源URI')
    parser.add_argument('--vqa_version', type=str, choices=['original', 'cpu_optimized'], 
                        default='original', help='VQA版本选择')
    parser.add_argument('--pure_detection', action='store_true', help='启用纯色检测')
    parser.add_argument('--output_json', type=str, help='输出JSON结果到文件')
    parser.add_argument('--verbose', action='store_true', help='详细日志输出')
    
    args = parser.parse_args()
    
    # 配置日志级别
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 检查参数
    if not args.tensor_uri and not args.local_tensor_path:
        print("错误: 必须提供 --tensor_uri 或 --local_tensor_path 中的一个")
        return 1
    
    # 设置环境变量
    if args.vqa_version:
        os.environ['VXCODE_VQA_VERSION'] = args.vqa_version
    
    if args.pure_detection:
        os.environ['VXCODE_VQA_PURE_DETECTION'] = '1'
    
    # 构建作业请求
    job_request = {
        'cid': args.cid,
        'uri': args.uri,
        'video_uri': args.video_uri
    }
    
    if args.tensor_uri:
        job_request['tensor_uri'] = args.tensor_uri
    if args.local_tensor_path:
        job_request['local_tensor_path'] = args.local_tensor_path
    
    try:
        print(f"开始VQA推理，版本: {args.vqa_version}")
        print(f"参数: {json.dumps(job_request, indent=2, ensure_ascii=False)}")
        
        # 执行推理
        result = VqaFlow.run(job_request)
        logging.info(f'推理结果: {json.dumps(result, indent=2, ensure_ascii=False)}')
        return 0
        
    except Exception as e:
        print(f"推理失败: {e}")
        if args.verbose:
            logging.exception("详细错误信息:")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())