# -*- coding: utf-8 -*-
import logging

from flows.operations.IOs.Download import Download
from utils import job


class DownloadFlow:
    @classmethod
    def run(cls):
        file_context = job.JOB_REQUEST["object"]
        url = file_context['url']
        md5 = file_context['md5']
        size = file_context['size']
        path = file_context['path']
        logging.info(f"download run, args: <{[url, md5, size]}>")
        path = Download.run(url, md5, size, path=path)
        logging.info(f'download path <{path}>')
        return
