"""
Archive QoE Sample 数据预处理流程模块

负责视频数据的预处理工作，包括视频下载、特征提取和tensor文件生成
支持可选的上传功能
"""
import logging
import os
import sys
import time

from flows.operations.IOs.Download import Download
from flows.operations.IOs.Upload import Upload
from utils import job
from utils import LOCAL_PATH
from utils import VQAExecutionError
from utils.error_type import *

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))
from model.bilivqa_v2.vqa_process import preprocess_data_with_timing, preprocess_data


class SampleFlow(object):
    """视频数据预处理流程类"""

    @classmethod
    def run(cls, job_request=None):
        """执行完整的数据预处理流程"""
        cls._validate_environment()

        if not job_request:
            job_request = job.JOB_REQUEST

        start_time = time.time()
        run_timing_stats = {}
        upload_tensor = job_request.get('upload_tensor', True)
        logging.info(f'Starting Archive QoE Sample preprocessing flow, job params: cid={job_request.get("cid", "N/A")}, video_uri={job_request.get("video_uri", "N/A")}, upload_tensor={upload_tensor}, start_time: {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))}')

        try:
            params = cls._parse_job_params(job_request)
            logging.info(f'Step 1/{4 if upload_tensor else 3}: Parse job parameters, cpu_count={params["optional_cpu"]}')

            logging.info(f'Step 2/{4 if upload_tensor else 3}: Download video file')
            download_start = time.time()
            local_path = cls._download_video(params)
            download_time = time.time() - download_start
            run_timing_stats['download_time'] = download_time
            logging.info(f'Video download completed, duration: {download_time:.2f}s, local_path: {local_path}')

            logging.info(f'Step 3/{4 if upload_tensor else 3}: Execute data preprocessing')
            preprocess_start = time.time()
            tensor_file, preprocess_timing_stats = cls._execute_preprocessing(local_path, params)
            preprocess_time = time.time() - preprocess_start
            run_timing_stats['preprocess_time'] = preprocess_time
            run_timing_stats.update(preprocess_timing_stats)

            tensor_filesize = os.path.getsize(str(tensor_file)) if tensor_file and os.path.exists(str(tensor_file)) else 0
            logging.info(f'Data preprocessing completed, duration: {preprocess_time:.2f}s, tensor_file: {tensor_file}')

            tensor_uri = None
            upload_time = 0
            if upload_tensor:
                logging.info('Step 4/4: Upload tensor file')
                upload_start = time.time()
                tensor_uri = cls._upload_tensor(tensor_file, params)
                upload_time = time.time() - upload_start
                run_timing_stats['upload_time'] = upload_time
                logging.info(f'Tensor file upload completed, duration: {upload_time:.2f}s, cloud_uri: {tensor_uri}')
            else:
                logging.info('Skipping tensor upload as requested')

            elapsed_time = time.time() - start_time
            run_timing_stats['total_time'] = elapsed_time

            # 输出详细的耗时统计
            cls._log_complete_timing_stats(run_timing_stats)

            if upload_tensor:
                logging.info(f'Sample preprocessing flow completed successfully! total_duration: {elapsed_time:.2f}s (download: {download_time:.2f}s, preprocessing: {preprocess_time:.2f}s, upload: {upload_time:.2f}s), final_output: {tensor_uri}')
            else:
                logging.info(f'Sample preprocessing flow completed successfully! total_duration: {elapsed_time:.2f}s (download: {download_time:.2f}s, preprocessing: {preprocess_time:.2f}s), local_output: {tensor_file}')
            return cls._format_response_sample(tensor_uri, tensor_filesize, str(tensor_file), upload_tensor, elapsed_time)

        except Exception as ex:
            elapsed_time = time.time() - start_time
            logging.error(f'Sample preprocessing flow failed! failure_time: {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}, total_duration: {elapsed_time:.2f}s, error_type: {type(ex).__name__}, error_details: {str(ex)}')
            raise

    @staticmethod
    def _validate_environment():
        """验证运行环境配置"""
        env = os.getenv("VXCODE_SWARM_ENV")
        logging.info(f'Validating environment variable: VXCODE_SWARM_ENV={env}')
        if env is None or env not in {'prod', 'uat'}:
            logging.error(f'Environment variable error: ENV [{env}] must be set to uat or prod')
            raise VQAExecutionError(f'ENV [{env}] is incorrect, should be set to uat or prod')

    @classmethod
    def _download_video(cls, params):
        """下载视频文件到本地存储"""
        video_uri = params['video_uri']
        video_filename, video_ext = os.path.splitext(os.path.basename(video_uri))
        local_path = LOCAL_PATH(f'origin{video_ext}')

        # 尝试获取远程文件大小
        video_size_mb = 0
        try:
            import requests
            response = requests.head(video_uri, timeout=10)
            if 'content-length' in response.headers:
                video_size_mb = int(response.headers['content-length']) / (1024 * 1024)
        except:
            pass

        size_info = f" ({video_size_mb:.2f}MB)" if video_size_mb > 0 else ""
        logging.info(f'Starting video file download, source_uri: {video_uri}, target_path: {local_path}{size_info}')

        cls._download(video_uri, local_path)

        if os.path.exists(local_path):
            actual_size_mb = os.path.getsize(local_path) / (1024 * 1024)
            logging.info(f'Video download successful, actual_size: {actual_size_mb:.2f}MB')
        else:
            logging.error(f'Video download failed, file not exists: {local_path}')

        return local_path

    @classmethod
    def _execute_preprocessing(cls, local_path, params):
        """执行视频数据预处理并返回详细耗时统计"""
        resource_config = cls._configure_resources(params['optional_cpu'])
        detection_config = cls._configure_detection()

        logging.info(f'Starting data preprocessing, input_video: {local_path}, spatial_resolution: {detection_config["spatial_resolution"]}, temporal_resolution: {detection_config["temporal_resolution"]}, pure_detection: {"enabled" if detection_config["pure_detection"] else "disabled"}, vframe_acceleration: {"enabled" if detection_config["vframe_acceleration"] else "disabled"}')

        tensor_file, timing_stats = cls._preprocess_data_with_timing(local_path, resource_config, detection_config)

        if tensor_file and os.path.exists(str(tensor_file)):
            tensor_size_mb = os.path.getsize(str(tensor_file)) / (1024 * 1024)
            timing_stats['tensor_file_size_mb'] = tensor_size_mb
            logging.info(f'Data preprocessing completed, output_tensor: {tensor_file}, tensor_size: {tensor_size_mb:.2f}MB')
        else:
            logging.error(f'Data preprocessing failed, invalid tensor file')

        return tensor_file, timing_stats

    @classmethod
    def _preprocess_data_with_timing(cls, local_path, resource_config, detection_config):
        """带详细耗时统计的数据预处理"""
        import time
        timing_stats = {}

        # 1. 视频信息获取和验证
        info_start = time.time()
        video_info, fps, num_frames, key_areas_num = cls._analyze_video_info(local_path)
        timing_stats['video_info_time'] = time.time() - info_start
        timing_stats['fps'] = fps
        timing_stats['num_frames'] = num_frames
        timing_stats['key_areas_num'] = key_areas_num

        # 2. 数据预处理
        preprocess_start = time.time()
        tensor_file, preprocess_timing_stats = preprocess_data_with_timing(
            local_path,
            resource_config['hard_decode'],
            detection_config['spatial_resolution'],
            detection_config['temporal_resolution'],
            resource_config['process_count'],
            resource_config['thread_count'],
            detection_config['vframe_acceleration']
        )
        timing_stats['actual_preprocess_time'] = time.time() - preprocess_start

        # 合并详细的预处理耗时统计
        timing_stats.update(preprocess_timing_stats)

        return tensor_file, timing_stats

    @staticmethod
    def _analyze_video_info(local_path):
        """分析视频信息"""
        from model.bilivqa_v2.utils import get_video_info
        import cv2
        import numpy as np
        import pandas as pd
        import os

        video_info = get_video_info(local_path)

        # 获取帧率
        try:
            numerator, denominator = video_info["avg_frame_rate"].split('/')
            fps = float(numerator) / float(denominator)
        except:
            fps = 25.0

        # 获取总帧数
        try:
            if 'nb_frames' in video_info:
                num_frames = int(video_info['nb_frames'])
            else:
                cap = cv2.VideoCapture(local_path)
                num_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if num_frames == 0:
                num_frames = int(float(video_info['format']['duration']) * fps)
        except:
            num_frames = int(float(video_info['format']['duration']) * fps)

        # 分析关键帧
        try:
            exp_name = "keyframe"
            ffprobe_cmd = f"ffprobe -loglevel error -select_streams v:0 -show_entries packet=pts_time,flags \
                -of csv=print_section=0 {local_path}" +  " | awk -F',' '/K/ {print $1}'>" + f"{os.path.splitext(local_path)[0]}_{exp_name}.csv"
            os.system(ffprobe_cmd)
            key_timestamps = np.array(pd.read_csv(f"{os.path.splitext(local_path)[0]}_{exp_name}.csv", header=None)).flatten()
            os.remove(f"{os.path.splitext(local_path)[0]}_{exp_name}.csv")

            # 计算关键区域数量（简化计算）
            video_read_len_max = 60
            vid_time_s = int(float(video_info['format']['duration']))
            video_read_len = min(vid_time_s, video_read_len_max)
            video_read_interval = int((vid_time_s / video_read_len))

            time_list = []
            for i in range(video_read_len):
                start_time = i * video_read_interval
                time_list.append(start_time)
            time_np = np.array(time_list)

            seek_idxes = np.searchsorted(key_timestamps, time_np+1e-4)
            insert_idxes = np.unique(seek_idxes)
            key_areas_num = len(insert_idxes)
        except:
            key_areas_num = 60

        return video_info, fps, num_frames, key_areas_num

    @staticmethod
    def _log_complete_timing_stats(run_timing_stats):
        """输出完整的Sample流程耗时统计"""
        import json

        total_time = run_timing_stats.get('total_time', 0)
        download_time = run_timing_stats.get('download_time', 0)
        preprocess_time = run_timing_stats.get('preprocess_time', 0)
        upload_time = run_timing_stats.get('upload_time', 0)
        video_info_time = run_timing_stats.get('video_info_time', 0)
        actual_preprocess_time = run_timing_stats.get('actual_preprocess_time', 0)

        # 获取详细的预处理耗时
        duration_validation_time = run_timing_stats.get('duration_validation_time', 0)
        resolution_validation_time = run_timing_stats.get('resolution_validation_time', 0)
        codec_fps_time = run_timing_stats.get('codec_fps_time', 0)
        keyframe_analysis_time = run_timing_stats.get('keyframe_analysis_time', 0)
        frame_indices_time = run_timing_stats.get('frame_indices_time', 0)
        dataloader_creation_time = run_timing_stats.get('dataloader_creation_time', 0)
        tensor_process_time = run_timing_stats.get('tensor_process_time', 0)
        tensor_save_time = run_timing_stats.get('tensor_save_time', 0)

        # 构建JSON格式的耗时统计
        timing_json = {
            "Sample总耗时": f"{round(total_time, 3)}s",
            "视频信息": {
                "fps": run_timing_stats.get('fps', 0),
                "Clip数": run_timing_stats.get('total_clips', 0),
                "总帧数": run_timing_stats.get('total_frames', 0),
                "关键区域数": run_timing_stats.get('key_areas_num', 0)
            },
            "视频下载": {"耗时": f"{round(download_time, 3)}s", "占比": f"{round(download_time/total_time*100, 1)}%"},
            "数据预处理": {
                "耗时": f"{round(preprocess_time, 3)}s",
                "占比": f"{round(preprocess_time/total_time*100, 1)}%",
                "子任务": {
                    "视频信息分析": {"耗时": f"{round(video_info_time, 3)}s", "占比": f"{round(video_info_time/total_time*100, 1)}%"},
                    "预处理详细": {
                        "耗时": f"{round(actual_preprocess_time, 3)}s",
                        "占比": f"{round(actual_preprocess_time/total_time*100, 1)}%",
                        "子步骤": {
                            "时长验证": {"耗时": f"{round(duration_validation_time, 3)}s", "占比": f"{round(duration_validation_time/total_time*100, 1)}%"},
                            "分辨率验证": {"耗时": f"{round(resolution_validation_time, 3)}s", "占比": f"{round(resolution_validation_time/total_time*100, 1)}%"},
                            "编码帧率": {"耗时": f"{round(codec_fps_time, 3)}s", "占比": f"{round(codec_fps_time/total_time*100, 1)}%"},
                            "关键帧分析": {"耗时": f"{round(keyframe_analysis_time, 3)}s", "占比": f"{round(keyframe_analysis_time/total_time*100, 1)}%"},
                            "帧索引计算": {"耗时": f"{round(frame_indices_time, 3)}s", "占比": f"{round(frame_indices_time/total_time*100, 1)}%"},
                            "数据加载器": {"耗时": f"{round(dataloader_creation_time, 3)}s", "占比": f"{round(dataloader_creation_time/total_time*100, 1)}%"},
                            "tensor处理": {"耗时": f"{round(tensor_process_time, 3)}s", "占比": f"{round(tensor_process_time/total_time*100, 1)}%"},
                            "文件保存": {"耗时": f"{round(tensor_save_time, 3)}s", "占比": f"{round(tensor_save_time/total_time*100, 1)}%"}
                        }
                    }
                }
            }
        }

        if upload_time > 0:
            timing_json["文件上传"] = {"耗时": f"{round(upload_time, 3)}s", "占比": f"{round(upload_time/total_time*100, 1)}%"}

        if 'tensor_file_size_mb' in run_timing_stats:
            timing_json["tensor文件大小"] = f"{run_timing_stats['tensor_file_size_mb']:.2f}MB"

        logging.info('sample time cost: ' + json.dumps(timing_json, ensure_ascii=False))

    @classmethod
    def _upload_tensor(cls, tensor_file, params):
        """上传tensor文件到云端存储"""
        video_uri = params['video_uri']
        video_filename, _ = os.path.splitext(os.path.basename(video_uri))
        tensor_ext = os.path.splitext(str(tensor_file))[1]
        tensor_uri = f'upos://vqa/{video_filename}_tensor{tensor_ext}'

        tensor_size_mb = os.path.getsize(str(tensor_file)) / (1024 * 1024) if os.path.exists(str(tensor_file)) else 0

        logging.info(f'Starting tensor file upload to cloud, local_file: {tensor_file} ({tensor_size_mb:.2f}MB), target_uri: {tensor_uri}')

        cls._upload(str(tensor_file), tensor_uri)

        logging.info(f'Tensor file upload successful: {tensor_uri}')

        return tensor_uri

    @staticmethod
    def _parse_job_params(job_request):
        """解析作业请求参数"""
        return {
            'cid': job_request.get('cid', ''),
            'uri': job_request['uri'],
            'video_uri': job_request['video_uri'],
            'optional_cpu': job.JOB_OPTIONAL_CPU
        }

    @staticmethod
    def _download(video_uri, local_path):
        """底层文件下载函数"""
        try:
            Download.dry_run(video_uri, local_path)
        except Exception as ex:
            logging.error(f'Download failed: {str(ex)}')
            err_obj = VQAExecutionError(str(ex))
            err_obj.should_retry = True
            raise err_obj

    @staticmethod
    def _upload(local_path, upload_uri):
        """底层文件上传函数"""
        try:
            Upload.dry_run(local_path, upload_uri)
        except Exception as ex:
            logging.error(f'Upload failed: {str(ex)}')
            err_obj = VQAExecutionError(str(ex))
            err_obj.should_retry = True
            raise err_obj

    @staticmethod
    def _configure_resources(optional_cpu):
        """配置计算资源"""
        used_cpu_count = int(os.getenv("VXCODE_CPU_NUM", optional_cpu))
        used_threads_count = int(os.getenv("VXCODE_THREAD_NUM", 2))
        used_processes_count = max(1, used_cpu_count * 2 // used_threads_count)
        used_hard_decoding = int(os.getenv("VXCODE_HARD_DECODE", 0))

        # 控制DataLoader是否使用多worker（避免共享内存问题）
        enable_multi_worker = int(os.getenv("VXCODE_ENABLE_MULTI_WORKER", 1))
        if enable_multi_worker == 0:
            used_processes_count = 0
            logging.info('Multi-worker disabled via VXCODE_ENABLE_MULTI_WORKER=0')

        logging.info(f'Resource configuration strategy: cpu_count={used_cpu_count}, process_count={used_processes_count}, thread_count={used_threads_count}, decode_mode={"hardware" if used_hard_decoding == 1 else "software"}, multi_worker={"enabled" if used_processes_count > 0 else "disabled"}')

        return {
            'cpu_count': used_cpu_count,
            'thread_count': used_threads_count,
            'process_count': used_processes_count,
            'hard_decode': used_hard_decoding == 1
        }

    @staticmethod
    def _configure_detection():
        """配置检测参数"""
        return {
            'pure_detection': int(os.getenv("VXCODE_VQA_PURE_DETECTION", 0)) == 1,
            'spatial_resolution': int(os.getenv("VXCODE_VQA_SPATIAL_RESOLUTION", 510)),
            'temporal_resolution': int(os.getenv("VXCODE_VQA_TEMPORAL_RESOLUTION", 160)),
            'vframe_acceleration': os.getenv('ENABLE_VFRAME_ACCELERATION', '0') == '1'
        }

    @staticmethod
    def _preprocess_data(local_path, resource_config, detection_config):
        """执行底层数据预处理操作"""
        try:
            if os.path.isfile(local_path):
                logging.info(f'Validating input file: {local_path} (exists)')
                tensor_file = preprocess_data(
                    local_path,
                    resource_config['hard_decode'],
                    detection_config['spatial_resolution'],
                    detection_config['temporal_resolution'],
                    num_processes=resource_config['process_count'],
                    num_threads=resource_config['thread_count'],
                    vframe_acceleration=detection_config['vframe_acceleration']
                )
                return tensor_file
            else:
                logging.error(f'Input file not exists: {local_path}')
                raise BiliVqaFullVideoError("Cann't find the video!")
        except Exception as ex:
            logging.error(f'Preprocessing exception: {type(ex).__name__} - {str(ex)}')
            if isinstance(ex, (BiliVqaTensorRTError, BiliVqaVideoError)):
                raise
            else:
                raise VQAExecutionError(str(ex))

    @staticmethod
    def _format_response_sample(tensor_uri, tensor_filesize, local_tensor_path, uploaded, elapsed_time):
        """格式化响应结果"""
        response = {
            'code': 0,
            'tensor_size': tensor_filesize,
            'local_tensor_path': local_tensor_path,
            'exec_time': elapsed_time,
        }
        if uploaded and tensor_uri:
            response['tensor_uri'] = tensor_uri
        return response