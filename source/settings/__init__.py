# -*- coding: utf-8 -*-
import os
import sys
import shlex
import socket
import importlib
import subprocess
from cpuinfo import get_cpu_info
from settings.base.dev import *


def __can_run(cmd):
    try:
        args = shlex.split(cmd)
        ret = subprocess.run(
            args,
            stderr=subprocess.DEVNULL,
            stdout=subprocess.DEVNULL,
            bufsize=4096
        )
        return not (bool(ret.returncode))
    except:
        return False


def __run_result(cmd):
    args = shlex.split(cmd)
    result = subprocess.run(
        args,
        universal_newlines=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        bufsize=4096
    )
    return result.stdout.strip()


EXPECTED_ENTRANCE_EXCEPTION_CODE = 6
UNEXPECTED_ENTRANCE_EXCEPTION_CODE = 1

SETTINGS_ROOT = os.path.dirname(os.path.realpath(__file__))
SOURCE_ROOT = os.path.dirname(SETTINGS_ROOT)
PROJECT_ROOT = os.path.dirname(SOURCE_ROOT)
ASSETS_ROOT = os.path.join(PROJECT_ROOT, "assets")
KEYS_ROOT = os.path.join(ASSETS_ROOT, "keys")

CONF_ROOT = os.path.join(PROJECT_ROOT, "conf")
UPOS_CONF_ROOT = os.path.join(CONF_ROOT, "upos")
LFOS_CONF_ROOT = os.path.join(CONF_ROOT, "lfos")

EXEC_ROOT = os.path.join(ASSETS_ROOT, "bin")
ENVS_ROOT = os.path.join(SETTINGS_ROOT, "base")

README_PATH = os.path.join(PROJECT_ROOT, "README.md")
LOCAL_WORKSPACE_ROOT = "/data/bili_vxcode_workspace"
HDFS_WORKSPACE_ROOT = "/vxcode/vxcode-swarms"
IPC_HANDLER_PATH = "/tmp/ipc.fifo"
IPC_EXIT_CLIENT_TIMEOUT = 30
IPC_WORKER_COUNT = 16
PROJECT_NAME = os.path.basename(PROJECT_ROOT)

CLUSTER_NAME = os.getenv("CASTER_CLUSTER_NAME", socket.gethostname())
POD_NAME = os.getenv("POD_NAME", str(os.getpid()))
SWARM_INSTANCE_NAME = f"{CLUSTER_NAME}_{POD_NAME}"

with open(README_PATH, "r") as rfile:
    whoami = rfile.readline().strip()
    whoami = whoami.replace("#", "").strip()
    VERSION = whoami[whoami.rfind('v'):]

ENV = os.getenv("VXCODE_SWARM_ENV", "dev")

# DRY means that we only run media processing without uploading results,
# send any events or reports
VXCODE_DRY_RUN = bool(int(os.getenv("VXCODE_DRY_RUN", 0)))
VXCODE_HIVE_TOKEN = "47960ddf85cc64b807acb45d46f6841e"
IS_LOCAL_TEST = "dev" in ENV
os.environ["HDFSCLI_CONFIG"] = os.path.join(CONF_ROOT, "hdfs", "hdfscli.cfg")

SWARM_JOB_ID = os.getenv("JOB_ID")
SWARM_QUEEN = os.getenv("SWARM_QUEEN", "dev")
SWARM_NAME = ENV
SWARM_SIGNATURE = whoami
SWARM_POSITION = os.getenv("SWARM_POSITION", ENV)
SWARM_IN_TEST = "dev" in ENV
SWARM_IN_PRODUCT = ("prod" in ENV) or ("pre" in ENV)
SWARM_IN_DOCKER = os.path.exists("/.dockerenv")
SWARM_IS_PRIVILEGED = int(os.getenv("SWARM_IS_PRIVILEGED", 1))

cpu_info = get_cpu_info()
SWARM_CPU_MODEL = cpu_info.get("brand", "Unknown")
SWARM_CPU_SIMD = next(i.upper() for i in ("avx2", "avx") if i in cpu_info["flags"])

SWARM_GPU_EQUIPPED = __can_run("nvidia-smi")
if SWARM_GPU_EQUIPPED:
    SWARM_GPU_NAME = __run_result("nvidia-smi --query-gpu=gpu_name --format=csv,noheader -i 0")
    SWARM_GPU_DRIVER_VERSION = __run_result("nvidia-smi --query-gpu=driver_version --format=csv,noheader -i 0")

SWARM_GPU_CUDA_EQUIPPED = (SWARM_GPU_EQUIPPED and os.path.exists("/usr/local/cuda/version.txt"))
if SWARM_GPU_CUDA_EQUIPPED:
    with open("/usr/local/cuda/version.txt", "r") as rfile:
        line = rfile.read()
        SWARM_GPU_CUDA_VERSION = (line.split(" ")[-1]).strip()

RSYNC_USER = "cloudCode"
RSYNC_PSD_PATH = os.path.join(KEYS_ROOT, "vstorage.psd")

PERF_STAT_GENERAL_CMD = "perf stat -e cpu-clock,instructions,cycles"
PERF_TRACE_TIMEOUT = 1
SWARM_PERF_ENABLED = __can_run(f"{PERF_STAT_GENERAL_CMD} echo PERF")

UPOS_PART_SIZE = 40
UPOS_THREAD_NUM = 8
UPOS_UPLOAD_TIMEOUT = 1200

UPCLONE_CMD = os.path.join(EXEC_ROOT, "upclone")

# LANCER
# http://berserker.bilibili.co/#/lancer/list/job/165053224760601
COMPARE_LANCER_LOG_ID = "015524"
COMPARE_LANCER_FIELD_NAMES = (
    "video_id",
    "vxcode_id",
    "task_id",
    "compare_info",
    "context_info",
)
# http://berserker.bilibili.co/#/lancer/list/job/164259098607601
QUALITY_LANCER_LOG_ID = "014078"
QUALITY_LANCER_FIELD_NAMES = (
    "vxcode_id",
    "task_id",
    "buid",
    "image",
    "version",
    "vp",
    "upload_info",
    "quality_info",
    "utime",
)
# http://berserker.bilibili.co/#/lancer/list/job/164250593150201
RESOURCE_LANCER_LOG_ID = "014060"
RESOURCE_LANCER_FIELD_NAMES = (
    "vxcode_id",
    "task_id",
    "buid",
    "image",
    "version",
    "queen",
    "request_info",
    "resource_info",
    "route_info",
    "cost_info",
    "platform_info",
    "status",
    "utime",
)

##### Settings Overidding ##############
ENVS = [os.path.splitext(filename)[0]
        for filename in os.listdir(ENVS_ROOT)
        if filename != "__init__.py"]

if ENV in ENVS:
    UPCLONE_UPOS_CONF_PATH = os.path.join(UPOS_CONF_ROOT, SWARM_POSITION, "upclone.conf")
    UPCLONE_LFOS_CONF_PATH = os.path.join(LFOS_CONF_ROOT, SWARM_POSITION, "upclone.conf")
    g = globals()
    if SWARM_IN_PRODUCT:
        base_env = importlib.import_module(f'settings.base.{ENV}s.{SWARM_POSITION}')
    else:
        base_env = importlib.import_module(f'settings.base.{ENV}')
    current_settings = {
        sym_name: sym_val
        for sym_name, sym_val in vars(base_env).items()
        if not sym_name.startswith('__')
    }

    detailed_env = importlib.import_module(f'settings.envs.{SWARM_POSITION}')
    current_settings.update({
        sym_name: sym_val
        for sym_name, sym_val in vars(detailed_env).items()
        if not sym_name.startswith('__')
    })
    g.update(current_settings)
