# -*- coding: utf-8 -*-
import os
import time

OPS_LOG_TASK_ID = '000069'
# FIXME: 内网向日志系统输出日志的应用ID，请前往http://ops-log.bilibili.co生成
OPS_LOG_APP_ID = 'video.bili-vod.bili-vod-archive-qoe-gpu'

UPOS_DOWNLOAD_END_POINTS = UPOS_UPLOAD_END_POINTS = ['http://upos-sz-office.bilibili.co']
UPOS_EXTRA_QUERY = ""
DOWNLOAD_RETRIES = 1
UPLOAD_RETRIES = 1
CALLBACK_RETRIES = 1
MIN_DOWNLOAD_BYTES_PER_SEC = 1000000
MIN_UPLOAD_BYTES_PER_SEC = 1000000

LUFFY_UPLOAD_END_POINT = "http://host.docker.internal:15000/obs/"
LUFFY_UPLOAD_PART_SIZE = 8 * 1024 * 1024
LUFFY_OBS_SCHEMA = "luffy"
BOSS_OBS_SCHEMA = "boss"
UPOS_OBS_SCHEMA = "upos"
HDFS_SCHEMA = "hdfs"
FILE_SCHEMA = "file"

VXCODE_HIVE_TRACE_HOST = VXCODE_HIVE_HOST = "http://host.docker.internal:18000"
VXCODE_HIVE_TIMEOUT = 3
QBONE_DOMAIN = None

EDGE_LOG_TEMPLATE = '{instance_id}|{level}|{lineno}|{job_id}|{queen}|{log}|{module}'
# FIXME: 外网向日志系统输出日志的应用ID，请前往http://berserker.bilibili.co生成
EDGE_LOG_ID = None
EDGE_LOG_DOMAIN = "dataflow.biliapi.com"
EDGE_LOG_URI = "log/system"
EDGE_LOG_API_TIMEOUT = 2
EDGE_LOG_API_RETRIES = 3
EDGE_LOG_MAX_CACHE_LINES = 100
EDGE_LOG_BATCH_SIZE = 20

SWARM_IS_EDGE = False

SKIP_EVENT_PROGRAMS = {}

SWARM_FUSE_DAEMON_WORKING = False
SWARM_FUSE_DAEMON_VERSION = None
SWARM_FUSE_UPOS_ROOT = None
SWARM_FUSE_HDFS_ROOT = None
SWARM_FUSE_BOSS_ROOT = None
SWARM_FUSE_LUFFY_ROOT = None

VXCODE_HIVE_APP_NAME = 'vxcode-hive-api'
VXCODE_HIVE_TRACE_APP_NAME = 'vxcode-hive-trace'

VXCODE_HIVE_PRE_APP_NAME = 'pre-vxcode-hive-api'
VXCODE_HIVE_TRACE_PRE_APP_NAME = 'pre-vxcode-hive-trace'

VXCODE_HIVE_V2_ENABLE = os.getenv('VXCODE_HIVE_V2_ENABLE') == '1'
if VXCODE_HIVE_V2_ENABLE:
    VXCODE_HIVE_APP_NAME = 'vxcode-hive-api-v2'
    VXCODE_HIVE_TRACE_APP_NAME = 'vxcode-hive-trace-v2'
    VXCODE_HIVE_PRE_APP_NAME = 'pre-vxcode-hive-api-v2'
    VXCODE_HIVE_TRACE_PRE_APP_NAME = 'pre-vxcode-hive-trace-v2'

# fuse sidecar support
__enable_fuse_sidecar = (os.getenv('VXCODE_SWARM_FUSE_SIDE_CAR') == '1')
__fuse_path_prefix = ''
__fuse_daemon_health_file_path = ''


def update_fuse_healthy_check_path():
    global __fuse_path_prefix
    global __fuse_daemon_health_file_path
    enable = __enable_fuse_sidecar
    if enable:
        pid = os.popen('pidof -s s3-common-bin').read().strip()
        __fuse_path_prefix = f'/proc/{pid}/root'
    else:
        __fuse_path_prefix = ''
    __fuse_daemon_health_file_path = __fuse_path_prefix + os.getenv('VXCODE_SWARM_FUSE_VERSION_FILE',
                                                                    '/data/s3fs/mount/.bvcs3fs_version')


def block_on_fuse_healthy_check():
    backoff = 1
    while True:
        update_fuse_healthy_check_path()
        path = __fuse_daemon_health_file_path
        if os.path.exists(path):
            return True
        if backoff > 3:
            return False
        time.sleep(backoff)
        backoff += 1


healthy_check_file_exist = block_on_fuse_healthy_check()

if healthy_check_file_exist:
    with open(__fuse_daemon_health_file_path, "r") as rfile:
        __requested_version = os.getenv("VXCODE_SWARM_FUSE_VERSION")
        __daemon_info = rfile.readline()
        __daemon_version, __daemon_heartbeat = __daemon_info.split("@", 1)
        if time.time() - float(__daemon_heartbeat) < 35 and __requested_version == __daemon_version:
            SWARM_FUSE_DAEMON_WORKING = True
            SWARM_FUSE_DAEMON_VERSION = __daemon_version
            SWARM_FUSE_UPOS_ROOT = __fuse_path_prefix + os.getenv("VXCODE_SWARM_FUSE_UPOS")
            SWARM_FUSE_HDFS_ROOT = __fuse_path_prefix + os.getenv("VXCODE_SWARM_FUSE_HDFS")
            SWARM_FUSE_BOSS_ROOT = __fuse_path_prefix + os.getenv("VXCODE_SWARM_FUSE_BOSS")
            SWARM_FUSE_LUFFY_ROOT = __fuse_path_prefix + os.getenv("VXCODE_SWARM_FUSE_LUFFY")
