# -*- coding: utf-8 -*-
import os
import logging
from flows.operations.IOs.Download import Download
from flows.operations.IOs.Upload import Upload
from tests import VXCodeSwarmOperationTestCase
from tests.resources import cpu_evaluation_object, upload_upos_prefix


class TestUPOS(VXCodeSwarmOperationTestCase):
    UPLOAD_TARGET = upload_upos_prefix + "larva_test.mp4"
    LOCAL_FILENAME = "larva_test.mp4"

    def test_down_and_up(self):
        logging.info("start downloading tests")
        Download.run(path=self.LOCAL_FILENAME, **cpu_evaluation_object)
        logging.info("Download test passed")
        logging.info("start uploading tests")
        Upload.run(self.LOCAL_FILENAME, self.UPLOAD_TARGET)
        logging.info("Uploading test passed")

    def tearDown(self) -> None:
        os.unlink(self.LOCAL_FILENAME)
