# -*- coding: utf-8 -*-
import datetime
import time
import logging
from utils import IPCContext
from utils import IPCClient
import settings
from utils import init_lancer_log
from tests import VXCodeSwarmOperationTestCase


class LancerTestCase(VXCodeSwarmOperationTestCase):
    def test_normal_run(self):
        DUMMY_QUALITY = {
            'vxcode_id': 'pvx20220121152813004807-0e5bd6d84',
            'task_id': 'pts20220121000000000000418176570',
            'buid': 'UNITTEST_TRANSCODER',
            'image': 'video.bili-vxcode.vxcode-swarm-transcoders',
            'version': 'u0.6.20.25',
            'vp': 16,
            'upload_info': {},
            'quality_info': {
                'input_width': 1280,
                'input_height': 720,
                'input_r_frame_rate': '120/1',
                'input_bit_rate': '42625',
                'input_codec_name': 'h264',
                'input_color_range': 'tv',
                'input_color_space': 'bt709',
                'input_color_primaries': 'bt709',
                'input_color_transfer': 'bt709',
                'input_pix_fmt': 'yuv420p',
                'output_width': 640,
                'output_height': 360,
                'output_r_frame_rate': '120/1',
                'output_codec_name': 'h264',
                'output_color_range': 'tv',
                'output_color_space': 'bt709',
                'output_color_primaries': 'bt709',
                'output_color_transfer': 'bt709',
                'output_pix_fmt': 'yuv420p',
                'video_id': '900ca39f93edf7b2206f59e2e3c4c6bf',
                'formats': ['UTPrf202201211526372833460000000']
            },
            'utime': 1642751926194
        }
        DUMMY_RESOURCE = {'vxcode_id': 'pvx20220121152813143509-fed6caad7',
                          'task_id': 'pts20220121000000000000418176570', 'buid': 'UNITTEST_TRANSCODER',
                          'image': 'video.bili-vxcode.vxcode-swarm-transcoders', 'version': 'u0.6.20.25',
                          'queen': 'idc_ylf_shared_cpu', 'request_info': {'flow': 'xcode', 'object': {'size': 2092730,
                                                                                                      'md5': '251d77479d6a0d1ec76c7e3ff1c1a4a5',
                                                                                                      'url': 'hdfs:///vxcode/bili_vxcode_workspace/2022/01/21/pts20220121000000000000418176570/04d298/S027.mp4',
                                                                                                      'slice_compensate': {
                                                                                                          '30': -1,
                                                                                                          '60': -1,
                                                                                                          '120': 0}},
                                                                          'vp': 27, 'profiles': [
                    {'profile': 'UTPrf202201211526372827880000000',
                     'save_to': 'upos://tmp/vxcode-unittests/uts20220121000000000000418176549',
                     'container': {'duration': 0, 'format': 'mp4', 'segment_time': 0, 'start_time': 0}, 'arch': 'cpu',
                     'audio': {'encode': {'bit_rate': 320000, 'channels': 6, 'codec': 'aac', 'sample_rate': 44100},
                               'filter': {'loudnorm': {'target_I': -12}}}, 'video': {
                        'encode': {'frame_rate': 30, 'codec': 'x264', 'dynamic_range': 'sdr', 'width': 640,
                                   'height': 360, 'bit_rate': 409600, 'maxrate': 12000000, 'bufsize': 12000000,
                                   'bili_preset': 'balance', 'gop_max': 150, 'gop_min': 150},
                        'filter': {'denoise': True}}, 'segment_idx': 27},
                    {'profile': 'UTPrf202201211526372830710000000',
                     'save_to': 'upos://tmp/vxcode-unittests/uts20220121000000000000418176549',
                     'container': {'duration': 0, 'format': 'mp4', 'segment_time': 0, 'start_time': 0}, 'arch': 'cpu',
                     'audio': {'encode': {'bit_rate': 128000, 'channels': 2, 'codec': 'aac', 'sample_rate': 44100},
                               'filter': {'loudnorm': {'target_I': -12}}}, 'video': {
                        'encode': {'frame_rate': 60, 'codec': 'x264', 'dynamic_range': 'sdr', 'width': 640,
                                   'height': 360, 'bit_rate': 409600, 'maxrate': 12000000, 'bufsize': 12000000,
                                   'bili_preset': 'balance', 'gop_max': 300, 'gop_min': 300},
                        'filter': {'mosaic': {'h': 63, 'w': 320, 'y': 36, 'x': -36}, 'denoise': True}},
                     'segment_idx': 27}, {'profile': 'UTPrf202201211526372833460000000',
                                          'save_to': 'upos://tmp/vxcode-unittests/uts20220121000000000000418176549',
                                          'container': {'duration': 0, 'format': 'mp4', 'segment_time': 0,
                                                        'start_time': 0}, 'arch': 'cpu', 'audio': {
                            'encode': {'bit_rate': 192000, 'channels': 2, 'codec': 'aac', 'sample_rate': 44100},
                            'filter': {'loudnorm': {'target_I': -12}}}, 'video': {
                            'encode': {'frame_rate': 120, 'codec': 'x264', 'dynamic_range': 'sdr', 'width': 640,
                                       'height': 360, 'bit_rate': 409600, 'maxrate': 12000000, 'bufsize': 12000000,
                                       'bili_preset': 'balance', 'gop_max': 600, 'gop_min': 600},
                            'filter': {'deinterlace': True, 'mosaic': {'h': 63, 'w': 320, 'y': 36, 'x': -36}}},
                                          'segment_idx': 27}], 'transcode_type': 'video', 'has_hdfs': True,
                                                                          'hdfs_workspace': 'hdfs:///vxcode/bili_vxcode_workspace/2022/01/21/pts20220121000000000000418176570',
                                                                          'upos_workspace': 'upos://stmp/pts20220121000000000000418176570',
                                                                          'buid': 'UNITTEST_TRANSCODER',
                                                                          'task_id': 'pts20220121000000000000418176570',
                                                                          'upload_info': {}},
                          'resource_info': {'required_cpu': 6000, 'optional_cpu': 18000, 'required_mem': 2457,
                                            'optional_mem': 19656, 'required_storage': 1000, 'optional_storage': 2000,
                                            'required_gpu': 0},
                          'route_info': {'prefer_gpu': False, 'allow_edge': False, 'is_privileged': False,
                                         'priority': 3, 'vendor_stored': False, 'role': '', 'high_io': False,
                                         'users': ['all', 'pre']},
                          'cost_info': {'supervised': True, 'cmd_count': 16, 'exec_time': 2004.6556692123413,
                                        'begin_time': 1642750096.9797266, 'end_time': 1642752101.6353958,
                                        'max_begin': 1642750098.9473462, 'max_end': 1642750939.826866,
                                        'max_memory': 563081216, 'max_cpu': 2.48093378815847,
                                        'cpu_time': 4732.416680539999},
                          'platform_info': {'cpu': 'Intel(R) Xeon(R) Gold 5218 CPU @ 2.30GHz', 'simd': 'AVX2',
                                            'hostname': 'pvx20220121152813143509-fed6caad7'}, 'utime': 1642752101651,
                          'status': 'DONE'}

        init_lancer_log("quality_lancer_logger", settings.QUALITY_LANCER_LOG_ID,
                        settings.QUALITY_LANCER_FIELD_NAMES)
        init_lancer_log("resource_lancer_logger", settings.RESOURCE_LANCER_LOG_ID,
                        settings.RESOURCE_LANCER_FIELD_NAMES)
        with IPCContext():
            logging.info(f"Try to send dummy quality and resource")
            time.sleep(3)
            IPCClient.report_quality(**DUMMY_QUALITY)
            IPCClient.report_resource(**DUMMY_RESOURCE)
            logging.info(f"Dummy quality and resource sent")


if __name__ == "__main__":
    a = LancerTestCase()
    a.test_normal_run()
