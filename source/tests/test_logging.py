# -*- coding: utf-8 -*-
import time
import logging
from unittest import TestCase
from utils import IPCContext
# from utils import init_ops_log
from utils.pyutils.src.log import init_ck_log


class TestLogging(TestCase):
    
    def test_normal_run(self):
        with IPCContext():
            init_ck_log("unittest")
            logging.info("This is a test")
            logging.info("This is a test")
            logging.info("This is a test")

        with IPCContext():
            time.sleep(3)
            init_ck_log("unittest")
            logging.info("This is a test")
            logging.info("This is a test")
            logging.info("This is a test")
