# -*- coding: utf-8 -*-
import time
import logging
from utils import get_hostname
from utils import VX<PERSON>odeH<PERSON>
from utils import IPCContext
from utils import IPCClient
from tests import VXCodeSwarmOperationTestCase


class IPCTestCase(VXCodeSwarmOperationTestCase):
    DUMMY_VXCODE_ID = "unittests_vxcode_id"
    DUMMY_BUILD_NAME = f"{get_hostname()}-{int(time.time())}"
    DUMMY_TRACE = {
        "task_id": "unittests_task_id",
        "vxcode_id": DUMMY_VXCODE_ID,
        "build": DUMMY_BUILD_NAME
    }

    def test_normal_run(self):
        with IPCContext():
            logging.info(f"Try to send dummy trace")
            time.sleep(3)
            IPCClient.report_quality_trace(**self.DUMMY_TRACE)
            logging.info(f"Dummy trace sent")
        quality_traces = VXCodeHive.retrieve_quality_trace(self.DUMMY_VXCODE_ID)
        print(len(quality_traces))
        self.assertTrue(any([True for t in quality_traces if t.get("build", None) == self.DUMMY_BUILD_NAME]))
