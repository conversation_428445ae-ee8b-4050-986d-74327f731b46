# -*- coding: utf-8 -*-
import signal
import settings
from tests import VXCodeSwarmOperationTestCase
from utils import CostSupervisor
from utils import supervised_run, LOCAL_PATH


class TestCostSupervisor(VXCodeSwarmOperationTestCase):
    def setUp(self) -> None:
        CostSupervisor.reset()

    def test_correct(self):
        cmd = "sleep 1"
        result = supervised_run(cmd)
        self.assertEqual(result["retcode"], 0)

    def test_exec_not_found(self):
        cmd = "x 1"
        with self.assertRaises(FileNotFoundError):
            CostSupervisor.run(cmd)

    def test_timeout(self):
        cmd = "sleep 10"
        result = CostSupervisor.run(cmd, timeout=1)
        self.assertEqual(result["retcode"], CostSupervisor.RET_TIMEOUT)

    def test_summary_sequential(self):
        cmd_count = 5
        sleep_time = 0.5
        expected_exec_time = cmd_count * sleep_time
        CostSupervisor.reset()
        for _ in range(cmd_count):
            cmd = f"sleep {sleep_time}"
            result = supervised_run(cmd)
            self.assertEqual(result["retcode"], 0)

        summary = CostSupervisor.summarize()
        self.assertEqual(summary["cmd_count"], cmd_count)
        total_exec_time_diff = abs(summary["exec_time"] - expected_exec_time)
        self.assertLessEqual(total_exec_time_diff, 0.1)
        # Waiever this for production environment
        # max_cpu_time_diff = abs(summary["max_end"] - summary["max_begin"] - sleep_time)
        # self.assertLessEqual(max_cpu_time_diff, 0.1)

    def test_progress(self):
        def progress(process, line):
            self.assertEqual(line, "Haha")

        script_path = LOCAL_PATH("test.sh")
        with open(script_path, "w") as wfile:
            wfile.write("for i in {1..5}; do echo Haha; sleep 1; done")
        cmd = f"bash {script_path}"
        supervised_run(cmd, progress_cb=progress)

    def test_interrupt(self):
        def progress(process, line):
            process.send_signal(signal.SIGINT)

        script_path = LOCAL_PATH("test.sh")
        with open(script_path, "w") as wfile:
            wfile.write("for i in {1..5}; do echo Haha; sleep 1; done")

        cmd = f"bash {script_path}"
        result = CostSupervisor.run(cmd, progress_cb=progress)
        self.assertEqual(result["retcode"], -(signal.SIGINT))
