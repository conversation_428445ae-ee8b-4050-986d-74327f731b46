# -*- coding: utf-8 -*-
import os
import settings
import logging
from flows.operations.IOs.Download import Download
from flows.operations.IOs.Upload import Upload
from tests.resources import cpu_evaluation_object
from tests import VXCodeSwarmOperationTestCase
from utils import LOCAL_PATH


class TestHDFS(VXCodeSwarmOperationTestCase):
    LOCAL_FILENAME = "larva_test.mp4"
    LOCAL_PATH = LOCAL_PATH(LOCAL_FILENAME)
    HDFS_FOLDER = f"hdfs://{settings.HDFS_WORKSPACE_ROOT}/larva_test"
    HDFS_TARGET = f"{HDFS_FOLDER}/{LOCAL_FILENAME}"

    def test_down_and_up(self):
        if settings.SWARM_IN_PRODUCT:
            logging.info("start downloading tests")
            Download.run(path=self.LOCAL_PATH, **cpu_evaluation_object)
            logging.info("UPOS Download test passed")
            logging.info("start HDFS uploading test")
            Upload.run(self.LOCAL_PATH, self.HDFS_TARGET)
            logging.info("HDFS uploading test passed")
            logging.info("start HDFS downloading test")
            Download.run(url=self.HDFS_TARGET, path=self.LOCAL_PATH,
                         md5=cpu_evaluation_object["md5"],
                         size=cpu_evaluation_object["size"])
            logging.info("HDFS downloading test passed")
