# -*- coding: utf-8 -*-
import time
import logging
from tests import VXCodeSwarmOperationTestCase
from utils import IPCContext
from utils import init_http_lancer_log

JOB_ID = "vxcode-swarm-vmaf-job"


class TestHTTPLogging(VXCodeSwarmOperationTestCase):

    def test_gossipping(self):
        with IPCContext():
            init_http_lancer_log(JOB_ID)
            logging.info("I am a test message")
            logging.info("I am a test message")
            logging.info("I am a test message")
        with IPCContext():
            time.sleep(3)
            init_http_lancer_log(JOB_ID)
            logging.info("I am a test message")
            logging.info("I am a test message")
            logging.info("I am a test message")
