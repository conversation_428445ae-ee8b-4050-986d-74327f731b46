# -*- coding: utf-8 -*-
import unittest
import settings
from utils import load_job_request, init_local_log, SHOW_SWARM_PROPERTIES

init_local_log()


class VXCodeSwarmFlowTestCase(unittest.TestCase):
    JOB_ID = "swarm-flow-unittest"
    JOB_DATA = {
        "vxcode_id": JOB_ID,
        "callback": settings.VXCODE_HIVE_HOST,
        "request": {"flow": "beep"},
        "resource": {
            "required_cpu": 8000,
            "optional_cpu": 8000,
            "required_mem": 1000,
            "optional_mem": 1000,
        },
        "image": JOB_<PERSON>,
        "version": JOB_ID,
        "route": JOB_<PERSON>,
        "queen": JOB_ID,
    }

    @classmethod
    def setUpClass(cls) -> None:
        cls.JOB_DATA.update({"request": cls.REQUEST_DATA})
        load_job_request(cls.JOB_ID, cls.JOB_DATA)
        SHOW_SWARM_PROPERTIES()


class VXCodeSwarmOperationTestCase(unittest.TestCase):
    JOB_ID = "swarm-operation-unittest"

    @classmethod
    def setUpClass(cls) -> None:
        SHOW_SWARM_PROPERTIES()
