# -*- coding: utf-8 -*-
import gzip
import logging
import requests
import settings
from concurrent.futures import ProcessPoolExecutor


class LancerHttpClient:
    def __init__(self):
        self.line_breaker = '\x03'
        self._cache = list()

    def clear(self):
        self._cache = list()

    def send(self, msg):
        self._cache.append(msg)
        if len(self._cache) >= settings.EDGE_LOG_MAX_CACHE_LINES:
            self._send()

    def flush(self):
        self._call_lancer(self._cache, True)

    def _send(self):
        batch_size = settings.EDGE_LOG_BATCH_SIZE
        worker_count = int(round(len(self._cache) / batch_size))
        with ProcessPoolExecutor(max_workers=worker_count) as executor:
            for idx in range(0, len(self._cache), batch_size):
                logs = self._cache[idx: idx + batch_size]
                executor.submit(self._call_lancer, logs, True)
        self.clear()

    def _call_lancer(self, lines, use_gzip):
        if len(lines):
            headers = dict()
            data = self.line_breaker.join(lines)
            if use_gzip:
                data = bytes(data, 'utf-8')
                data = gzip.compress(data)
                headers["Content-Encoding"] = "gzip"
            for attempt in range(settings.EDGE_LOG_API_RETRIES):
                try:
                    if settings.QBONE_DOMAIN:
                        url = f"http://{settings.QBONE_DOMAIN}/{settings.EDGE_LOG_URI}"
                        headers["Host"] = settings.EDGE_LOG_DOMAIN

                    else:
                        url = f"http://{settings.EDGE_LOG_DOMAIN}/{settings.EDGE_LOG_URI}"
                    resp = requests.post(url,
                                         data=data,
                                         headers=headers,
                                         timeout=settings.EDGE_LOG_API_TIMEOUT)
                    resp.raise_for_status()
                    logging.info(f"Call logs to Lancer Done @attempt {attempt}")
                    return
                except Exception as ex:
                    logging.error(f"Fail to call Lancer due to {ex} @attempt {attempt}")
