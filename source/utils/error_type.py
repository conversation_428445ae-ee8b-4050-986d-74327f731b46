# -*- coding: utf-8 -*-
'''
* <AUTHOR>
* @version [2024/6/25]
* @description [bilivqa错误类型]
* @code [error_type.py]
'''
from utils import BiliVqaVideoError, BiliVqaTensorRTError, BiliVqaGPUError


class BiliVqaFullVideoError(BiliVqaVideoError):
    def __init__(self, message):
        super(BiliVqaFullVideoError, self).__init__()
        self.message = message

    def __str__(self):
        return f"Cann't ffprobe the video! Please check the video. Details: \n<detail> \n{self.message} \n</detail>"


class BiliVqaPartialFrameError(BiliVqaVideoError):
    def __init__(self):
        super(BiliVqaPartialFrameError, self).__init__()

    def __str__(self):
        return "Partial frames error while decoding! Please check the video."


class BiliVqaMetaInfoError(BiliVqaVideoError):
    def __init__(self, missing_info):
        super(BiliVqaMetaInfoError, self).__init__()
        self.missing_info = missing_info

    def __str__(self):
        return f"Missing {self.missing_info} information in video meta info. Please check the video."


class BiliVqaLoadingEngineError(BiliVqaTensorRTError):
    def __init__(self, message):
        super(BiliVqaLoadingEngineError, self).__init__()
        self.message = message

    def __str__(self):
        return f"TensorRT engine loading failed, please confirm the GPU compatibility and the memory! TensorRT information: \n<detail> \n {self.message} \n</detail>"


class BiliVqaGetEngineError(BiliVqaTensorRTError):
    def __init__(self, sm_version):
        super(BiliVqaGetEngineError, self).__init__()
        self.sm_version = sm_version

    def __str__(self):
        return f"The sm version {self.sm_version} is not supported yet!"


class BiliVqaTRTRuntimeError(BiliVqaTensorRTError):
    def __init__(self, message):
        super(BiliVqaTRTRuntimeError, self).__init__()
        self.message = message

    def __str__(self):
        return f"TensorRT runtime error! TensorRT information: '{self.message}'"