import gzip
import time
import atexit
import logging
import requests
from typing import Callable

from utils.lancer.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BLogger

EDGE_LOG_DOMAIN = "dataflow.biliapi.com"
EDGE_LOG_URI = "log/system"


class LancerFormatter(JsonFormatter):

    def __init__(self, template, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.template = template
        self.type_hint = []
        self.tmp_keys = []
        # parse template
        if "|" in template:
            splitted = self.template.split("|")
        else:
            splitted = self.template.split("\u0001")
        for k in splitted:
            key, hint = k.split(":")
            key = key.replace("{", "").replace("}", "")
            self.tmp_keys.append(key)
            self.type_hint.append(hint)
        self.template = "\u0001".join("{" + i + "}" for i in self.tmp_keys)

    def log_to_template(self, log):
        data = {}
        i = 0
        for k in self.tmp_keys:
            cur_hint = self.type_hint[i]
            # Log through hive table will mess up logs' timestamp
            if k == "true_timestamp":
                data[k] = time.time()
            elif cur_hint == "int" or cur_hint == "long" or cur_hint == "short":
                data[k] = log.get(k, 0)
            elif cur_hint == "double" or cur_hint == "float":
                data[k] = log.get(k, 0.0)
            else:
                data[k] = log.get(k, 'unknown')
            i += 1
        return self.template.format(**data)

    def serialize_log_record(self, log_record):
        """Returns the final representation of the log record."""
        return "{level}:{logid}{ts}{log_detail}".format(
            level=log_record["level"],
            logid=self.prefix,
            ts=int(time.time() * 1000),
            log_detail=self.log_to_template(log_record)
        )


class LancerHttpClient(object):
    _cache = list()
    _timestamp = 0
    cache_len = None
    cache_time = 1
    separator: str = "\u0003"
    use_gzip: bool = True
    url = f"http://{EDGE_LOG_DOMAIN}/{EDGE_LOG_URI}"

    def __init__(
            self,
            timeout: float = 0.2,
            retry: int = 3,
            cache_len: int = None,
            cache_time: int = None
    ):
        self.timeout = timeout
        self.retry = retry
        if cache_len:
            LancerHttpClient.cache_len = cache_len
            atexit.register(self.flush)
        if cache_time:
            LancerHttpClient.cache_time = cache_time
            atexit.register(self.flush)

    def send(self, msg) -> None:
        level, msg = msg.split(":", 1)
        self._cache.append(msg)
        if self.cache_len and len(self._cache) >= self.cache_len:
            self.flush()
        elif self.cache_time and time.time() - self._timestamp > LancerHttpClient.cache_time:
            LancerHttpClient._timestamp = time.time()
            self.flush()

    def flush(self) -> None:
        if not self._cache:
            return
        msgs = self.separator.join(self._cache)
        self._cache.clear()
        self._call_lancer(msgs)

    def _call_lancer(self, lines):
        headers = dict()
        if self.use_gzip:
            data = bytes(lines, 'utf-8')
            data = gzip.compress(data)
            headers["Content-Encoding"] = "gzip"
        else:
            data = lines
        for attempt in range(self.retry):
            try:
                resp = requests.post(
                    self.url,
                    data=data,
                    headers=headers,
                    timeout=self.timeout
                )
                resp.raise_for_status()
                return
            except Exception as ex:
                # TODO FIXME maximum recursion depth exceeded via cache_len
                logging.error(ex)


class LancerHttpStream(object):
    def __init__(self, client: LancerHttpClient):
        self.client = client

    def write(self, msg):
        if msg == "\n":
            return
        self.client.send(msg)


def init_lancer_logger(
        name: str,
        log_id: str,
        lancer_fmt: str,
        add_fields: dict = None,
        log_level: int = logging.INFO,
        retry: int = 3,
        timeout: float = 0.2,
        cache_len: int = None,
        cache_time: int = None,
        filter: Callable = None
):
    """
    This func is used in edge logging.
    ref: https://info.bilibili.co/pages/viewpage.action?pageId=94017407

    :param name: logger name
    :param log_id: create in http://berserker.bilibili.co/#/lancer/list
    :param fmt: 'index in berserker'
    :param filter: func of filter record, def xxx(record)
    :param retry: api retry times
    :param timeout: api timeout
    :param cache_len: the max lines of the cache logs
    :param cache_time: the time of cache logs
    EDGE_LOG_DOMAIN = "dataflow.biliapi.com"
    EDGE_LOG_URI = "log/system"
        *note*: logging.error will flush the cache
    """
    fmt = "[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s"
    date_fmt = "%Y-%m-%d %H:%M:%S"
    if add_fields is None:
        add_fields = {}
    c = LancerHttpClient(retry=retry, timeout=timeout, cache_time=cache_time, cache_len=cache_len)
    lstream = LancerHttpStream(client=c)
    formatter = LancerFormatter(lancer_fmt, fmt, date_fmt, prefix=log_id, filter=filter, static_fields=add_fields)
    lancerhandler = logging.StreamHandler(stream=lstream)
    lancerhandler.setFormatter(formatter)
    logger = logging.getLogger(name=name)
    logger.__class__ = BLogger
    logger.setLevel(log_level)
    logger.handlers.append(lancerhandler)
    return logger


def get_lancer_logger(name):
    logger = logging.getLogger(name=name)
    return logger


def log_to_lancer(log, extra):
    logger = get_lancer_logger('http_lancer_logger')
    logger.info(log, extra=extra)


def init_lancer_http_log():
    init_lancer_logger(
        name='http_lancer_logger',
        log_id='021714',
        lancer_fmt='{vxcode_id}:str\u0001{task_id}:str\u0001{buid}:str\u0001'
                   '{image}:str\u0001{version}:str\u0001{queen}:str\u0001'
                   '{request_info}:str\u0001{resource_info}:str\u0001'
                   '{route_info}:str\u0001{cost_info}:str\u0001{platform_info}:str\u0001'
                   '{utime}:str\u0001{status}:str',
        cache_time=10,
    )


if __name__ == '__main__':
    init_lancer_http_log()
    reousrce_report = {
        'vxcode_id': 'test_vxcode_id',
        'task_id': 'test_task_id',
        'buid': 'test_buid',
        'image': 'test_image',
        'version': 'test_version',
        'queen': 'test_queen',
        'request_info': {'aaa': 123, 'bbb': 234},
        'resource_info': {'aaa': 123, 'bbb': 234},
        'route_info': {'aaa': 123, 'bbb': 234},
        'cost_info': {'aaa': 123, 'bbb': 234},
        'platform_info': {'aaa': 123, 'bbb': 234},
        'utime': 'test_utime',
        'status': 'test_status',
    }
    log_to_lancer(f"test http lancer logger: {reousrce_report}", reousrce_report)
