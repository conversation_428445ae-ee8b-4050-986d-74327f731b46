import os
import re
import stat
import pytz
import json
import time
import socket
import logging
import datetime
import traceback
import importlib
from datetime import date, datetime
from logging.handlers import RotatingFileHandler

from typing import Callable, ClassVar
from inspect import istraceback
from collections import OrderedDict

UAT_OPS_LOG_ID = '000069'
PROD_OPS_LOG_ID = '000161'
TIMEZONE = os.getenv('TIMEZONE', 'Asia/Shanghai')
OPS_LOG_SOCKET = '/var/run/lancer/collector.sock'
LANCER_LOG_SOCKET_TCP = '/var/run/lancer/collector_tcp.sock'
LANCER_LOG_SOCKET_UDP = '/var/run/lancer/collector_udp.sock'
# skip natural LogRecord attributes
# http://docs.python.org/library/logging.html#logrecord-attributes
RESERVED_ATTRS = (
    'args', 'asctime', 'created', 'exc_info', 'exc_text', 'filename',
    'funcName', 'levelname', 'levelno', 'msecs', 'message', 'msg',
    'name', 'pathname', 'process', 'processName', 'relativeCreated',
    'stack_info', 'thread', 'threadName')
RESERVED_ATTR_HASH = dict(zip(RESERVED_ATTRS, RESERVED_ATTRS))


def is_socket(fpath):
    return os.path.exists(fpath) and stat.S_ISSOCK(os.stat(fpath).st_mode)


class JsonEncoder(json.JSONEncoder):
    """
    A custom encoder extending the default JSONEncoder
    """

    def default(self, obj):
        if isinstance(obj, (date, datetime, time)):
            return self.format_datetime_obj(obj)

        elif istraceback(obj):
            return ''.join(traceback.format_tb(obj)).strip()

        elif type(obj) == Exception \
                or isinstance(obj, Exception) \
                or type(obj) == type:
            return str(obj)

        try:
            return super(JsonEncoder, self).default(obj)

        except TypeError:
            try:
                return str(obj)

            except Exception:
                return None

    def format_datetime_obj(self, obj):
        return obj.isoformat()


class Formatter(logging.Formatter):
    """override logging.Formatter to use an aware datetime object"""

    def converter(self, timestamp):
        tzinfo = pytz.timezone(TIMEZONE)
        return datetime.now(tz=tzinfo)

    def formatTime(self, record, datefmt=None):
        dt = self.converter(record.created)
        if datefmt:
            s = dt.strftime(datefmt)
        else:
            try:
                s = dt.isoformat(timespec='milliseconds')
            except TypeError:
                s = dt.isoformat()
        return s


class JsonFormatter(Formatter):
    """
    A custom formatter to format logging records as json strings.
    extra values will be formatted as str() if nor supported by
    json default encoder
    """

    def __init__(self, *args, **kwargs):
        """
        :param json_default: a function for encoding non-standard objects
            as outlined in http://docs.python.org/2/library/json.html
        :param json_encoder: optional custom encoder
        :param json_serializer: a :meth:`json.dumps`-compatible callable
            that will be used to serialize the log record.
        :param json_indent: an optional :meth:`json.dumps`-compatible numeric value
            that will be used to customize the indent of the output json.
        :param prefix: an optional string prefix added at the beginning of
            the formatted string
        :param rename_fields: an optional dict, used to rename field names in the output.
            Rename message to @message: {'message': '@message'}
        :param static_fields: an optional dict, used to add fields with static values to all logs
        :param static_fields_fresh: extra will update static fields permanent
        :param json_indent: indent parameter for json.dumps
        :param json_ensure_ascii: ensure_ascii parameter for json.dumps
        :param reserved_attrs: an optional list of fields that will be skipped when
            outputting json log record. Defaults to all log record attributes:
            http://docs.python.org/library/logging.html#logrecord-attributes
        :param timestamp: an optional string/boolean field to add a timestamp when
            outputting the json log record. If string is passed, timestamp will be added
            to log record using string as key. If True boolean is passed, timestamp key
            will be "timestamp". Defaults to False/off.
        """
        self.json_default = self._str_to_fn(kwargs.pop("json_default", None))
        self.json_encoder = self._str_to_fn(kwargs.pop("json_encoder", None))
        self.json_serializer = self._str_to_fn(kwargs.pop("json_serializer", json.dumps))
        self.json_indent = kwargs.pop("json_indent", None)
        self.json_ensure_ascii = kwargs.pop("json_ensure_ascii", True)
        self.prefix = kwargs.pop("prefix", "")
        self.rename_fields = kwargs.pop("rename_fields", {
            "message": "log", "levelname": "level", "asctime": "time"
        })
        self.static_fields = kwargs.pop("static_fields", {})
        self.static_fields_fresh = kwargs.pop("static_fields_fresh", True)
        reserved_attrs = kwargs.pop("reserved_attrs", RESERVED_ATTRS)
        self.reserved_attrs = dict(zip(reserved_attrs, reserved_attrs))
        self.timestamp = kwargs.pop("timestamp", False)
        self.filter = self._str_to_fn(kwargs.pop("filter"))

        super(JsonFormatter, self).__init__(*args, **kwargs)
        if not self.json_encoder and not self.json_default:
            self.json_encoder = JsonEncoder

        self._required_fields = self.parse()
        self._skip_fields = dict(zip(self._required_fields,
                                     self._required_fields))
        self._skip_fields.update(self.reserved_attrs)

    def _str_to_fn(self, fn_as_str):
        """
        If the argument is not a string, return whatever was passed in.
        Parses a string such as package.module.function, imports the module
        and returns the function.
        :param fn_as_str: The string to parse. If not a string, return it.
        """
        if not isinstance(fn_as_str, str):
            return fn_as_str

        path, _, function = fn_as_str.rpartition('.')
        module = importlib.import_module(path)
        return getattr(module, function)

    def merge_record_extra(self, record, target, reserved):
        """
        Merges extra attributes from LogRecord object into target dictionary
        :param record: logging.LogRecord
        :param target: dict to update
        :param reserved: dict or list with reserved keys to skip
        """
        for key, value in record.__dict__.items():
            # this allows to have numeric keys
            if key not in reserved:
                target[key] = value
            if self.static_fields_fresh and key in self.static_fields:
                self.static_fields[key] = value
        return target

    def parse(self):
        """
        Parses format string looking for substitutions
        This method is responsible for returning a list of fields (as strings)
        to include in all log messages.
        """
        standard_formatters = re.compile(r'\((.+?)\)', re.IGNORECASE)
        return standard_formatters.findall(self._fmt)

    def add_fields(self, log_record, record, message_dict):
        """
        Override this method to implement custom logic for adding fields.
        """
        for field in self._required_fields:
            if field in self.rename_fields:
                log_record[self.rename_fields[field]] = record.__dict__.get(field)
            else:
                log_record[field] = record.__dict__.get(field)
        log_record.update(self.static_fields)
        log_record.update(message_dict)
        self.merge_record_extra(record, log_record, self._skip_fields)

        if self.timestamp:
            key = self.timestamp if type(self.timestamp) == str else 'timestamp'
            log_record[key] = datetime.fromtimestamp(record.created, tz=pytz.timezone(TIMEZONE))

    def process_log_record(self, log_record):
        """
        Override this method to implement custom logic
        on the possibly ordered dictionary.
        """
        return log_record

    def jsonify_log_record(self, log_record):
        """Returns a json string of the log record."""
        return self.json_serializer(log_record,
                                    default=self.json_default,
                                    cls=self.json_encoder,
                                    indent=self.json_indent,
                                    ensure_ascii=self.json_ensure_ascii)

    def serialize_log_record(self, log_record):
        """Returns the final representation of the log record."""
        return "%s%s" % (self.prefix, self.jsonify_log_record(log_record))

    def format(self, record):
        """Formats a log record and serializes to json"""
        message_dict = {}
        if isinstance(record.msg, dict):
            message_dict = record.msg
            record.message = None
        else:
            record.message = record.getMessage()
        # only format time if needed
        if "asctime" in self._required_fields:
            record.asctime = self.formatTime(record, self.datefmt)

        # Display formatted exception, but allow overriding it in the
        # user-supplied dict.
        if record.exc_info and not message_dict.get('exc_info'):
            message_dict['exc_info'] = self.formatException(record.exc_info)
        if not message_dict.get('exc_info') and record.exc_text:
            message_dict['exc_info'] = record.exc_text

        try:
            log_record = OrderedDict()
        except NameError:
            log_record = {}

        self.add_fields(log_record, record, message_dict)
        log_record = self.process_log_record(log_record)
        # log_record filter
        if self.filter and not self.filter(log_record):
            return ""
        return self.serialize_log_record(log_record)


class LogAgentStream(object):
    def __init__(self, logid, sock, istcp):
        self.logid = logid
        self.sock = sock
        self.istcp = istcp
        self.client = None

    def connect(self):
        try:
            if self.istcp:
                self.client = socket.socket(socket.AF_UNIX, socket.SOCK_SEQPACKET)
            else:
                self.client = socket.socket(socket.AF_UNIX, socket.SOCK_DGRAM)
            self.client.connect(self.sock)
            self.client.setblocking(0)
        except Exception as e:
            print("Failed to connect to log agent:{}".format(e))
            self.client = None

    def write(self, msg):
        if msg == "\n":
            return
        if not self.client:
            self.connect()
        if self.client:
            msg_send = self.logid + str(int(time.time() * 1000)) + msg
            if self.istcp:
                msg_send += u"\u0001"
            try:
                self.client.send(msg_send.encode())
            except Exception as e:
                print("Failed to send log to log agent:{}".format(e))
                self.client = None


class BLogger(logging.Logger):
    def makeRecord(self, name, level, fn, lno, msg, args, exc_info,
                   func=None, extra=None, sinfo=None):
        """
        A factory method which can be overridden in subclasses to create
        specialized LogRecords.
        """
        rv = logging.LogRecord(name, level, fn, lno, msg, args, exc_info, func,
                               sinfo)
        if extra is not None:
            for key in extra:
                # if (key in ["message", "asctime"]) or (key in rv.__dict__):
                #     raise KeyError("Attempt to overwrite %r in LogRecord" % key)
                rv.__dict__[key] = extra[key]
        return rv


def init_logger(
        log_level=logging.INFO,
        local_file: str = "",
        rotate_file: str = "",
        rotate_size: int = 1024,
        rotate_cnt: int = 1,
        opslog_id: str = None,
        opslog_type: str = None,
        app_id: str = None,
        add_fields: dict = None,
        forbidden: bool = False,
        filter: Callable = None,
        ipc_client: ClassVar = None
):
    """
    :param log_level: logging level
    :param local_file: path of local log file
    :param rotate_file: path to rotation log file
    :param rotate_size: how large one rotation log file is (in bytes)
    :param rotate_cnt: how many rotation file exists while running (actual log files cnt = rotate_cnt + 1)
    :param opslog_id: logid create in http://berserker.bilibili.co/#/lancer/list
    :param opslog_type: [HTTP, TCP, UDP, SOCKET] ,https://info.bilibili.co/pages/viewpage.action?pageId=88175313
    :param app_id: ops-log app_id
    :param add_fields: add init fields
    :param forbidden: forbidden local stdout log
    :param ipc_client: ipc client will forbidden socket log
        **note**
    UAT_OPS_LOG_ID = '000069'
    PROD_OPS_LOG_ID = '000161'
    """
    fmt = "[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s"
    date_fmt = "%Y-%m-%d %H:%M:%S"
    logger = logging.getLogger()
    logger.__class__ = BLogger
    logger.setLevel(log_level)
    logger.handlers = [logging.NullHandler()]
    formatter = Formatter(fmt, date_fmt)
    # log stdout
    if not forbidden:
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        logger.handlers = [handler]
    # log to file
    if local_file:
        file_handler = logging.FileHandler(local_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    if rotate_file:
        rotate_handler = RotatingFileHandler(
            rotate_file,
            maxBytes=rotate_size,
            backupCount=rotate_cnt,
            encoding="utf-8")
        rotate_handler.setFormatter(formatter)
        logger.addHandler(rotate_handler)
    # ipc fifo
    if ipc_client:
        lstream = ipc_client
        logging.info(f"Logger socket detected @ipc_client")
    # socket ops log
    elif opslog_type == "TCP" and is_socket(LANCER_LOG_SOCKET_TCP):
        lstream = LogAgentStream(opslog_id, LANCER_LOG_SOCKET_TCP, True)
        logging.info(f"Logger socket detected @{LANCER_LOG_SOCKET_TCP}")
    elif opslog_type == "UDP" and is_socket(LANCER_LOG_SOCKET_UDP):
        lstream = LogAgentStream(opslog_id, LANCER_LOG_SOCKET_UDP, False)
        logging.info(f"Logger socket detected @{LANCER_LOG_SOCKET_UDP}")
    elif opslog_type and is_socket(OPS_LOG_SOCKET):
        lstream = LogAgentStream(opslog_id, OPS_LOG_SOCKET, False)
        logging.info(f"Logger socket detected @{OPS_LOG_SOCKET}")
    else:
        logging.info("Logger is disabled as no socket detected")
        return logger
    # add default field app_id
    if app_id and isinstance(add_fields, dict):
        add_fields["app_id"] = app_id
    elif app_id:
        add_fields = {"app_id": app_id}
    else:
        add_fields = {}
    formatter = JsonFormatter(fmt=fmt, static_fields=add_fields, filter=filter)
    handler = logging.StreamHandler(stream=lstream)
    handler.setFormatter(formatter)
    logger = logging.getLogger()
    logger.setLevel(log_level)
    logger.addHandler(handler)
    return logger
