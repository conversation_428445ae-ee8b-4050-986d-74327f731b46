# -*- coding: utf-8 -*-
import functools
import os
import re
import sys
import time
import stat
import socket
import signal
import shlex
import select
import hashlib
import logging
import requests
import settings
import ujson as json
import logging.handlers
from logging import Formatter
from urllib.parse import urlparse
from urllib.parse import parse_qs
from urllib.parse import urlencode
from importlib import import_module
from threading import Event
from threading import Lock
from multiprocessing import Queue
from multiprocessing import Process
from subprocess import PIPE
from subprocess import Popen
from subprocess import TimeoutExpired
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from utils.billions_logger.jsonlogger import JsonFormatter
from utils.billions_logger.lancerhandler import LancerStream
from utils.lancer_logger.logger import IPCLogFormatter
from utils.lancer_logger.client import LancerHttpClient
from utils import job
from utils.pyutils.src.log import init_std_log
from utils.pyutils.src.log import ExtraFieldFormatter
from utils.pyutils.src.log import BillionsLoggerV2


OPS_LOG_SOCKET = '/var/run/lancer/collector.sock'

KB = lambda b: b << 10
MB = lambda b: b << 20
GB = lambda b: b << 30


class BiliVqaVideoError(Exception):
    def __init__(self):
        self.should_retry = False

    def __str__(self):
        return "This error is caused by the video."


class BiliVqaTensorRTError(Exception):
    def __init__(self):
        self.should_retry = True

    def __str__(self):
        return "This error is caused by the TensorRT."


class BiliVqaGPUError(Exception):
    def __init__(self):
        self.should_retry = True

    def __str__(self):
        return "This error is caused by the insufficient GPU resources."


class BiliVqaUnknownError(Exception):
    def __init__(self, message):
        self.should_retry = True
        self.message = message

    def __str__(self):
        return f"This error is caused by unknown reasons. Details: \n<detail> \n{self.message} \n</detail>"


class CKLogFormatter(ExtraFieldFormatter):
    def format(self, record):
        setattr(record, 'vxcode_id', job.JOB_VXCODE_ID)
        setattr(record, 'queen', job.JOB_QUEEN)
        setattr(record, 'image_version', job.JOB_VERSION)
        return super().format(record)


def init_ck_log(*, level: str = 'INFO', channel: str = None, app_id: str = None, formatter: logging.Formatter = None):
    channel = channel or 'unix:///var/run/lancer/collector_otel.sock'
    BillionsLoggerV2(channel=channel, level=level, service_name=app_id, otel_formatter=formatter).get_logger()

def init_log(app_id):
    init_std_log()
    if not settings.IS_LOCAL_TEST:
        init_ck_log(app_id=app_id, formatter=CKLogFormatter())

def get_hostname():
    return socket.gethostname()


def get_host_ip():
    ip = os.getenv("HOST_IP", "")
    if ip == "":
        try:
            hostname = get_hostname()
            ip = socket.gethostbyname(hostname)
        except Exception as ex:
            logging.info(f"Get host ip failed due to {ex}")
    return ip


def get_class_name(obj):
    return obj.__class__.__name__


def is_socket(fpath):
    return os.path.exists(OPS_LOG_SOCKET) and \
           stat.S_ISSOCK(os.stat(fpath).st_mode)


def init_local_log(logging_level=logging.INFO):
    fmt = '[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s'
    date_fmt = '%y%m%d %H:%M:%S'
    logging.basicConfig(format=fmt, datefmt=date_fmt, level=logging_level)
    if os.path.exists("/data/log/bili-vxcode"):
        LOGGING_ROOT = f"/data/log/bili-vxcode/{settings.PROJECT_NAME}.log"
        opslogHandler = logging.handlers.RotatingFileHandler(LOGGING_ROOT, maxBytes=MB(100), backupCount=5)
        formatter = Formatter(fmt, date_fmt)
        opslogHandler.setFormatter(formatter)
        logger = logging.getLogger()
        logger.setLevel(logging_level)
        logger.addHandler(opslogHandler)


def init_ops_log(job_id, level=logging.INFO, force_remote=False):
    if is_socket(OPS_LOG_SOCKET) and (not force_remote):
        logging.info(f"OPS-log socket detected @{OPS_LOG_SOCKET}")
        init_udp_lancer_log(job_id, level)
    elif settings.SWARM_IN_PRODUCT:
        logging.info(f"OPS-log through HTTP LogStream")
        init_http_lancer_log(job_id, level)
    else:
        logging.info("OPS-log is disabled as no socket detected")


def init_udp_lancer_log(job_id, level=logging.INFO):
    if settings.OPS_LOG_APP_ID:
        fmt = '[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s'
        opslogHandler = logging.StreamHandler(stream=LancerStream(settings.OPS_LOG_TASK_ID,
                                                                  OPS_LOG_SOCKET))
        opslogformatter = JsonFormatter(fmt=fmt,
                                        additional_fields={
                                            'app_id': settings.OPS_LOG_APP_ID,
                                            "job_id": job_id,
                                            "queen": settings.SWARM_QUEEN
                                        })
        opslogHandler.setFormatter(opslogformatter)
        logger = logging.getLogger()
        logger.setLevel(level)
        logger.addHandler(opslogHandler)
    else:
        logging.info("Please specify OPS_LOG_APP_ID to enable OPS-log")


def init_http_lancer_log(job_id, level=logging.INFO):
    if settings.EDGE_LOG_ID:
        logger = logging.getLogger()
        loghandler = IPCLogHandler()
        formatter = IPCLogFormatter(settings.EDGE_LOG_TEMPLATE,
                                    prefix=settings.EDGE_LOG_ID,
                                    additional_fields={
                                        "job_id": job_id,
                                        "queen": settings.SWARM_QUEEN
                                    })
        loghandler.setFormatter(formatter)
        logger.addHandler(loghandler)
        logger.setLevel(level)
    else:
        logging.info("Please specify EDGE_LOG_ID to enable EDGE-log")


class LancerFormatter(logging.Formatter):
    def __init__(self, fields=(), separator="\u0001", *args, **kwargs):
        super(LancerFormatter, self).__init__(*args, **kwargs)
        self._required_fields = fields
        self._separator = separator

    def format(self, record):
        if isinstance(record.msg, dict):
            msg = self._separator.join([str(record.msg.get(k, "")) for k in self._required_fields])
        else:
            return str(record.msg)
        return msg


class LancerSwarmHandler(logging.StreamHandler):
    terminator = '\u0001'


def init_lancer_log(name, log_id, fields, level=logging.INFO):
    if is_socket(OPS_LOG_SOCKET) and settings.ENV in ("pre", "prod"):
        logging.info("Lancer-Logger socket detected @{}".format(OPS_LOG_SOCKET))
        fmt = '[%(levelname)1.1s %(asctime)s %(module)-16.16s:%(lineno)4d] %(message)s'
        handler = LancerSwarmHandler(stream=LancerStream(log_id, OPS_LOG_SOCKET))
        formatter = LancerFormatter(fields, fmt=fmt)
        handler.setFormatter(formatter)
        logger = logging.getLogger(name=name)
        logger.setLevel(level)
        logger.addHandler(handler)
        logger.propagate = False
    else:
        logging.info("Lancer-Logger is disabled as no socket detected")


def get_lancer_logger(name):
    logger = logging.getLogger(name=name)
    return logger


def hash_md5(msg, raw=False):
    md5 = hashlib.md5()
    md5.update(msg.encode())
    if raw:
        return md5.digest()
    else:
        return md5.hexdigest()


def register_exit_handler(signal_handler):
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGQUIT, signal_handler)


class Serializable:
    @property
    def data(self):
        return self.__dict__

    @property
    def serialization(self):
        return json.dumps(self.__dict__, sort_keys=True)

    @property
    def signature(self):
        return hash_md5(self.serialization)

    def __str__(self):
        return json.dumps(self.__dict__, sort_keys=True, indent=4)

    def __repr__(self):
        return json.dumps(self.__dict__, sort_keys=True, indent=4)


class VXCodeKnownError(Exception):
    def __init__(self):
        self.err_dict = {
            'error_type': get_class_name(self),
            'error_details': dict(),
            'should_retry': False,
            'reporter': get_hostname(),
            "swarm_queen": settings.SWARM_QUEEN,
            'swarm_ip': get_host_ip(),
            "vxcode_id": job.JOB_VXCODE_ID,
        }

    def __str__(self):
        return str(self.err_dict)

    def __repr__(self):
        return repr(self.err_dict)


def generate_error_ctx(ex):
    if isinstance(ex, VXCodeKnownError):
        error_ctx = ex.err_dict
    elif isinstance(ex, (BiliVqaTensorRTError, BiliVqaVideoError, BiliVqaUnknownError)):
        error_ctx = {
            'error_type': get_class_name(ex),
            'error_details': {
                'msg': str(ex),
            },
            'should_retry': ex.should_retry,
            'reporter': get_hostname(),
            "swarm_queen": settings.SWARM_QUEEN,
            'swarm_ip': get_host_ip(),
            "vxcode_id": job.JOB_VXCODE_ID,
        }
    else:
        general_ex = BiliVqaUnknownError(str(ex))
        error_ctx = {
            'error_type': get_class_name(general_ex),
            'error_details': {
                'msg': str(general_ex),
            },
            'should_retry': True,
            'reporter': get_hostname(),
            "swarm_queen": settings.SWARM_QUEEN,
            'swarm_ip': get_host_ip(),
            "vxcode_id": job.JOB_VXCODE_ID,
        }
    error_ctx['error_source'] = 'vqa_model'
    return error_ctx


class ExecutionError(VXCodeKnownError):
    MAX_LOG_SIZE = KB(2)

    def __init__(self, result):
        super(ExecutionError, self).__init__()
        result.update({
            "stdout": result["stdout"][:self.MAX_LOG_SIZE],
            "stderr": result["stderr"][:self.MAX_LOG_SIZE],
        })
        self.err_dict['error_details'].update(result)
        retcode = result["retcode"]
        if retcode == CostSupervisor.RET_TIMEOUT:
            self.err_dict['should_retry'] = True
        else:
            self.err_dict['should_retry'] = retcode < 0


class JobDataUnavailable(VXCodeKnownError):
    def __init__(self, job_id):
        super(JobDataUnavailable, self).__init__()
        self.err_dict.update({
            "vxcode_id": job_id,
            "queen": settings.SWARM_QUEEN,
            "instance": settings.SWARM_INSTANCE_NAME,
            "pod": settings.POD_NAME,
        })

class VQAExecutionError(VXCodeKnownError):
    def __init__(self, error: str):
        super(VQAExecutionError, self).__init__()
        self.err_dict['error_details'].update({
            "msg": error,
        })
        self.err_dict['should_retry'] = True


def checksum_md5(path, chunksize=2 ** 12):
    if settings.VXCODE_DRY_RUN:
        return "DRY_RUN_MD5"
    result = CostSupervisor.run(f"md5sum {path}")
    md5 = result["stdout"].split(' ', 1)[0].strip()
    if result["retcode"]:
        md5 = hashlib.md5()
        with open(path, 'rb') as f:
            for chunk in iter(lambda: f.read(chunksize), b''):
                md5.update(chunk)
        return md5.hexdigest()
    else:
        return md5


def supervised_run(cmd, timeout=None, progress_cb=None):
    result = CostSupervisor.run(cmd, timeout, progress_cb)
    if result["retcode"]:
        raise ExecutionError(result)
    return result


def batch_run(fn, *args_batch):
    worker_count = len(args_batch[0])
    with ThreadPoolExecutor(max_workers=worker_count) as executor:
        return list(executor.map(fn, *args_batch))


def move_file(src, dst):
    return supervised_run(f"mv {src} {dst}")


def copy_file(src, dst):
    return supervised_run(f"cp -rf {src} {dst}")


def link_file(src, dst):
    return supervised_run(f"ln -s {src} {dst}")


def sign_dict(data):
    return hash_md5(json.dumps(data, sort_keys=True))


def dict_difference(target, reference):
    target_type = type(target)
    reference_type = type(reference)
    if target_type != reference_type:
        return "Type mismatch: {} vs {}".format(target_type, reference_type)
    else:
        if target_type == dict:
            target_keys = set(target)
            reference_keys = set(reference)
            if target_keys != reference_keys:
                return "Dict key mismatch: target_only {} vs reference only {}".format(
                    target_keys - reference_keys,
                    reference_keys - target_keys
                )
            else:
                for k in target_keys:
                    target_value = target[k]
                    reference_value = reference[k]
                    difference = dict_difference(target_value, reference_value)
                    if difference:
                        return {"KEY_{}".format(k): difference}

        elif target_type == list:
            target_length = len(target)
            reference_length = len(reference)
            if target_length != reference_length:
                return "List length mismatch: target {} vs reference {}".format(target_length, reference_length)
            else:
                for i in range(target_length):
                    target_value = target[i]
                    reference_value = reference[i]
                    difference = dict_difference(target_value, reference_value)
                    if difference:
                        return {"IDX_{}".format(i): difference}

        else:
            if target != reference:
                return "Value mismatch: {} vs {}".format(target, reference)


def ensure_local_path(fpath):
    if os.path.isfile(fpath) or os.path.islink(fpath):
        os.unlink(fpath)
    supervised_run(f"mkdir -p {fpath}")


def url_add_params(url, params):
    pr = urlparse(url)
    query_dict = parse_qs(pr.query)
    query_dict.update(params)
    query = urlencode(query_dict, doseq=True)
    new_url = pr._replace(query=query).geturl()
    return new_url


def get_max_container_memory_used():
    try:
        with open("/sys/fs/cgroup/memory/memory.max_usage_in_bytes", "r") as rfile:
            return int(rfile.read())
    except FileNotFoundError:
        return 0


def CLIP(val, below, upper):
    tmp = max(val, below)
    return min(tmp, upper)


def LOCAL_PATH(filename):
    return os.path.join(settings.LOCAL_WORKSPACE_ROOT, filename)


def generate_curl_cmd(method, url, **kwargs):
    params = kwargs.get("params", None)
    if params:
        url += ("?" + urlencode(params))
    payload = kwargs.get("json", "")
    if payload:
        payload = f"-d '{json.dumps(payload)}'"
    else:
        payload = kwargs.get("data", "")
        if payload:
            payload = f"-d '{payload}'"

    headers = kwargs.get("headers", "")
    if headers:
        headers = " ".join([f"-H \"{k}:{v}\"" for k, v in headers.items()])
    return f"curl -X {method.upper()} \"{url}\" {payload} {headers}"


def load_job_request(job_id, job_data=None):
    if not job_data:
        job_data = VXCodeHive.get_job_data(job_id)
    if not job_data:
        raise JobDataUnavailable(job_id)

    request = job_data["request"]
    request['task_id'] = job_data['task_id']
    request['buid'] = job_data['buid']

    module = import_module(f"utils.job")
    setattr(module, "JOB_VXCODE_ID", job_id)
    setattr(module, "JOB_CALLBACK", job_data["callback"])
    setattr(module, "JOB_REQUEST", job_data["request"])
    setattr(module, "JOB_RESOURCE", job_data["resource"])
    setattr(module, "JOB_IMAGE", job_data["image"])
    setattr(module, "JOB_VERSION", job_data["version"])
    setattr(module, "JOB_ROUTE", job_data["route"])
    setattr(module, "JOB_QUEEN", job_data["queen"])
    job_resource = job_data["resource"]
    required_cpu = job_resource["required_cpu"] // 1000
    optional_cpu = max(job_resource["required_cpu"], job_resource.get("optional_cpu", 0)) // 1000
    required_mem = job_resource["required_mem"]
    required_gpu = job_resource.get("required_gpu", 0)
    setattr(module, "JOB_REQUIRED_CPU", required_cpu)
    setattr(module, "JOB_OPTIONAL_CPU", optional_cpu)
    setattr(module, "JOB_REQUIRED_MEN", required_mem)
    setattr(module, "JOB_REQUIRED_GPU", required_gpu)


def SHOW_SWARM_PROPERTIES():
    logging.info("-" * 40)
    logging.info(f"Under Environment:\t{settings.ENV}")
    logging.info("-" * 40)
    for k, v in vars(settings).items():
        if k.startswith("SWARM_"):
            logging.info(f"{k}:\t{v}")
    logging.info(f"Project\t@{settings.PROJECT_ROOT}")
    logging.info(f"Workspace\t@{settings.LOCAL_WORKSPACE_ROOT}")
    logging.info("-" * 40)


def SHOW_JOB_REQUESTS():
    logging.info(f"Receive Job: {job.JOB_VXCODE_ID}")
    logging.info("-" * 40)
    for k, v in vars(job).items():
        if k.startswith("JOB_"):
            logging.info(f"{k}\t{v}")
    logging.info("=" * 40)


class VXCodeHive:
    HOSTNAME = settings.VXCODE_HIVE_HOST

    @staticmethod
    def _create_sign(params, payload):
        payload = payload if payload else {}
        params = params if params else {}
        body_sign = sign_dict(payload)
        params_sign = sign_dict(params)
        return hash_md5(body_sign + settings.VXCODE_HIVE_TOKEN + params_sign)

    @staticmethod
    def _create_sign_v2(params, payload):
        params = params if params else {}
        params_token = token_from_data(params)

        payload = payload if payload else {}
        payload_token = token_from_data(payload)

        user_meta = dict(appid=settings.OPS_LOG_APP_ID)
        user_token = token_from_data(user_meta)
        return hash_md5(params_token + payload_token + user_token + settings.VXCODE_HIVE_TOKEN)

    @classmethod
    def _call_hive(cls, method, uri, **request_kwargs):
        attempt = 0
        request_headers = dict()
        if not settings.IS_LOCAL_TEST:
            params = request_kwargs.get("params", None)
            payload = request_kwargs.get('json', None)
            if settings.VXCODE_HIVE_V2_ENABLE:
                token = cls._create_sign_v2(params, payload)
                request_headers["X-Authorization-Token"] = token
                request_headers['x1-bilispy-user'] = settings.OPS_LOG_APP_ID

            else:
                token = cls._create_sign(params, payload)
                request_headers["X-Authorization-Token"] = token

        # If Quic Protocol available, use quic to fire HTTP calls
        if settings.QBONE_DOMAIN:
            url = f"{settings.QBONE_DOMAIN}/{uri}"
            request_headers["Host"] = cls.HOSTNAME[len("http://"):]
        else:
            url = f"{cls.HOSTNAME}/{uri}"

        if request_headers:
            request_kwargs["headers"] = request_headers
        request_kwargs["timeout"] = settings.VXCODE_HIVE_TIMEOUT
        while True:
            try:
                resp = requests.request(method, url, **request_kwargs)
                resp.raise_for_status()
                resp_dict = resp.json()
                assert not resp_dict["code"], f"Invalid Hive Data: {resp_dict['error']}"
                return resp_dict["data"]
            except Exception as ex:
                logging.error(f"Fail to access VXCode-Hive @attemp {attempt} due to {ex}")
                if attempt < settings.CALLBACK_RETRIES:
                    attempt += 1
                else:
                    logging.error(f"Give up access VXCode-Hive")
                    curl_cmd = generate_curl_cmd(method, url, **request_kwargs)
                    logging.info(f"Please retry with: {curl_cmd}")
                    return

    @classmethod
    def get_job_data(cls, vxcode_id):
        method = "get"
        params = {"id": vxcode_id}
        request_kwargs = {"params": params}
        data = cls._call_hive(method, "api/v1/job", **request_kwargs)
        logging.info(f"Job [{vxcode_id}] data loaded")
        return data

    @classmethod
    def report_summary(cls, vxcode_id, summary_data):
        if settings.VXCODE_DRY_RUN:
            logging.debug("Skip summary report in dry-run mode")
        else:
            method = "post"
            summary_data["vxcode_id"] = vxcode_id
            request_kwargs = {"json": summary_data}

            result = cls._call_hive(method, "api/v1/job/summary", **request_kwargs)
            if result:
                logging.info(f"Job [{vxcode_id}] summary discarded")
            else:
                logging.info(f"Job [{vxcode_id}] summary reported")

    @classmethod
    def report_event(cls, **kwargs):
        if settings.VXCODE_DRY_RUN:
            logging.debug("Skip event report in dry-run mode")
        else:
            method = "post"
            payload = {
                "vxcode_id": kwargs["vxcode_id"],
                "cmd": kwargs["cmd"],
                "program": kwargs["program"],
                "event": kwargs["event"],
                "uts": time.time(),
            }
            request_kwargs = {"json": payload}
            cls._call_hive(method, "api/v1/job/events", **request_kwargs)

    @classmethod
    def relay_callback(cls, callback_report):
        if settings.VXCODE_DRY_RUN:
            logging.debug("Skip event report in dry-run mode")
        else:
            method = "post"
            payload = {
                "vxcode_id": job.JOB_VXCODE_ID,
                "report": callback_report
            }
            request_kwargs = {"json": payload}
            cls._call_hive(method, "api/v1/job/notify", **request_kwargs)

    @classmethod
    def retrieve_quality_trace(cls, vxcode_id):
        method = "get"
        params = {"id": vxcode_id}
        request_kwargs = {"params": params}
        data = cls._call_hive(method, "api/v1/traces/venc_quality", **request_kwargs)
        return data


class VXCodeHiveTrace(VXCodeHive):
    HOSTNAME = settings.VXCODE_HIVE_TRACE_HOST

    @classmethod
    def report_quality_trace(cls, **kwargs):
        if settings.VXCODE_DRY_RUN:
            logging.debug("Skip quality tracing report in dry-run mode")
        else:
            method = "post"
            request_kwargs = {"json": kwargs}
            cls._call_hive(method, "api/v1/traces/venc_quality", **request_kwargs)


def dump_object(obj) -> str:
    if obj is True:
        return 'true'
    if obj is False:
        return 'false'
    if obj is None:
        return 'null'
    if isinstance(obj, int):
        return str(obj)
    if isinstance(obj, float):
        return str(int(obj))
    if isinstance(obj, dict):
        items = []
        keys = list(obj.keys())
        keys.sort(key=lambda x: x)
        for k in keys:
            v = obj[k]
            items.append("{}={}".format(k, dump_object(v)))
        s = ','.join(items)
        s = '{' + s + '}'
        return s
    if isinstance(obj, list):
        items = []
        for item in obj:
            items.append(dump_object(item))
        s = ','.join(items)
        s = '[' + s + ']'
        return s

    return str(obj)


def token_from_data(data) -> str:
    content = dump_object(data)
    return hash_md5(content)


class IPCServer:
    ExitEvent = Event()
    LogClient = LancerHttpClient()
    CompareLancerLogger = get_lancer_logger("compare_lancer_logger")
    QualityLancerLogger = get_lancer_logger("quality_lancer_logger")
    ResourceLancerLogger = get_lancer_logger("resource_lancer_logger")

    @classmethod
    def main(cls, msg_queue):
        register_exit_handler(cls.quit)
        logging.info(f"IPCServer starts...")
        with ThreadPoolExecutor(max_workers=4) as executor:
            while not cls.ExitEvent.is_set():
                try:
                    message = msg_queue.get()
                    logging.debug(f"IPCServer receives message: {message}")
                    func_name = message["func"].lower()
                    if func_name == "stop":
                        cls.stop()
                    else:
                        kwargs = message.get("kwargs", {})
                        func = getattr(cls, func_name, None)
                        if func:
                            if func_name == "_report_compare":
                                func = functools.partial(func, logger=cls.CompareLancerLogger)
                            elif func_name == "_report_quality":
                                func = functools.partial(func, logger=cls.QualityLancerLogger)
                            elif func_name == "_report_resource":
                                func = functools.partial(func, logger=cls.ResourceLancerLogger)
                            executor.submit(func, **kwargs)
                except Exception as ex:
                    logging.error(f"Exceptional IPCServer call: {ex}")

    @classmethod
    def stop(cls):
        cls.ExitEvent.set()
        cls.LogClient.flush()

    @classmethod
    def quit(cls, signum, stack):
        logging.info(f"IPCServer signaled by signal: {signum}")
        cls.stop()

    @staticmethod
    def _report_event(**kwargs):
        VXCodeHiveTrace.report_event(**kwargs)

    def _send_logging(self, **kwargs):
        self.LogClient.send(**kwargs)

    @staticmethod
    def _report_quality_trace(**kwargs):
        VXCodeHiveTrace.report_quality_trace(**kwargs)

    @staticmethod
    def _report_compare(logger, **kwargs):
        logger.info(kwargs)

    @staticmethod
    def _report_quality(logger, **kwargs):
        logger.info(kwargs)

    @staticmethod
    def _report_resource(logger, **kwargs):
        logger.info(kwargs)


class IPCClient:
    IPCQueue = Queue()
    Server = None

    @classmethod
    def report_event(cls, **kwargs):
        return cls._call_server("_report_event", **kwargs)

    @classmethod
    def send_logging(cls, **kwargs):
        return cls._call_server("_send_logging", **kwargs)

    @classmethod
    def report_quality_trace(cls, **kwargs):
        return cls._call_server("_report_quality_trace", **kwargs)

    @classmethod
    def report_compare(cls, **kwargs):
        return cls._call_server("_report_compare", **kwargs)

    @classmethod
    def report_quality(cls, **kwargs):
        return cls._call_server("_report_quality", **kwargs)

    @classmethod
    def report_resource(cls, **kwargs):
        return cls._call_server("_report_resource", **kwargs)

    @classmethod
    def stop_server(cls):
        cls._call_server("stop")
        logging.info(f"IPCClient stops server with message")
        if cls.Server:
            logging.info(f"IPCClient waits for server's elegant exit")
            cls.Server.join(settings.IPC_EXIT_CLIENT_TIMEOUT)
            if not cls.Server.is_alive():
                logging.info(f"IPCClient exits after server's message exit")
                return
            cls.Server.terminate()
            logging.error(f"IPCClient stops server with SIGTERM")
            cls.Server.join(settings.IPC_EXIT_CLIENT_TIMEOUT)
            if not cls.Server.is_alive():
                logging.info(f"IPCClient exits after server's SIGTERM exit")
                return
            cls.Server.kill()
            logging.error(f"IPCClient stops server with SIGKILL")
            cls.Server.join(settings.IPC_EXIT_CLIENT_TIMEOUT)
            if cls.Server.is_alive():
                logging.error(f"IPCClient waits no longer")
                sys.exit(-1)
            else:
                logging.info(f"IPCClient exits after server's SIGKILL exit")

    @classmethod
    def start_server(cls):
        cls.Server = Process(target=IPCServer.main, args=(cls.IPCQueue,))
        cls.Server.start()

    @classmethod
    def _call_server(cls, func, **kwargs):
        message = {
            "func": func,
            "kwargs": kwargs
        }
        cls.send(message)

    @classmethod
    def send(cls, message):
        logging.debug(f"IPCClient sends message: {message}")
        cls.IPCQueue.put(message)

    def __enter__(self):
        self.start_server()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop_server()


IPCContext = IPCClient


class IPCLogHandler(logging.Handler):

    def emit(self, record):
        msg = self.format(record)
        try:
            IPCClient.send_logging(msg=msg)
        except Exception as ex:
            logging.error(f"Fail to send message due to {ex}")


class IPCEventReporter:
    EVENT_START = "start"
    EVENT_END = "end"

    @staticmethod
    def extract_program_name(cmd):
        cmd_split = cmd.split(" ", 1)
        if len(cmd_split) == 1:
            return cmd
        else:
            program, args = cmd_split
            program = os.path.basename(program)
            if "python" in program:
                program = args.split(" ", 1)[0]
                program = os.path.basename(program)
            return program

    @classmethod
    def __report(cls, cmd, event):
        program_name = cls.extract_program_name(cmd)
        if program_name not in settings.SKIP_EVENT_PROGRAMS:
            event_data = {
                "vxcode_id": job.JOB_VXCODE_ID,
                "program": program_name,
                "cmd": cmd,
                "event": event
            }
            IPCClient.report_event(**event_data)

    @classmethod
    def start_cmd(cls, cmd):
        cls.__report(cmd, cls.EVENT_START)

    @classmethod
    def end_cmd(cls, cmd):
        cls.__report(cmd, cls.EVENT_END)


class CostSupervisor:
    RET_TIMEOUT = "Timeout"
    DataLock = Lock()
    CmdCount = 0

    CPUTimeInMS = 0
    BeginTime = 2 * (time.time())
    EndTime = 0
    MaxMillCPU = 0
    MaxBegin = 0
    MaxEnd = 0

    @classmethod
    def run(cls, cmd, timeout=None, progress_cb=None, max_log_lines=1000):
        logging.info(f"Exec: {cmd}")
        IPCEventReporter.start_cmd(cmd)
        exec_result = {"cmd": cmd}
        process = Popen(shlex.split(cmd),
                        shell=False,
                        stdin=None, stdout=PIPE, stderr=PIPE,
                        errors="replace",
                        encoding="utf-8",
                        universal_newlines=True)
        try:
            with PerfProfiler(process.pid) as domain:
                if progress_cb:
                    exec_result.update(cls._manage_with_progress(process, timeout, progress_cb, max_log_lines))
                else:
                    exec_result.update(cls._manage_without_progress(process, timeout))
                return exec_result
        finally:
            IPCEventReporter.end_cmd(cmd)
            logging.debug(f"Complete: {cmd}")
            cls.merge_cost(domain.latest_cost)

    @classmethod
    def summarize(cls):
        return {"supervised": settings.SWARM_PERF_ENABLED, "cmd_count": cls.CmdCount,
                "exec_time": cls.EndTime - cls.BeginTime, "begin_time": cls.BeginTime, "end_time": cls.EndTime,
                "max_begin": cls.MaxBegin, "max_end": cls.MaxEnd, "max_memory": get_max_container_memory_used(),
                "max_cpu": cls.MaxMillCPU / 1000. if cls.MaxMillCPU is not None else None,
                "cpu_time": cls.CPUTimeInMS / 1000. if cls.CPUTimeInMS is not None else None}

    @classmethod
    def reset(cls):
        cls.CmdCount = 0
        cls.CPUTimeInMS = 0
        cls.BeginTime = 2 * (time.time())
        cls.EndTime = 0
        cls.MaxMillCPU = 0
        cls.MaxBegin = 0
        cls.MaxEnd = 0

    @classmethod
    def _manage_without_progress(cls, process, timeout):
        exec_timeout = False
        try:
            stdout, stderr = process.communicate(timeout=timeout)
        except TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
            exec_timeout = True
        except:  # Including KeyboardInterrupt, communicate handled that.
            process.kill()
            raise
        finally:
            retcode = cls.RET_TIMEOUT if exec_timeout else process.poll()

        return {
            "retcode": retcode,
            "timeout": timeout,
            "stdout": stdout,
            "stderr": stderr,
        }

    @classmethod
    def _manage_with_progress(cls, process, timeout, progress_cb, max_log_lines):
        stderr_lines = deque(maxlen=max_log_lines)
        stdout_lines = deque(maxlen=max_log_lines)
        log_mapper = {
            process.stdout: stdout_lines,
            process.stderr: stderr_lines,
        }
        exec_timeout = False
        try:
            begin_time = time.time()
            while process.poll() is None:
                (rpipes, _, _) = select.select([process.stdout, process.stderr], [], [], 2)

                for pipe in rpipes:
                    log_line = pipe.readline().strip()
                    if log_line:
                        log_mapper[pipe].append(log_line)
                        cls._notify_progress(progress_cb, process, log_line)
                if (timeout is not None) and (time.time() > begin_time + timeout):
                    process.kill()
                    exec_timeout = True
        except:
            process.kill()
            raise
        finally:
            # We do not need to call wait() as __exit__ will do that
            retcode = cls.RET_TIMEOUT if exec_timeout else process.wait()
            stdout, stderr = process.communicate()
            stdout_lines.append(stdout)
            stderr_lines.append(stderr)

        return {
            "retcode": retcode,
            "timeout": timeout,
            "stdout": os.linesep.join(stdout_lines),
            "stderr": os.linesep.join(stderr_lines),
        }

    @classmethod
    def _notify_progress(cls, progress_cb, process, content):
        try:
            progress_cb(process, content)
        except Exception as ex:
            logging.exception(f"Fail to execute progress callback due to {ex}")

    @classmethod
    def merge_cost(cls, cost):
        with cls.DataLock:
            begin_time = cost["begin_time"]
            end_time = cost["end_time"]
            exec_time = end_time - begin_time
            cpu_time_in_ms = cost["cpu_time_in_ms"]
            cls.CmdCount += 1
            cls.BeginTime = min(cls.BeginTime, begin_time)
            cls.EndTime = max(cls.EndTime, end_time)

            if cpu_time_in_ms is not None:
                cls.CPUTimeInMS += cpu_time_in_ms
                used_mcpu = cpu_time_in_ms / exec_time
                latter_begin = max(cls.MaxBegin, begin_time)
                earlier_end = min(cls.MaxEnd, end_time)
                overlapped = latter_begin < earlier_end
                if overlapped:
                    cls.MaxMillCPU += used_mcpu
                    cls.MaxBegin = latter_begin
                    cls.MaxEnd = earlier_end
                else:
                    if used_mcpu > cls.MaxMillCPU:
                        cls.MaxMillCPU = used_mcpu
                        cls.MaxBegin = begin_time
                        cls.MaxEnd = end_time
            else:
                cls.CPUTimeInMS = None
                cls.MaxMillCPU = None
                cls.MaxBegin = cls.BeginTime
                cls.MaxEnd = cls.EndTime


class PerfProfiler:
    Supervised = settings.SWARM_PERF_ENABLED

    def __init__(self, pid=os.getpid()):
        self.begin_time = None
        self.end_time = None
        self.pid = pid
        self.latest_cost = None
        self.process = None
        self.perf_report_path = None

    def __enter__(self):
        self.begin_time = time.time()
        if settings.SWARM_PERF_ENABLED:
            perf_report_filename = f"perf_report_{self.pid}_{int(time.time())}.txt"
            self.perf_report_path = f"/tmp/{perf_report_filename}"
            perf_cmd = f"{settings.PERF_STAT_GENERAL_CMD} -p {self.pid} -o {self.perf_report_path}"
            self.process = Popen(shlex.split(perf_cmd))
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        if settings.SWARM_PERF_ENABLED and self.process:
            try:
                self.process.send_signal(signal.SIGINT)
                self.process.wait(settings.PERF_TRACE_TIMEOUT)
            except TimeoutExpired:
                logging.error("Fail to stop perf tracing, just keep ahead")
                self.process.terminate()
        self.latest_cost = self._parse_cost_from_report(self.perf_report_path)
        self.latest_cost.update({
            "begin_time": self.begin_time,
            "end_time": self.end_time,
            "exec_time": self.end_time - self.begin_time
        })

    @classmethod
    def _parse_cost_from_report(cls, perf_report_path):
        if not settings.SWARM_PERF_ENABLED:
            cpu_clock_in_ms = None
        else:
            cpu_clock_in_ms = 0
            if os.path.exists(perf_report_path):
                cpu_clock_seen = False
                with open(perf_report_path, "r") as rfile:
                    for line in rfile.readlines():
                        if not cpu_clock_seen:
                            cpu_clock_seen, cpu_clock_in_ms = cls._parse_cpu_clock(line)
                        else:
                            break
        return {
            "cpu_time_in_ms": cpu_clock_in_ms,
        }

    @staticmethod
    def _parse_cpu_clock(line):
        cpu_clock_in_ms = 0
        cpu_clock_seen = "cpu-clock" in line
        if cpu_clock_seen:
            matched = re.search(r"([0-9\.,]+)\s+.*\scpu-clock", line)
            if matched:
                cpu_clock_in_ms = float(matched.group(1).replace(",", ""))
        return cpu_clock_seen, cpu_clock_in_ms
